-- Migration Script from old schema (script_data.sql) to new schema (improved_schema.sql)
-- This script will migrate data from the old database structure to the new one

USE [InventoryManagement]
GO

-- Note: SQL Server only allows IDENTITY_INSERT to be ON for one table at a time
-- We'll turn it ON and OFF for each table as we migrate data

-- =============================================
-- STEP 1: Migrate Core Reference Data
-- =============================================

-- Migrate Departments
SET IDENTITY_INSERT [dbo].[Department] ON;
INSERT INTO [dbo].[Department] (
    [DepartmentId], [Name], [CreatedAt], [IsActive]
)
SELECT
    [Depart_Id],
    [Depart_Name],
    GETDATE(),
    1
FROM [Salacia].[dbo].[DepartmentTbl];
SET IDENTITY_INSERT [dbo].[Department] OFF;
GO

-- Migrate Product Groups
SET IDENTITY_INSERT [dbo].[ProductGroup] ON;
INSERT INTO [dbo].[ProductGroup] (
    [GroupId], [Name], [DepartmentId], [CreatedAt], [IsActive]
)
SELECT
    [Group_Id],
    [Group_Name],
    [Depart_Id],
    GETDATE(),
    1
FROM [Salacia].[dbo].[GroupsTbl];
SET IDENTITY_INSERT [dbo].[ProductGroup] OFF;
GO

-- Migrate Product SubGroups
SET IDENTITY_INSERT [dbo].[ProductSubGroup] ON;
INSERT INTO [dbo].[ProductSubGroup] (
    [SubGroupId], [Name], [GroupId], [CreatedAt], [IsActive]
)
SELECT
    [SubGroup_Id],
    [SubGroup_Name],
    [Group_Id],
    GETDATE(),
    1
FROM [Salacia].[dbo].[SubGroup];
SET IDENTITY_INSERT [dbo].[ProductSubGroup] OFF;
GO

-- Migrate Units (create a temporary mapping table for unit groups)
CREATE TABLE #UnitMapping (
    [OldUnitId] INT,
    [OldUnitName] NVARCHAR(150),
    [NewUnitId] INT,
    [UnitGroupId] INT
);

INSERT INTO [dbo].[Unit] (
    [Name], [Abbreviation], [BaseConversionFactor], [CreatedAt], [IsActive]
)
SELECT DISTINCT
    [Unt_Name],
    LEFT([Unt_Name], 3),
    1,
    GETDATE(),
    1
FROM [Salacia].[dbo].[ProductsTbl]
WHERE [Unt_Name] IS NOT NULL;

-- Populate the unit mapping table
INSERT INTO #UnitMapping (
    [OldUnitId], [OldUnitName], [NewUnitId], [UnitGroupId]
)
SELECT
    p.[Unt_Id],
    p.[Unt_Name],
    u.[UnitId],
    p.[Unt_GroupId]
FROM [Salacia].[dbo].[ProductsTbl] p
JOIN [dbo].[Unit] u ON p.[Unt_Name] = u.[Name]
WHERE p.[Unt_Name] IS NOT NULL
GROUP BY p.[Unt_Id], p.[Unt_Name], u.[UnitId], p.[Unt_GroupId];

-- Migrate Brands
INSERT INTO [dbo].[Brand] (
    [Name], [CreatedAt], [IsActive]
)
SELECT DISTINCT
    [Product_BrandName],
    GETDATE(),
    1
FROM [Salacia].[dbo].[ProductsTbl]
WHERE [Product_BrandName] IS NOT NULL;

-- Create a brand mapping table
CREATE TABLE #BrandMapping (
    [OldBrandId] INT,
    [OldBrandName] NVARCHAR(150),
    [NewBrandId] INT
);

INSERT INTO #BrandMapping (
    [OldBrandId], [OldBrandName], [NewBrandId]
)
SELECT
    p.[Product_BrandId],
    p.[Product_BrandName],
    b.[BrandId]
FROM [Salacia].[dbo].[ProductsTbl] p
JOIN [dbo].[Brand] b ON p.[Product_BrandName] = b.[Name]
WHERE p.[Product_BrandName] IS NOT NULL
GROUP BY p.[Product_BrandId], p.[Product_BrandName], b.[BrandId];

-- Migrate Locations
INSERT INTO [dbo].[Location] (
    [Name], [CreatedAt], [IsActive]
)
SELECT DISTINCT
    [Location_Name],
    GETDATE(),
    1
FROM [Salacia].[dbo].[StoresTbl]
WHERE [Location_Name] IS NOT NULL;

-- Create a location mapping table
CREATE TABLE #LocationMapping (
    [OldLocationId] INT,
    [OldLocationName] NVARCHAR(150),
    [NewLocationId] INT
);

INSERT INTO #LocationMapping (
    [OldLocationId], [OldLocationName], [NewLocationId]
)
SELECT
    s.[Location_Id],
    s.[Location_Name],
    l.[LocationId]
FROM [Salacia].[dbo].[StoresTbl] s
JOIN [dbo].[Location] l ON s.[Location_Name] = l.[Name]
WHERE s.[Location_Name] IS NOT NULL
GROUP BY s.[Location_Id], s.[Location_Name], l.[LocationId];

-- Migrate Stores
SET IDENTITY_INSERT [dbo].[Store] ON;
INSERT INTO [dbo].[Store] (
    [StoreId], [Name], [LocationId], [IsSalesPoint], [CreatedAt], [IsActive]
)
SELECT
    s.[Store_id],
    s.[Store_Name],
    lm.[NewLocationId],
    ISNULL(s.[issales], 0),
    GETDATE(),
    1
FROM [Salacia].[dbo].[StoresTbl] s
LEFT JOIN #LocationMapping lm ON s.[Location_Id] = lm.[OldLocationId];
SET IDENTITY_INSERT [dbo].[Store] OFF;
GO

-- Migrate Cost Centers
SET IDENTITY_INSERT [dbo].[CostCenter] ON;
INSERT INTO [dbo].[CostCenter] (
    [CostCenterId], [Name], [StoreId], [TypeId], [TypeName], [AutoTransfer], [IsSalesPoint], [Abbreviation], [CreatedAt], [IsActive]
)
SELECT
    [CostCenter_Id],
    [CostCenter_Name],
    [Store_id],
    [Type_id],
    [CostCenter_Type],
    ISNULL([Auto_Transfer], 0),
    ISNULL([IsSales], 0),
    [Abbreviation],
    GETDATE(),
    1
FROM [Salacia].[dbo].[CostCenterTbl];

-- Find and insert any missing cost centers that are referenced in transactions as source
-- This ensures all cost centers referenced in transactions exist in the new database
INSERT INTO [dbo].[CostCenter] (
    [CostCenterId], [Name], [StoreId], [TypeId], [TypeName], [AutoTransfer], [IsSalesPoint], [CreatedAt], [IsActive]
)
SELECT DISTINCT
    td.[CostCenter_Id_Frm],
    'Migrated Source Cost Center ' + CAST(td.[CostCenter_Id_Frm] AS NVARCHAR(10)),
    NULL, -- No store association
    NULL, -- No type ID
    'Unknown', -- Default type name
    0, -- Default AutoTransfer
    0, -- Default IsSalesPoint
    GETDATE(),
    1
FROM [Salacia].[dbo].[Transaction_DetailsTbl] td
WHERE td.[CostCenter_Id_Frm] IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM [dbo].[CostCenter] cc WHERE cc.[CostCenterId] = td.[CostCenter_Id_Frm]
);

-- Find and insert any missing cost centers that are referenced in transactions as destination
INSERT INTO [dbo].[CostCenter] (
    [CostCenterId], [Name], [StoreId], [TypeId], [TypeName], [AutoTransfer], [IsSalesPoint], [CreatedAt], [IsActive]
)
SELECT DISTINCT
    td.[CostCenter_Id_To],
    'Migrated Destination Cost Center ' + CAST(td.[CostCenter_Id_To] AS NVARCHAR(10)),
    NULL, -- No store association
    NULL, -- No type ID
    'Unknown', -- Default type name
    0, -- Default AutoTransfer
    0, -- Default IsSalesPoint
    GETDATE(),
    1
FROM [Salacia].[dbo].[Transaction_DetailsTbl] td
WHERE td.[CostCenter_Id_To] IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM [dbo].[CostCenter] cc WHERE cc.[CostCenterId] = td.[CostCenter_Id_To]
);

SET IDENTITY_INSERT [dbo].[CostCenter] OFF;
GO

-- Migrate Tax Rates
INSERT INTO [dbo].[Tax] (
    [Name], [Rate], [IsDefault], [CreatedAt], [IsActive]
)
SELECT DISTINCT
    'Tax ' + CAST([Tax_Id] AS NVARCHAR(10)),
    [Tax_Persentage],
    0,
    GETDATE(),
    1
FROM [Salacia].[dbo].[Transaction_DetailsTbl]
WHERE [Tax_Id] IS NOT NULL AND [Tax_Persentage] IS NOT NULL;

-- Create a tax mapping table
CREATE TABLE #TaxMapping (
    [OldTaxId] INT,
    [OldTaxName] NVARCHAR(100),
    [OldTaxRate] DECIMAL(5,2),
    [NewTaxId] INT
);

INSERT INTO #TaxMapping (
    [OldTaxId], [OldTaxName], [OldTaxRate], [NewTaxId]
)
SELECT
    td.[Tax_Id],
    td.[Tax_Name],
    td.[Tax_Persentage],
    t.[TaxId]
FROM [Salacia].[dbo].[Transaction_DetailsTbl] td
JOIN [dbo].[Tax] t ON 'Tax ' + CAST(td.[Tax_Id] AS NVARCHAR(10)) = t.[Name]
WHERE td.[Tax_Id] IS NOT NULL AND td.[Tax_Persentage] IS NOT NULL
GROUP BY td.[Tax_Id], td.[Tax_Name], td.[Tax_Persentage], t.[TaxId];

-- =============================================
-- STEP 2: Migrate Products and Recipes
-- =============================================

-- Migrate Products
SET IDENTITY_INSERT [dbo].[Product] ON;
INSERT INTO [dbo].[Product] (
    [ProductId], [Code], [Name], [BrandId], [UnitId], [UnitGroupId], [DepartmentId],
    [GroupId], [SubGroupId], [CostPrice], [AverageCost], [SalesPrice], [MinStock],
    [MaxStock], [ReorderPoint], [Notes], [IsStockItem], [IsRecipe], [HasExpiry],
    [IsProduction], [IsSaleable], [TaxId], [SalesUnitId], [SalesUnitConversionFactor],
    [AllowDiscount], [CreatedAt], [IsActive]
)
SELECT
    p.[Product_Id],
    p.[Product_Code],
    p.[Product_Name],
    bm.[NewBrandId],
    um.[NewUnitId],
    p.[Unt_GroupId],
    p.[Depart_Id],
    p.[Group_Id],
    p.[SubGroup_Id],
    p.[Cost_Product],
    p.[AvCost],
    p.[SalesPrice],
    p.[MinStock],
    p.[MaxStock],
    p.[ReOrder],
    p.[Notes],
    ISNULL(p.[IsStock], 1),
    ISNULL(p.[IsRecipe], 0),
    ISNULL(p.[IsExpire], 0),
    ISNULL(p.[IsProduction], 0),
    ISNULL(p.[IsSales], 1),
    tm.[NewTaxId],
    NULL, -- SalesUnitId will be updated later
    NULL, -- SalesUnitConversionFactor will be updated later
    ISNULL(p.[IsDescount], 1),
    GETDATE(),
    1
FROM [Salacia].[dbo].[ProductsTbl] p
LEFT JOIN #BrandMapping bm ON p.[Product_BrandId] = bm.[OldBrandId]
LEFT JOIN #UnitMapping um ON p.[Unt_Id] = um.[OldUnitId]
LEFT JOIN #TaxMapping tm ON p.[Tax_Id] = tm.[OldTaxId];
SET IDENTITY_INSERT [dbo].[Product] OFF;
GO

-- Update SalesUnitId and SalesUnitConversionFactor
UPDATE p
SET
    p.[SalesUnitId] = um.[NewUnitId],
    p.[SalesUnitConversionFactor] = op.[Unt_QSales]
FROM [dbo].[Product] p
JOIN [Salacia].[dbo].[ProductsTbl] op ON p.[ProductId] = op.[Product_Id]
JOIN #UnitMapping um ON op.[Unt_IdSales] = um.[OldUnitId]
WHERE op.[Unt_IdSales] IS NOT NULL;
GO

-- Migrate Recipes
-- First, identify products that are recipes
INSERT INTO [dbo].[Recipe] (
    [ProductId], [Name], [Description], [YieldQuantity], [YieldUnitId], [TotalCost], [IsActive], [CreatedAt]
)
SELECT
    p.[ProductId],
    p.[Name] + ' Recipe',
    'Migrated from old system',
    1, -- Default yield quantity
    p.[UnitId],
    p.[CostPrice],
    1,
    GETDATE()
FROM [dbo].[Product] p
WHERE p.[IsRecipe] = 1;

-- Create a recipe mapping table
CREATE TABLE #RecipeMapping (
    [ProductId] INT,
    [RecipeId] INT
);

INSERT INTO #RecipeMapping (
    [ProductId], [RecipeId]
)
SELECT
    r.[ProductId],
    r.[RecipeId]
FROM [dbo].[Recipe] r;

-- Migrate Recipe Ingredients
INSERT INTO [dbo].[RecipeIngredient] (
    [RecipeId], [IngredientProductId], [Quantity], [UnitId], [CostPerUnit], [TotalCost],
    [IsSubRecipe], [WastagePercentage], [CreatedAt], [IsActive]
)
SELECT
    rm.[RecipeId],
    rp.[Recipe_Product_Id],
    rp.[UsedQuantity],
    um.[NewUnitId],
    rp.[Cost_Product],
    rp.[Recipe_Price],
    CASE WHEN EXISTS (SELECT 1 FROM [dbo].[Product] p WHERE p.[ProductId] = rp.[Recipe_Product_Id] AND p.[IsRecipe] = 1) THEN 1 ELSE 0 END,
    rp.[Recipe_Persentage_Loss],
    GETDATE(),
    1
FROM [Salacia].[dbo].[Recipe_ProductsTbl] rp
JOIN #RecipeMapping rm ON rp.[Product_Id] = rm.[ProductId]
JOIN #UnitMapping um ON rp.[Unt_Id] = um.[OldUnitId]
WHERE rp.[Del] IS NULL OR rp.[Del] = 0;
GO

-- =============================================
-- STEP 3: Migrate Inventory Data
-- =============================================

-- Migrate Stock On Hand
INSERT INTO [dbo].[StockOnHand] (
    [ProductId], [CostCenterId], [Quantity], [BaseQuantity], [UnitId], [AverageCost],
    [CostPrice], [ReturnVariance], [NetCost], [SalesPrice], [LastUpdated]
)
SELECT
    soh.[Product_Id],
    soh.[CostCenter_Id],
    soh.[Quntity],
    soh.[QuntityBase],
    um.[NewUnitId],
    soh.[AvCost],
    soh.[Cost_Product],
    soh.[ReturnVariance],
    soh.[NetCost],
    soh.[SalesPrice],
    GETDATE()
FROM [Salacia].[dbo].[StockOnHandTbl] soh
JOIN #UnitMapping um ON soh.[Item_Unit] = um.[OldUnitId]
WHERE soh.[Product_Id] IS NOT NULL AND soh.[CostCenter_Id] IS NOT NULL;
GO

-- Migrate Product Cost Center Links
INSERT INTO [dbo].[ProductCostCenterLink] (
    [ProductId], [CostCenterId], [MaximumStock], [MinimumStock], [ReorderPoint], [CreatedAt], [IsActive]
)
SELECT
    icc.[Product_Id],
    icc.[CostCenter_Id],
    icc.[MaxiMum],
    icc.[MinMum],
    icc.[ReOrder],
    GETDATE(),
    1
FROM [Salacia].[dbo].[ItmCostCenterLink] icc
WHERE icc.[Product_Id] IS NOT NULL AND icc.[CostCenter_Id] IS NOT NULL;
GO

-- Migrate Batches/Lots
INSERT INTO [dbo].[Batch] (
    [BatchNumber], [ProductId], [CostCenterId], [ManufactureDate], [ExpiryDate],
    [InitialQuantity], [CurrentQuantity], [UnitId], [CostPrice], [IsOpen], [CreatedAt], [IsActive]
)
SELECT
    p.[Patch_Name],
    p.[Product_Id],
    p.[CostCenter_Id],
    p.[Prud_Date],
    p.[Exp_Date],
    p.[NetQ_Qsetup_CurrentQ],
    p.[NetQ_Qsetup_CurrentQ],
    um.[NewUnitId],
    NULL, -- No direct cost price in old schema
    CASE WHEN p.[CloseOpen] = 1 THEN 0 ELSE 1 END,
    GETDATE(),
    1
FROM [Salacia].[dbo].[Patches_EXP] p
JOIN #UnitMapping um ON p.[Unt_Id] = um.[OldUnitId]
WHERE p.[Product_Id] IS NOT NULL AND p.[CostCenter_Id] IS NOT NULL AND p.[NoShow] = 0;
GO

-- =============================================
-- STEP 4: Migrate Transaction Data
-- =============================================

-- Migrate Transaction Types
SET IDENTITY_INSERT [dbo].[TransactionType] ON;
INSERT INTO [dbo].[TransactionType] (
    [TransactionTypeId], [Name], [Description], [AffectsInventory], [IsSale], [CreatedAt], [IsActive]
)
SELECT
    t.[Transaction_Id],
    t.[Transaction_Name],
    t.[Transaction_Name] + ' transaction',
    ISNULL(t.[Effecting], 1),
    ISNULL(t.[Pos], 0),
    GETDATE(),
    1
FROM [Salacia].[dbo].[TransactionsTbl] t;
SET IDENTITY_INSERT [dbo].[TransactionType] OFF;
GO

-- Migrate Suppliers
INSERT INTO [dbo].[Supplier] (
    [Name], [CreatedAt], [IsActive]
)
SELECT DISTINCT
    [Suppliers_Name_Frm],
    GETDATE(),
    1
FROM [Salacia].[dbo].[Transaction_DetailsTbl]
WHERE [Suppliers_Name_Frm] IS NOT NULL AND [Suppliers_Name_Frm] <> '';
GO

-- Create a supplier mapping table
CREATE TABLE #SupplierMapping (
    [OldSupplierId] INT,
    [OldSupplierName] NVARCHAR(150),
    [NewSupplierId] INT
);

INSERT INTO #SupplierMapping (
    [OldSupplierName], [NewSupplierId]
)
SELECT
    s.[Name],
    s.[SupplierId]
FROM [dbo].[Supplier] s;

UPDATE #SupplierMapping
SET [OldSupplierId] = td.[Suppliers_Id_Frm]
FROM #SupplierMapping sm
JOIN [Salacia].[dbo].[Transaction_DetailsTbl] td ON sm.[OldSupplierName] = td.[Suppliers_Name_Frm]
WHERE td.[Suppliers_Id_Frm] IS NOT NULL;
GO

-- Migrate Users
INSERT INTO [dbo].[Role] (
    [Name], [Description], [CreatedAt], [IsActive]
)
VALUES
    ('Legacy User', 'Migrated from old system', GETDATE(), 1);

DECLARE @LegacyRoleId INT = (SELECT [RoleId] FROM [dbo].[Role] WHERE [Name] = 'Legacy User');

SET IDENTITY_INSERT [dbo].[User] ON;
INSERT INTO [dbo].[User] (
    [UserId], [Username], [Email], [PasswordHash], [FirstName], [LastName],
    [RoleId], [DefaultLanguage], [CreatedAt], [IsActive]
)
SELECT
    u.[User_Id],
    ISNULL(u.[User_LognName], 'user' + CAST(u.[User_Id] AS NVARCHAR(10))),
    ISNULL(u.[User_Email], 'user' + CAST(u.[User_Id] AS NVARCHAR(10)) + '@example.com'),
    ISNULL(u.[User_Password], 'AQAAAAEAACcQAAAAEHxQPLBfSvDsPUm9qvJJcaP9Oq1OKQQYYBdRVS/WEwZ3FBP+QJrJeYnD1xnr/JQnQA=='), -- Default hashed password
    u.[User_Name],
    '',
    @LegacyRoleId,
    ISNULL(u.[Lang], 'en'),
    GETDATE(),
    ISNULL(u.[User_Active], 1)
FROM [Salacia].[dbo].[UsersTbl] u;
SET IDENTITY_INSERT [dbo].[User] OFF;
GO

-- Migrate Transaction Headers with the new process-based structure
-- First, create a temporary table to handle the three-part transaction process
-- (product request, product order, and receiving)
CREATE TABLE #TransactionHeaderMapping (
    [OldTransactionId] INT,
    [OldTransactionCode] NVARCHAR(50),
    [NewTransactionNumber] NVARCHAR(50),
    [TransactionStage] NVARCHAR(20),
    [StageTypeId] INT,
    [ProcessId] INT
);

-- First, create transaction processes for each unique transaction code
INSERT INTO [dbo].[TransactionProcess] (
    [ProcessNumber], [Description], [CreatedById], [CreatedAt], [IsActive]
)
SELECT DISTINCT
    CAST(th.[Transaction_Code] AS NVARCHAR(50)),
    'Transaction process ' + CAST(th.[Transaction_Code] AS NVARCHAR(50)),
    1, -- Default admin user
    GETDATE(),
    1
FROM [Salacia].[dbo].[Transaction_HeadTbl] th
GROUP BY th.[Transaction_Code];

-- Generate mapping with process IDs and stage type IDs
INSERT INTO #TransactionHeaderMapping (
    [OldTransactionId], [OldTransactionCode], [NewTransactionNumber], [TransactionStage], [StageTypeId], [ProcessId]
)
SELECT
    th.[Ser],
    CAST(th.[Transaction_Code] AS NVARCHAR(50)),
    CAST(th.[Transaction_Code] AS NVARCHAR(50)) +
        CASE
            -- Map transaction types to appropriate stages
            WHEN th.[Transaction_Id] = 1 THEN '-REQ' -- Order Request
            WHEN th.[Transaction_Id] = 2 THEN '-ORD' -- Orders
            WHEN th.[Transaction_Id] = 3 THEN '-RCV' -- Receiving
            WHEN th.[Transaction_Id] = 4 THEN '-TIN' -- Transfer(In)
            WHEN th.[Transaction_Id] = 5 THEN '-TOU' -- Transfer(Out)
            WHEN th.[Transaction_Id] = 6 THEN '-CRN' -- Credit Note
            WHEN th.[Transaction_Id] = 7 THEN '-PRD' -- Production
            WHEN th.[Transaction_Id] = 8 THEN '-ADJ' -- Adjustment
            WHEN th.[Transaction_Id] = 9 THEN '-SAL' -- Sales
            WHEN th.[Transaction_Id] = 10 THEN '-WST' -- Waste
            WHEN th.[Transaction_Id] = 11 THEN '-RTR' -- Request Transfer
            WHEN th.[Transaction_Id] = 12 THEN '-RRL' -- Reverse Roll
            WHEN th.[Transaction_Id] = 15 THEN '-EXP' -- Execution Expire
            WHEN th.[Transaction_Id] = 16 THEN '-SRT' -- Sales Return
            WHEN th.[Transaction_Id] = 17 THEN '-GFT' -- Gift Sales
            WHEN th.[Transaction_Id] = 18 THEN '-ADT' -- Adjustment Temporary
            ELSE '-' + CAST(ROW_NUMBER() OVER (PARTITION BY th.[Transaction_Code] ORDER BY th.[Ser]) AS NVARCHAR(5))
        END,
    CASE
        WHEN th.[Transaction_Id] = 1 THEN 'Request'
        WHEN th.[Transaction_Id] = 2 THEN 'Order'
        WHEN th.[Transaction_Id] = 3 THEN 'Receiving'
        WHEN th.[Transaction_Id] IN (4, 5, 11) THEN 'Transfer'
        WHEN th.[Transaction_Id] IN (8, 18) THEN 'Adjustment'
        WHEN th.[Transaction_Id] = 7 THEN 'Production'
        WHEN th.[Transaction_Id] = 16 THEN 'Return'
        WHEN th.[Transaction_Id] = 6 THEN 'ReturnToSupplier'
        ELSE 'Other'
    END,
    CASE
        WHEN th.[Transaction_Id] = 1 THEN 1 -- Request
        WHEN th.[Transaction_Id] = 2 THEN 2 -- Order
        WHEN th.[Transaction_Id] = 3 THEN 3 -- Receiving
        WHEN th.[Transaction_Id] IN (4, 5, 11) THEN 4 -- Transfer
        WHEN th.[Transaction_Id] IN (8, 18) THEN 5 -- Adjustment
        WHEN th.[Transaction_Id] = 7 THEN 6 -- Production
        WHEN th.[Transaction_Id] = 16 THEN 7 -- Return
        WHEN th.[Transaction_Id] = 6 THEN 8 -- ReturnToSupplier
        ELSE NULL -- Other
    END,
    tp.[ProcessId]
FROM [Salacia].[dbo].[Transaction_HeadTbl] th
JOIN [dbo].[TransactionProcess] tp ON CAST(th.[Transaction_Code] AS NVARCHAR(50)) = tp.[ProcessNumber];

-- Now insert with the process-based structure
SET IDENTITY_INSERT [dbo].[TransactionHeader] ON;

INSERT INTO [dbo].[TransactionHeader] (
    [TransactionId], [TransactionNumber], [ProcessId], [StageTypeId], [TransactionTypeId], [SourceCostCenterId],
    [DestinationCostCenterId], [SupplierId], [ReferenceNumber], [TransactionDate],
    [Notes], [SubTotal], [TaxAmount], [TotalAmount], [DiscountAmount],
    [DiscountPercentage], [Status], [CreatedById], [CreatedAt], [IsActive]
)
SELECT
    th.[Ser],
    thm.[NewTransactionNumber],
    thm.[ProcessId],
    thm.[StageTypeId],
    th.[Transaction_Id],
    NULL, -- Will update source cost center later
    th.[CostCenter_Id],
    sm.[NewSupplierId],
    th.[Invoice_No],
    ISNULL(th.[Transaction_Date], GETDATE()),
    CASE
        WHEN th.[Remarks] IS NULL OR th.[Remarks] = '' THEN 'Transaction Stage: ' + thm.[TransactionStage]
        ELSE th.[Remarks] + ' | Transaction Stage: ' + thm.[TransactionStage]
    END,
    ISNULL(th.[Amount_Bill], 0),
    ISNULL(th.[Tax_Bill], 0),
    ISNULL(th.[Total_Amount], 0),
    ISNULL(th.[Discount_Amount], 0),
    ISNULL(th.[Discount_Pers], 0),
    CASE
        WHEN th.[Transaction_Submit] = 1 THEN 'Submitted'
        WHEN th.[AcceptedLevel] = 1 THEN 'Approved'
        WHEN th.[DiscardLevel] = 1 THEN 'Rejected'
        ELSE 'Draft'
    END,
    ISNULL(th.[User_Id], 1),
    ISNULL(th.[Transaction_Date], GETDATE()),
    1
FROM [Salacia].[dbo].[Transaction_HeadTbl] th
LEFT JOIN #SupplierMapping sm ON th.[Suppliers_Id] = sm.[OldSupplierId]
JOIN #TransactionHeaderMapping thm ON th.[Ser] = thm.[OldTransactionId];

SET IDENTITY_INSERT [dbo].[TransactionHeader] OFF;
GO

-- Update source cost centers based on transaction details
UPDATE th
SET [SourceCostCenterId] = td.[CostCenter_Id_Frm]
FROM [dbo].[TransactionHeader] th
JOIN #TransactionHeaderMapping thm ON th.[TransactionNumber] = thm.[NewTransactionNumber]
JOIN [Salacia].[dbo].[Transaction_DetailsTbl] td
    ON CAST(thm.[OldTransactionCode] AS BIGINT) = td.[Transaction_Code]
    AND th.[TransactionTypeId] = td.[Transaction_Id]
WHERE td.[CostCenter_Id_Frm] IS NOT NULL;

-- Also ensure destination cost centers are properly set
-- Some transactions might have destination cost centers in the details table
UPDATE th
SET [DestinationCostCenterId] = td.[CostCenter_Id_To]
FROM [dbo].[TransactionHeader] th
JOIN #TransactionHeaderMapping thm ON th.[TransactionNumber] = thm.[NewTransactionNumber]
JOIN [Salacia].[dbo].[Transaction_DetailsTbl] td
    ON CAST(thm.[OldTransactionCode] AS BIGINT) = td.[Transaction_Code]
    AND th.[TransactionTypeId] = td.[Transaction_Id]
WHERE td.[CostCenter_Id_To] IS NOT NULL
AND th.[DestinationCostCenterId] IS NULL;
GO

-- Migrate Transaction Details
INSERT INTO [dbo].[TransactionDetail] (
    [TransactionId], [ProductId], [Quantity], [UnitId], [UnitPrice],
    [TaxId], [TaxRate], [TaxAmount], [DiscountAmount], [DiscountPercentage],
    [LineTotal], [IsRecipe], [CreatedAt], [IsActive]
)
SELECT
    th.[TransactionId],
    td.[Product_Id],
    CASE
        WHEN td.[Transaction_Id] IN (1, 3, 5, 8) THEN ISNULL(td.[Reciving_Q], 0) -- Purchase, Transfer In, Production, Adjustment
        WHEN td.[Transaction_Id] IN (2, 4, 6, 7) THEN ISNULL(td.[Invoice_Q], 0)  -- Sale, Transfer Out, Return, Return to Supplier
        ELSE ISNULL(td.[NetQ_Qsetup_CurrentQ], 0)
    END,
    um.[NewUnitId],
    ISNULL(td.[Cost_Product], 0),
    tm.[NewTaxId],
    ISNULL(td.[Tax_Persentage], 0),
    ISNULL(td.[Tax_Value], 0),
    ISNULL(td.[Discount_Amount], 0),
    ISNULL(td.[Discount_Pers], 0),
    ISNULL(td.[CostTotalLine], 0),
    ISNULL(td.[IsRecipe], 0),
    GETDATE(),
    1
FROM [Salacia].[dbo].[Transaction_DetailsTbl] td
JOIN #TransactionHeaderMapping thm ON CAST(thm.[OldTransactionCode] AS BIGINT) = td.[Transaction_Code]
JOIN [dbo].[TransactionHeader] th ON th.[TransactionNumber] = thm.[NewTransactionNumber] AND th.[TransactionTypeId] = td.[Transaction_Id]
LEFT JOIN #UnitMapping um ON td.[Current_Unt_Id] = um.[OldUnitId]
LEFT JOIN #TaxMapping tm ON td.[Tax_Id] = tm.[OldTaxId]
WHERE td.[Product_Id] IS NOT NULL AND (td.[Del] IS NULL OR td.[Del] = 0);
GO

-- =============================================
-- STEP 5: Migrate Payment Data
-- =============================================

-- Migrate Payment Methods
INSERT INTO [dbo].[PaymentMethod] (
    [Name], [Description], [AccountNumber], [IsPointsSystem], [CreatedAt], [IsActive]
)
SELECT
    [MethodOfpayment_Name],
    [MethodOfpayment_Name] + ' payment method',
    [AccountNo],
    ISNULL([IsPoints], 0),
    GETDATE(),
    1
FROM [Salacia].[dbo].[MethodOfPaymentTBL]
WHERE [MethodOfpayment_Name] IS NOT NULL;
GO

-- Create a payment method mapping table
CREATE TABLE #PaymentMethodMapping (
    [OldPaymentMethodId] INT,
    [OldPaymentMethodName] NVARCHAR(150),
    [NewPaymentMethodId] INT
);

INSERT INTO #PaymentMethodMapping (
    [OldPaymentMethodId], [OldPaymentMethodName], [NewPaymentMethodId]
)
SELECT
    m.[MethodOfpayment_ID],
    m.[MethodOfpayment_Name],
    pm.[PaymentMethodId]
FROM [Salacia].[dbo].[MethodOfPaymentTBL] m
JOIN [dbo].[PaymentMethod] pm ON m.[MethodOfpayment_Name] = pm.[Name]
WHERE m.[MethodOfpayment_Name] IS NOT NULL;
GO

-- Migrate Payments
INSERT INTO [dbo].[Payment] (
    [TransactionId], [PaymentMethodId], [Amount], [ChangeAmount],
    [PointsUsed], [PointsRate], [Notes], [PaymentDate], [CreatedById], [CreatedAt], [IsActive]
)
SELECT
    th.[TransactionId],
    pmm.[NewPaymentMethodId],
    p.[Amount],
    ISNULL(p.[ChangeAmount], 0),
    0, -- No direct points used in old schema
    ISNULL(p.[PointsRate], 0),
    p.[Comment],
    ISNULL(th.[TransactionDate], GETDATE()),
    1, -- Default admin user
    GETDATE(),
    1
FROM [Salacia].[dbo].[PaymentTBL] p
JOIN #TransactionHeaderMapping thm ON CAST(thm.[OldTransactionCode] AS BIGINT) = p.[Transaction_Code]
JOIN [dbo].[TransactionHeader] th ON th.[TransactionNumber] = thm.[NewTransactionNumber]
JOIN #PaymentMethodMapping pmm ON p.[MethodOfpayment_ID] = pmm.[OldPaymentMethodId]
WHERE p.[Amount] IS NOT NULL AND p.[Amount] > 0;
GO

-- =============================================
-- STEP 6: Clean Up
-- =============================================

-- Drop temporary tables
DROP TABLE #UnitMapping;
DROP TABLE #BrandMapping;
DROP TABLE #LocationMapping;
DROP TABLE #TaxMapping;
DROP TABLE #RecipeMapping;
DROP TABLE #SupplierMapping;
DROP TABLE #PaymentMethodMapping;
DROP TABLE #TransactionHeaderMapping;

-- =============================================
-- STEP 7: Insert Default Data (if needed)
-- =============================================

-- Insert default roles if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[Role] WHERE [Name] = 'Administrator')
    INSERT INTO [dbo].[Role] ([Name], [Description], [CreatedAt], [IsActive])
    VALUES ('Administrator', 'System administrator with full access', GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[Role] WHERE [Name] = 'Manager')
    INSERT INTO [dbo].[Role] ([Name], [Description], [CreatedAt], [IsActive])
    VALUES ('Manager', 'Manager with approval rights', GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[Role] WHERE [Name] = 'Inventory Clerk')
    INSERT INTO [dbo].[Role] ([Name], [Description], [CreatedAt], [IsActive])
    VALUES ('Inventory Clerk', 'Staff responsible for inventory management', GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[Role] WHERE [Name] = 'Sales Staff')
    INSERT INTO [dbo].[Role] ([Name], [Description], [CreatedAt], [IsActive])
    VALUES ('Sales Staff', 'Staff responsible for sales', GETDATE(), 1);

-- Insert default admin user if no users exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[User] WHERE [Username] = 'admin')
BEGIN
    DECLARE @AdminRoleId INT = (SELECT [RoleId] FROM [dbo].[Role] WHERE [Name] = 'Administrator');

    IF @AdminRoleId IS NOT NULL
        INSERT INTO [dbo].[User] ([Username], [Email], [PasswordHash], [FirstName], [LastName], [RoleId], [CreatedAt], [IsActive])
        VALUES ('admin', '<EMAIL>', 'AQAAAAEAACcQAAAAEHxQPLBfSvDsPUm9qvJJcaP9Oq1OKQQYYBdRVS/WEwZ3FBP+QJrJeYnD1xnr/JQnQA==', 'System', 'Administrator', @AdminRoleId, GETDATE(), 1);
END

-- Insert default transaction types if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[TransactionType] WHERE [Name] = 'Purchase')
    INSERT INTO [dbo].[TransactionType] ([Name], [Description], [AffectsInventory], [IsSale], [CreatedAt], [IsActive])
    VALUES ('Purchase', 'Purchase from supplier', 1, 0, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[TransactionType] WHERE [Name] = 'Sale')
    INSERT INTO [dbo].[TransactionType] ([Name], [Description], [AffectsInventory], [IsSale], [CreatedAt], [IsActive])
    VALUES ('Sale', 'Sale to customer', 1, 1, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[TransactionType] WHERE [Name] = 'Transfer')
    INSERT INTO [dbo].[TransactionType] ([Name], [Description], [AffectsInventory], [IsSale], [CreatedAt], [IsActive])
    VALUES ('Transfer', 'Transfer between cost centers', 1, 0, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[TransactionType] WHERE [Name] = 'Adjustment')
    INSERT INTO [dbo].[TransactionType] ([Name], [Description], [AffectsInventory], [IsSale], [CreatedAt], [IsActive])
    VALUES ('Adjustment', 'Inventory adjustment', 1, 0, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[TransactionType] WHERE [Name] = 'Production')
    INSERT INTO [dbo].[TransactionType] ([Name], [Description], [AffectsInventory], [IsSale], [CreatedAt], [IsActive])
    VALUES ('Production', 'Production of recipes', 1, 0, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[TransactionType] WHERE [Name] = 'Return')
    INSERT INTO [dbo].[TransactionType] ([Name], [Description], [AffectsInventory], [IsSale], [CreatedAt], [IsActive])
    VALUES ('Return', 'Return from customer', 1, 0, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[TransactionType] WHERE [Name] = 'Return to Supplier')
    INSERT INTO [dbo].[TransactionType] ([Name], [Description], [AffectsInventory], [IsSale], [CreatedAt], [IsActive])
    VALUES ('Return to Supplier', 'Return to supplier', 1, 0, GETDATE(), 1);

-- Insert default payment methods if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[PaymentMethod] WHERE [Name] = 'Cash')
    INSERT INTO [dbo].[PaymentMethod] ([Name], [Description], [IsPointsSystem], [CreatedAt], [IsActive])
    VALUES ('Cash', 'Cash payment', 0, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[PaymentMethod] WHERE [Name] = 'Credit Card')
    INSERT INTO [dbo].[PaymentMethod] ([Name], [Description], [IsPointsSystem], [CreatedAt], [IsActive])
    VALUES ('Credit Card', 'Credit card payment', 0, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[PaymentMethod] WHERE [Name] = 'Debit Card')
    INSERT INTO [dbo].[PaymentMethod] ([Name], [Description], [IsPointsSystem], [CreatedAt], [IsActive])
    VALUES ('Debit Card', 'Debit card payment', 0, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[PaymentMethod] WHERE [Name] = 'Bank Transfer')
    INSERT INTO [dbo].[PaymentMethod] ([Name], [Description], [IsPointsSystem], [CreatedAt], [IsActive])
    VALUES ('Bank Transfer', 'Bank transfer payment', 0, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[PaymentMethod] WHERE [Name] = 'Loyalty Points')
    INSERT INTO [dbo].[PaymentMethod] ([Name], [Description], [IsPointsSystem], [PointsConversionRate], [CreatedAt], [IsActive])
    VALUES ('Loyalty Points', 'Payment using loyalty points', 1, 0.01, GETDATE(), 1);

-- Insert default currencies if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[Currency] WHERE [Code] = 'USD')
    INSERT INTO [dbo].[Currency] ([Code], [Name], [Symbol], [ExchangeRate], [IsBaseCurrency], [CreatedAt], [IsActive])
    VALUES ('USD', 'US Dollar', '$', 1.00, 1, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[Currency] WHERE [Code] = 'EUR')
    INSERT INTO [dbo].[Currency] ([Code], [Name], [Symbol], [ExchangeRate], [CreatedAt], [IsActive])
    VALUES ('EUR', 'Euro', '€', 1.10, GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[Currency] WHERE [Code] = 'GBP')
    INSERT INTO [dbo].[Currency] ([Code], [Name], [Symbol], [ExchangeRate], [CreatedAt], [IsActive])
    VALUES ('GBP', 'British Pound', '£', 1.30, GETDATE(), 1);

-- Insert default shifts if they don't exist
IF NOT EXISTS (SELECT 1 FROM [dbo].[Shift] WHERE [Name] = 'Morning')
    INSERT INTO [dbo].[Shift] ([Name], [StartTime], [EndTime], [CreatedAt], [IsActive])
    VALUES ('Morning', '06:00:00', '14:00:00', GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[Shift] WHERE [Name] = 'Afternoon')
    INSERT INTO [dbo].[Shift] ([Name], [StartTime], [EndTime], [CreatedAt], [IsActive])
    VALUES ('Afternoon', '14:00:00', '22:00:00', GETDATE(), 1);

IF NOT EXISTS (SELECT 1 FROM [dbo].[Shift] WHERE [Name] = 'Night')
    INSERT INTO [dbo].[Shift] ([Name], [StartTime], [EndTime], [CreatedAt], [IsActive])
    VALUES ('Night', '22:00:00', '06:00:00', GETDATE(), 1);

-- =============================================
-- STEP 8: Final Validation
-- =============================================

-- Print migration summary using variables to avoid subquery issues
DECLARE @DepartmentCount INT = (SELECT COUNT(*) FROM [dbo].[Department]);
DECLARE @ProductGroupCount INT = (SELECT COUNT(*) FROM [dbo].[ProductGroup]);
DECLARE @ProductSubGroupCount INT = (SELECT COUNT(*) FROM [dbo].[ProductSubGroup]);
DECLARE @ProductCount INT = (SELECT COUNT(*) FROM [dbo].[Product]);
DECLARE @RecipeCount INT = (SELECT COUNT(*) FROM [dbo].[Recipe]);
DECLARE @RecipeIngredientCount INT = (SELECT COUNT(*) FROM [dbo].[RecipeIngredient]);
DECLARE @StockOnHandCount INT = (SELECT COUNT(*) FROM [dbo].[StockOnHand]);
DECLARE @TransactionHeaderCount INT = (SELECT COUNT(*) FROM [dbo].[TransactionHeader]);
DECLARE @TransactionDetailCount INT = (SELECT COUNT(*) FROM [dbo].[TransactionDetail]);
DECLARE @PaymentCount INT = (SELECT COUNT(*) FROM [dbo].[Payment]);
DECLARE @ProductCostCenterLinkCount INT = (SELECT COUNT(*) FROM [dbo].[ProductCostCenterLink]);

PRINT 'Migration completed successfully!';
PRINT 'Departments migrated: ' + CAST(@DepartmentCount AS NVARCHAR(10));
PRINT 'Product Groups migrated: ' + CAST(@ProductGroupCount AS NVARCHAR(10));
PRINT 'Product SubGroups migrated: ' + CAST(@ProductSubGroupCount AS NVARCHAR(10));
PRINT 'Products migrated: ' + CAST(@ProductCount AS NVARCHAR(10));
PRINT 'Recipes migrated: ' + CAST(@RecipeCount AS NVARCHAR(10));
PRINT 'Recipe Ingredients migrated: ' + CAST(@RecipeIngredientCount AS NVARCHAR(10));
PRINT 'Stock On Hand records migrated: ' + CAST(@StockOnHandCount AS NVARCHAR(10));
PRINT 'Transactions migrated: ' + CAST(@TransactionHeaderCount AS NVARCHAR(10));
PRINT 'Transaction Details migrated: ' + CAST(@TransactionDetailCount AS NVARCHAR(10));
PRINT 'Payments migrated: ' + CAST(@PaymentCount AS NVARCHAR(10));
PRINT 'Product-CostCenter Links migrated: ' + CAST(@ProductCostCenterLinkCount AS NVARCHAR(10));
GO
