/* You can add global styles to this file, and also import other style files */
@import 'bootstrap/scss/bootstrap';

// Custom variables
$primary-color: #3f51b5;
$secondary-color: #f50057;
$success-color: #4caf50;
$info-color: #2196f3;
$warning-color: #ff9800;
$danger-color: #f44336;
$light-color: #f5f5f5;
$dark-color: #212121;

// Override Bootstrap variables
$theme-colors: (
  "primary": $primary-color,
  "secondary": $secondary-color,
  "success": $success-color,
  "info": $info-color,
  "warning": $warning-color,
  "danger": $danger-color,
  "light": $light-color,
  "dark": $dark-color
);

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  background-color: #f8f9fa;
}

// Custom utility classes
.cursor-pointer {
  cursor: pointer;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.card {
  border-radius: 0.5rem;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: box-shadow 0.3s ease-in-out;

  &:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
}

.btn {
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
  }
}

.table {
  thead {
    background-color: $light-color;
    th {
      font-weight: 600;
      border-bottom: 2px solid $primary-color;
    }
  }

  tbody {
    tr {
      transition: background-color 0.2s ease-in-out;

      &:hover {
        background-color: rgba($primary-color, 0.05);
      }
    }
  }
}

.page-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

// Sidebar styles
.sidebar {
  background-color: $dark-color;
  color: white;
  min-height: 100vh;

  .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    margin-bottom: 0.25rem;

    &:hover, &.active {
      color: white;
      background-color: rgba(255, 255, 255, 0.1);
    }

    i {
      margin-right: 0.5rem;
    }
  }
}
