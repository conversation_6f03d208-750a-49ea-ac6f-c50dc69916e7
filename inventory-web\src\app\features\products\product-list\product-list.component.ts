import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlaceholderComponent } from '../../../shared/components/placeholder/placeholder.component';

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [CommonModule, PlaceholderComponent],
  template: `
    <app-placeholder 
      title="Products Management" 
      message="The products management feature is coming soon. This will allow you to manage your inventory products."
      icon="bi-box"
      backLink="/dashboard"
      backLabel="Back to Dashboard">
    </app-placeholder>
  `
})
export class ProductListComponent {}
