using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using InventoryAPI.Data;
using InventoryAPI.Models;
using InventoryAPI.DTOs;

namespace InventoryAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TransfersController : ControllerBase
    {
        private readonly InventoryDbContext _context;

        public TransfersController(InventoryDbContext context)
        {
            _context = context;
        }

        // GET: api/Transfers
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TransactionHeader>>> GetTransfers()
        {
            // Get all transactions of type "Transfer"
            var transferType = await _context.TransactionTypes
                .FirstOrDefaultAsync(t => t.Name == "Transfer");

            if (transferType == null)
            {
                return NotFound("Transfer transaction type not found");
            }

            return await _context.TransactionHeaders
                .Include(t => t.TransactionType)
                .Include(t => t.SourceCostCenter)
                .Include(t => t.DestinationCostCenter)
                .Where(t => t.TransactionTypeId == transferType.TransactionTypeId && t.IsActive)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        // GET: api/Transfers/5
        [HttpGet("{id}")]
        public async Task<ActionResult<TransferDTO>> GetTransfer(int id)
        {
            var transaction = await _context.TransactionHeaders
                .Include(t => t.TransactionType)
                .Include(t => t.SourceCostCenter)
                .Include(t => t.DestinationCostCenter)
                .FirstOrDefaultAsync(t => t.TransactionId == id && t.IsActive);

            if (transaction == null)
            {
                return NotFound();
            }

            var details = await _context.TransactionDetails
                .Include(d => d.Product)
                .Include(d => d.Unit)
                .Include(d => d.Batch)
                .Where(d => d.TransactionId == id && d.IsActive)
                .ToListAsync();

            var transferDTO = new TransferDTO
            {
                TransactionId = transaction.TransactionId,
                TransactionNumber = transaction.TransactionNumber,
                TransactionDate = transaction.TransactionDate,
                SourceCostCenterId = transaction.SourceCostCenterId,
                SourceCostCenterName = transaction.SourceCostCenter?.Name,
                DestinationCostCenterId = transaction.DestinationCostCenterId,
                DestinationCostCenterName = transaction.DestinationCostCenter?.Name,
                Notes = transaction.Notes,
                Status = transaction.Status,
                SubTotal = transaction.SubTotal,
                TaxAmount = transaction.TaxAmount,
                TotalAmount = transaction.TotalAmount,
                Items = details.Select(d => new TransferItemDTO
                {
                    TransactionDetailId = d.TransactionDetailId,
                    ProductId = d.ProductId,
                    ProductCode = d.Product.Code,
                    ProductName = d.Product.Name,
                    BatchId = d.BatchId,
                    BatchNumber = d.Batch?.BatchNumber,
                    OrderQuantity = d.Quantity,
                    TransferQuantity = d.Quantity,
                    UnitId = d.UnitId,
                    UnitName = d.Unit.Name,
                    UnitPrice = d.UnitPrice,
                    LineTotal = d.LineTotal
                }).ToList()
            };

            return transferDTO;
        }

        // POST: api/Transfers
        [HttpPost]
        public async Task<ActionResult<TransactionHeader>> CreateTransfer(TransferDTO transferDTO)
        {
            // Get the Transfer transaction type
            var transferType = await _context.TransactionTypes
                .FirstOrDefaultAsync(t => t.Name == "Transfer");

            if (transferType == null)
            {
                return BadRequest("Transfer transaction type not found");
            }

            // Validate source and destination cost centers
            if (transferDTO.SourceCostCenterId == transferDTO.DestinationCostCenterId)
            {
                return BadRequest("Source and destination cost centers cannot be the same");
            }

            // Create the transaction header
            var transaction = new TransactionHeader
            {
                TransactionNumber = GenerateTransactionNumber(),
                TransactionTypeId = transferType.TransactionTypeId,
                SourceCostCenterId = transferDTO.SourceCostCenterId,
                DestinationCostCenterId = transferDTO.DestinationCostCenterId,
                TransactionDate = transferDTO.TransactionDate,
                Notes = transferDTO.Notes,
                Status = "Draft",
                SubTotal = transferDTO.Items.Sum(i => i.TransferQuantity * i.UnitPrice),
                TaxAmount = 0, // Transfers typically don't have tax
                TotalAmount = transferDTO.Items.Sum(i => i.TransferQuantity * i.UnitPrice),
                CreatedById = 1, // Replace with actual user ID from authentication
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _context.TransactionHeaders.Add(transaction);
            await _context.SaveChangesAsync();

            // Create the transaction details
            foreach (var item in transferDTO.Items)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.TransactionId,
                    ProductId = item.ProductId,
                    BatchId = item.BatchId,
                    Quantity = item.TransferQuantity,
                    UnitId = item.UnitId,
                    UnitPrice = item.UnitPrice,
                    LineTotal = item.TransferQuantity * item.UnitPrice,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                _context.TransactionDetails.Add(detail);

                // Update stock on hand for source (decrease) and destination (increase)
                await UpdateStockOnHand(item.ProductId, transferDTO.SourceCostCenterId.Value, -item.TransferQuantity, item.UnitPrice);
                await UpdateStockOnHand(item.ProductId, transferDTO.DestinationCostCenterId.Value, item.TransferQuantity, item.UnitPrice);
            }

            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetTransfer), new { id = transaction.TransactionId }, transaction);
        }

        // PUT: api/Transfers/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTransfer(int id, TransferDTO transferDTO)
        {
            if (id != transferDTO.TransactionId)
            {
                return BadRequest();
            }

            var transaction = await _context.TransactionHeaders.FindAsync(id);
            if (transaction == null)
            {
                return NotFound();
            }

            // Validate source and destination cost centers
            if (transferDTO.SourceCostCenterId == transferDTO.DestinationCostCenterId)
            {
                return BadRequest("Source and destination cost centers cannot be the same");
            }

            // Get existing details to reverse stock changes
            var existingDetails = await _context.TransactionDetails
                .Where(d => d.TransactionId == id)
                .ToListAsync();

            // Reverse stock changes
            foreach (var detail in existingDetails)
            {
                await UpdateStockOnHand(detail.ProductId, transaction.SourceCostCenterId.Value, detail.Quantity, detail.UnitPrice);
                await UpdateStockOnHand(detail.ProductId, transaction.DestinationCostCenterId.Value, -detail.Quantity, detail.UnitPrice);
                _context.TransactionDetails.Remove(detail);
            }

            // Update transaction header
            transaction.SourceCostCenterId = transferDTO.SourceCostCenterId;
            transaction.DestinationCostCenterId = transferDTO.DestinationCostCenterId;
            transaction.TransactionDate = transferDTO.TransactionDate;
            transaction.Notes = transferDTO.Notes;
            transaction.SubTotal = transferDTO.Items.Sum(i => i.TransferQuantity * i.UnitPrice);
            transaction.TotalAmount = transaction.SubTotal;
            transaction.UpdatedAt = DateTime.UtcNow;

            _context.Entry(transaction).State = EntityState.Modified;

            // Add new details and update stock
            foreach (var item in transferDTO.Items)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.TransactionId,
                    ProductId = item.ProductId,
                    BatchId = item.BatchId,
                    Quantity = item.TransferQuantity,
                    UnitId = item.UnitId,
                    UnitPrice = item.UnitPrice,
                    LineTotal = item.TransferQuantity * item.UnitPrice,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                _context.TransactionDetails.Add(detail);

                // Update stock on hand for source (decrease) and destination (increase)
                await UpdateStockOnHand(item.ProductId, transferDTO.SourceCostCenterId.Value, -item.TransferQuantity, item.UnitPrice);
                await UpdateStockOnHand(item.ProductId, transferDTO.DestinationCostCenterId.Value, item.TransferQuantity, item.UnitPrice);
            }

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TransactionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/Transfers/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTransfer(int id)
        {
            var transaction = await _context.TransactionHeaders
                .Include(t => t.TransactionDetails)
                .FirstOrDefaultAsync(t => t.TransactionId == id);
                
            if (transaction == null)
            {
                return NotFound();
            }

            // Reverse stock changes
            foreach (var detail in transaction.TransactionDetails)
            {
                await UpdateStockOnHand(detail.ProductId, transaction.SourceCostCenterId.Value, detail.Quantity, detail.UnitPrice);
                await UpdateStockOnHand(detail.ProductId, transaction.DestinationCostCenterId.Value, -detail.Quantity, detail.UnitPrice);
            }

            // Soft delete
            transaction.IsActive = false;
            transaction.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool TransactionExists(int id)
        {
            return _context.TransactionHeaders.Any(e => e.TransactionId == id);
        }

        private string GenerateTransactionNumber()
        {
            // Generate a unique transaction number
            // Format: TRF-YYYYMMDD-XXXX
            string prefix = "TRF";
            string date = DateTime.Now.ToString("yyyyMMdd");
            string random = new Random().Next(1000, 9999).ToString();
            
            return $"{prefix}-{date}-{random}";
        }

        private async Task UpdateStockOnHand(int productId, int costCenterId, decimal quantity, decimal unitPrice)
        {
            // Find existing stock record
            var stock = await _context.StockOnHands
                .FirstOrDefaultAsync(s => s.ProductId == productId && s.CostCenterId == costCenterId);

            if (stock == null)
            {
                // Create new stock record if it doesn't exist
                stock = new StockOnHand
                {
                    ProductId = productId,
                    CostCenterId = costCenterId,
                    Quantity = quantity,
                    BaseQuantity = quantity, // Assuming base unit for now
                    UnitId = await _context.Products
                        .Where(p => p.ProductId == productId)
                        .Select(p => p.UnitId)
                        .FirstOrDefaultAsync(),
                    CostPrice = unitPrice,
                    AverageCost = unitPrice,
                    LastUpdated = DateTime.UtcNow
                };

                _context.StockOnHands.Add(stock);
            }
            else
            {
                // Update existing stock record
                decimal oldQuantity = stock.Quantity;
                decimal oldCost = stock.AverageCost ?? unitPrice;
                
                stock.Quantity += quantity;
                
                // Calculate new average cost if adding stock
                if (quantity > 0)
                {
                    stock.AverageCost = ((oldQuantity * oldCost) + (quantity * unitPrice)) / (oldQuantity + quantity);
                }
                
                stock.LastUpdated = DateTime.UtcNow;
                
                _context.Entry(stock).State = EntityState.Modified;
            }
        }
    }
}
