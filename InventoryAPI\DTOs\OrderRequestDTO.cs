using System;
using System.Collections.Generic;

namespace InventoryAPI.DTOs
{
    public class OrderRequestDTO
    {
        public int TransactionId { get; set; }
        public string TransactionNumber { get; set; }
        public DateTime TransactionDate { get; set; }
        public int? SourceCostCenterId { get; set; }
        public string SourceCostCenterName { get; set; }
        public int? DestinationCostCenterId { get; set; }
        public string DestinationCostCenterName { get; set; }
        public string Notes { get; set; }
        public string Status { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public List<OrderRequestItemDTO> Items { get; set; } = new List<OrderRequestItemDTO>();
    }

    public class OrderRequestItemDTO
    {
        public int TransactionDetailId { get; set; }
        public int ProductId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public decimal Quantity { get; set; }
        public int UnitId { get; set; }
        public string UnitName { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal LineTotal { get; set; }
    }
}
