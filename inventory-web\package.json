{"name": "inventory-web", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.json", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:inventory-web": "node dist/inventory-web/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^17.1.0", "@angular/cdk": "^17.3.10", "@angular/common": "^17.1.0", "@angular/compiler": "^17.1.0", "@angular/core": "^17.1.0", "@angular/forms": "^17.1.0", "@angular/material": "^17.3.10", "@angular/platform-browser": "^17.1.0", "@angular/platform-browser-dynamic": "^17.1.0", "@angular/platform-server": "^17.1.0", "@angular/router": "^17.1.0", "@angular/ssr": "^17.1.1", "@auth0/angular-jwt": "^5.2.0", "bootstrap": "^5.3.6", "chart.js": "^4.4.9", "express": "^4.18.2", "ng2-charts": "^5.0.4", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.1.1", "@angular/cli": "^17.1.1", "@angular/compiler-cli": "^17.1.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.3.2"}}