import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { OrderRequestDTO, TransactionHeader } from '../models/transaction.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class OrderRequestService {
  private apiUrl = `${environment.apiUrl}/orderrequest`;

  constructor(private http: HttpClient) { }

  getOrderRequests(): Observable<TransactionHeader[]> {
    return this.http.get<TransactionHeader[]>(this.apiUrl);
  }

  getOrderRequest(id: number): Observable<OrderRequestDTO> {
    return this.http.get<OrderRequestDTO>(`${this.apiUrl}/${id}`);
  }

  createOrderRequest(orderRequest: OrderRequestDTO): Observable<TransactionHeader> {
    return this.http.post<TransactionHeader>(this.apiUrl, orderRequest);
  }

  updateOrderRequest(id: number, orderRequest: OrderRequestDTO): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, orderRequest);
  }

  deleteOrderRequest(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  submitOrderRequest(id: number): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/${id}/submit`, {});
  }
}
