﻿// <auto-generated />
using System;
using InventoryAPI.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace InventoryAPI.Migrations
{
    [DbContext(typeof(InventoryDbContext))]
    partial class InventoryDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("InventoryAPI.Models.ApplicationForm", b =>
                {
                    b.Property<int>("FormId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FormId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FormId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("FormCategory")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("FormCategory");

                    b.Property<string>("FormDescription")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("FormDescription");

                    b.Property<string>("FormName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("FormName");

                    b.Property<string>("FormPath")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("FormPath");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("FormId");

                    b.ToTable("ApplicationForm");
                });

            modelBuilder.Entity("InventoryAPI.Models.Batch", b =>
                {
                    b.Property<int>("BatchId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BatchId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BatchId"));

                    b.Property<string>("BatchNumber")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("BatchNumber");

                    b.Property<int>("CostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("CostPrice");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<decimal>("CurrentQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("CurrentQuantity");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ExpiryDate");

                    b.Property<decimal>("InitialQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("InitialQuantity");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsOpen")
                        .HasColumnType("bit")
                        .HasColumnName("IsOpen");

                    b.Property<DateTime?>("ManufactureDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ManufactureDate");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    b.Property<int?>("UnitId")
                        .HasColumnType("int")
                        .HasColumnName("UnitId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("BatchId");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("ProductId");

                    b.HasIndex("UnitId");

                    b.ToTable("Batch");
                });

            modelBuilder.Entity("InventoryAPI.Models.Brand", b =>
                {
                    b.Property<int>("BrandId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BrandId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BrandId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("BrandId");

                    b.ToTable("Brand");
                });

            modelBuilder.Entity("InventoryAPI.Models.CostCenter", b =>
                {
                    b.Property<int>("CostCenterId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CostCenterId"));

                    b.Property<string>("Abbreviation")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Abbreviation");

                    b.Property<bool>("AutoTransfer")
                        .HasColumnType("bit")
                        .HasColumnName("AutoTransfer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsSalesPoint")
                        .HasColumnType("bit")
                        .HasColumnName("IsSalesPoint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<int?>("StoreId")
                        .HasColumnType("int")
                        .HasColumnName("StoreId");

                    b.Property<int?>("TypeId")
                        .HasColumnType("int")
                        .HasColumnName("TypeId");

                    b.Property<string>("TypeName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("TypeName");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("CostCenterId");

                    b.HasIndex("StoreId");

                    b.ToTable("CostCenter");
                });

            modelBuilder.Entity("InventoryAPI.Models.CostCenterFiscalYear", b =>
                {
                    b.Property<int>("CostCenterFiscalYearId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CostCenterFiscalYearId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CostCenterFiscalYearId"));

                    b.Property<int>("CostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("FiscalYearId")
                        .HasColumnType("int")
                        .HasColumnName("FiscalYearId");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("CostCenterFiscalYearId");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("FiscalYearId");

                    b.ToTable("CostCenterFiscalYear");
                });

            modelBuilder.Entity("InventoryAPI.Models.Currency", b =>
                {
                    b.Property<int>("CurrencyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CurrencyId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CurrencyId"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("Code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<decimal>("ExchangeRate")
                        .HasPrecision(18, 6)
                        .HasColumnType("decimal(18,6)")
                        .HasColumnName("ExchangeRate");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsBaseCurrency")
                        .HasColumnType("bit")
                        .HasColumnName("IsBaseCurrency");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Name");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Symbol");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("CurrencyId");

                    b.ToTable("Currencies");
                });

            modelBuilder.Entity("InventoryAPI.Models.Department", b =>
                {
                    b.Property<int>("DepartmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DepartmentId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DepartmentId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("DepartmentId");

                    b.ToTable("Department");
                });

            modelBuilder.Entity("InventoryAPI.Models.FiscalYear", b =>
                {
                    b.Property<int>("FiscalYearId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FiscalYearId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FiscalYearId"));

                    b.Property<int?>("ClosedById")
                        .HasColumnType("int")
                        .HasColumnName("ClosedById");

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ClosedDate");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("EndDate");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsClosed")
                        .HasColumnType("bit")
                        .HasColumnName("IsClosed");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("StartDate");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("Year");

                    b.HasKey("FiscalYearId");

                    b.HasIndex("ClosedById");

                    b.ToTable("FiscalYear");
                });

            modelBuilder.Entity("InventoryAPI.Models.FormAction", b =>
                {
                    b.Property<int>("ActionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ActionId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ActionId"));

                    b.Property<string>("ActionCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ActionCode");

                    b.Property<string>("ActionDescription")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("ActionDescription");

                    b.Property<string>("ActionName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ActionName");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("FormId")
                        .HasColumnType("int")
                        .HasColumnName("FormId");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("ActionId");

                    b.HasIndex("FormId");

                    b.ToTable("FormAction");
                });

            modelBuilder.Entity("InventoryAPI.Models.Location", b =>
                {
                    b.Property<int>("LocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LocationId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LocationId"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Address");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("City");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Country");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("PostalCode");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("State");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("LocationId");

                    b.ToTable("Location");
                });

            modelBuilder.Entity("InventoryAPI.Models.Payment", b =>
                {
                    b.Property<int>("PaymentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PaymentId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PaymentId"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Amount");

                    b.Property<decimal?>("ChangeAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ChangeAmount");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("CreatedById")
                        .HasColumnType("int")
                        .HasColumnName("CreatedById");

                    b.Property<int?>("CurrencyId")
                        .HasColumnType("int")
                        .HasColumnName("CurrencyId");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("PaymentDate");

                    b.Property<int>("PaymentMethodId")
                        .HasColumnType("int")
                        .HasColumnName("PaymentMethodId");

                    b.Property<decimal?>("PointsRate")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("PointsRate");

                    b.Property<decimal?>("PointsUsed")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("PointsUsed");

                    b.Property<int?>("ShiftId")
                        .HasColumnType("int")
                        .HasColumnName("ShiftId");

                    b.Property<int?>("TransactionHeaderTransactionId")
                        .HasColumnType("int");

                    b.Property<int>("TransactionId")
                        .HasColumnType("int")
                        .HasColumnName("TransactionId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("PaymentId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("PaymentMethodId");

                    b.HasIndex("ShiftId");

                    b.HasIndex("TransactionHeaderTransactionId");

                    b.HasIndex("TransactionId");

                    b.ToTable("Payment");
                });

            modelBuilder.Entity("InventoryAPI.Models.PaymentMethod", b =>
                {
                    b.Property<int>("PaymentMethodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PaymentMethodId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PaymentMethodId"));

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("AccountNumber");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsPointsSystem")
                        .HasColumnType("bit")
                        .HasColumnName("IsPointsSystem");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<decimal?>("PointsConversionRate")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("PointsConversionRate");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("PaymentMethodId");

                    b.ToTable("PaymentMethod");
                });

            modelBuilder.Entity("InventoryAPI.Models.Period", b =>
                {
                    b.Property<int>("PeriodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PeriodId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PeriodId"));

                    b.Property<int?>("ClosedById")
                        .HasColumnType("int")
                        .HasColumnName("ClosedById");

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ClosedDate");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("EndDate");

                    b.Property<int>("FiscalYearId")
                        .HasColumnType("int")
                        .HasColumnName("FiscalYearId");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsClosed")
                        .HasColumnType("bit")
                        .HasColumnName("IsClosed");

                    b.Property<int>("PeriodNumber")
                        .HasColumnType("int")
                        .HasColumnName("PeriodNumber");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("StartDate");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("PeriodId");

                    b.HasIndex("ClosedById");

                    b.HasIndex("FiscalYearId");

                    b.ToTable("Period");
                });

            modelBuilder.Entity("InventoryAPI.Models.PeriodClose", b =>
                {
                    b.Property<int>("PeriodCloseId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PeriodCloseId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PeriodCloseId"));

                    b.Property<decimal?>("AverageCost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("AverageCost");

                    b.Property<int>("ClosedById")
                        .HasColumnType("int")
                        .HasColumnName("ClosedById");

                    b.Property<DateTime>("ClosedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ClosedDate");

                    b.Property<decimal>("ClosingQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ClosingQuantity");

                    b.Property<decimal>("ClosingValue")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ClosingValue");

                    b.Property<int>("CostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("CostPrice");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("FiscalYearId")
                        .HasColumnType("int")
                        .HasColumnName("FiscalYearId");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<decimal>("OpeningQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("OpeningQuantity");

                    b.Property<decimal>("OpeningValue")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("OpeningValue");

                    b.Property<int>("PeriodId")
                        .HasColumnType("int")
                        .HasColumnName("PeriodId");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    b.Property<int?>("StockTakeId")
                        .HasColumnType("int")
                        .HasColumnName("StockTakeId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("PeriodCloseId");

                    b.HasIndex("ClosedById");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("PeriodId");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockTakeId");

                    b.ToTable("PeriodClose");
                });

            modelBuilder.Entity("InventoryAPI.Models.Product", b =>
                {
                    b.Property<int>("ProductId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductId"));

                    b.Property<bool>("AllowDiscount")
                        .HasColumnType("bit")
                        .HasColumnName("AllowDiscount");

                    b.Property<decimal?>("AverageCost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("AverageCost");

                    b.Property<int?>("BrandId")
                        .HasColumnType("int")
                        .HasColumnName("BrandId");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Code");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("CostPrice");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("DepartmentId");

                    b.Property<int?>("GroupId")
                        .HasColumnType("int")
                        .HasColumnName("GroupId");

                    b.Property<bool>("HasExpiry")
                        .HasColumnType("bit")
                        .HasColumnName("HasExpiry");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsProduction")
                        .HasColumnType("bit")
                        .HasColumnName("IsProduction");

                    b.Property<bool>("IsRecipe")
                        .HasColumnType("bit")
                        .HasColumnName("IsRecipe");

                    b.Property<bool>("IsSaleable")
                        .HasColumnType("bit")
                        .HasColumnName("IsSaleable");

                    b.Property<bool>("IsStockItem")
                        .HasColumnType("bit")
                        .HasColumnName("IsStockItem");

                    b.Property<decimal?>("MaxStock")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("MaxStock");

                    b.Property<decimal?>("MinStock")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("MinStock");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<decimal?>("ReorderPoint")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ReorderPoint");

                    b.Property<decimal?>("SalesPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("SalesPrice");

                    b.Property<decimal?>("SalesUnitConversionFactor")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("SalesUnitConversionFactor");

                    b.Property<int?>("SalesUnitId")
                        .HasColumnType("int")
                        .HasColumnName("SalesUnitId");

                    b.Property<int?>("SubGroupId")
                        .HasColumnType("int")
                        .HasColumnName("SubGroupId");

                    b.Property<int?>("TaxId")
                        .HasColumnType("int")
                        .HasColumnName("TaxId");

                    b.Property<int?>("UnitGroupId")
                        .HasColumnType("int")
                        .HasColumnName("UnitGroupId");

                    b.Property<int?>("UnitId")
                        .HasColumnType("int")
                        .HasColumnName("UnitId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("ProductId");

                    b.HasIndex("BrandId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("GroupId");

                    b.HasIndex("SalesUnitId");

                    b.HasIndex("SubGroupId");

                    b.HasIndex("TaxId");

                    b.HasIndex("UnitId");

                    b.ToTable("Product");
                });

            modelBuilder.Entity("InventoryAPI.Models.ProductCostCenterLink", b =>
                {
                    b.Property<int>("LinkId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LinkId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LinkId"));

                    b.Property<int>("CostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<decimal?>("MaximumStock")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("MaximumStock");

                    b.Property<decimal?>("MinimumStock")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("MinimumStock");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    b.Property<decimal?>("ReorderPoint")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ReorderPoint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("LinkId");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductCostCenterLink");
                });

            modelBuilder.Entity("InventoryAPI.Models.ProductGroup", b =>
                {
                    b.Property<int>("GroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("GroupId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("GroupId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("DepartmentId");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("GroupId");

                    b.HasIndex("DepartmentId");

                    b.ToTable("ProductGroup");
                });

            modelBuilder.Entity("InventoryAPI.Models.ProductSubGroup", b =>
                {
                    b.Property<int>("SubGroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SubGroupId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SubGroupId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int?>("GroupId")
                        .HasColumnType("int")
                        .HasColumnName("GroupId");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("SubGroupId");

                    b.HasIndex("GroupId");

                    b.ToTable("ProductSubGroup");
                });

            modelBuilder.Entity("InventoryAPI.Models.Recipe", b =>
                {
                    b.Property<int>("RecipeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("RecipeId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RecipeId"));

                    b.Property<decimal>("Cost")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("Cost");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Name");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    b.Property<int>("UnitId")
                        .HasColumnType("int")
                        .HasColumnName("UnitId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<decimal>("Yield")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Yield");

                    b.HasKey("RecipeId");

                    b.HasIndex("ProductId");

                    b.HasIndex("UnitId");

                    b.ToTable("Recipes");
                });

            modelBuilder.Entity("InventoryAPI.Models.RecipeIngredient", b =>
                {
                    b.Property<int>("RecipeIngredientId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("RecipeIngredientId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RecipeIngredientId"));

                    b.Property<decimal>("Cost")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("Cost");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    b.Property<decimal>("Quantity")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("Quantity");

                    b.Property<int>("RecipeId")
                        .HasColumnType("int")
                        .HasColumnName("RecipeId");

                    b.Property<int>("UnitId")
                        .HasColumnType("int")
                        .HasColumnName("UnitId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("RecipeIngredientId");

                    b.HasIndex("ProductId");

                    b.HasIndex("RecipeId");

                    b.HasIndex("UnitId");

                    b.ToTable("RecipeIngredients");
                });

            modelBuilder.Entity("InventoryAPI.Models.Role", b =>
                {
                    b.Property<int>("RoleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("RoleId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RoleId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("DefaultCanApproveCostCenters")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanApproveCostCenters");

                    b.Property<bool>("DefaultCanApproveTransactions")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanApproveTransactions");

                    b.Property<bool>("DefaultCanCountStockTakes")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanCountStockTakes");

                    b.Property<bool>("DefaultCanCreateCostCenters")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanCreateCostCenters");

                    b.Property<bool>("DefaultCanCreateStockTakes")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanCreateStockTakes");

                    b.Property<bool>("DefaultCanCreateTransactions")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanCreateTransactions");

                    b.Property<bool>("DefaultCanDeleteCostCenters")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanDeleteCostCenters");

                    b.Property<bool>("DefaultCanDeleteStockTakes")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanDeleteStockTakes");

                    b.Property<bool>("DefaultCanDeleteTransactions")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanDeleteTransactions");

                    b.Property<bool>("DefaultCanEditCostCenters")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanEditCostCenters");

                    b.Property<bool>("DefaultCanEditStockTakes")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanEditStockTakes");

                    b.Property<bool>("DefaultCanEditTransactions")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanEditTransactions");

                    b.Property<bool>("DefaultCanExportReports")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanExportReports");

                    b.Property<bool>("DefaultCanFinalizeStockTakes")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanFinalizeStockTakes");

                    b.Property<bool>("DefaultCanProcessTransactions")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanProcessTransactions");

                    b.Property<bool>("DefaultCanRejectTransactions")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanRejectTransactions");

                    b.Property<bool>("DefaultCanReopenStockTakes")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanReopenStockTakes");

                    b.Property<bool>("DefaultCanScheduleReports")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanScheduleReports");

                    b.Property<bool>("DefaultCanViewCostCenters")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanViewCostCenters");

                    b.Property<bool>("DefaultCanViewReports")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanViewReports");

                    b.Property<bool>("DefaultCanViewStockTakes")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanViewStockTakes");

                    b.Property<bool>("DefaultCanViewTransactions")
                        .HasColumnType("bit")
                        .HasColumnName("DefaultCanViewTransactions");

                    b.Property<decimal?>("DefaultMaxApprovalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("DefaultMaxApprovalAmount");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("bit")
                        .HasColumnName("IsAdmin");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("bit")
                        .HasColumnName("IsSystem");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("RoleId");

                    b.ToTable("Role");
                });

            modelBuilder.Entity("InventoryAPI.Models.RoleFormActionAccess", b =>
                {
                    b.Property<int>("AccessId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("AccessId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AccessId"));

                    b.Property<int>("ActionId")
                        .HasColumnType("int")
                        .HasColumnName("ActionId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("HasAccess")
                        .HasColumnType("bit")
                        .HasColumnName("HasAccess");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("RoleId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("AccessId");

                    b.HasIndex("ActionId");

                    b.HasIndex("RoleId");

                    b.ToTable("RoleFormActionAccess");
                });

            modelBuilder.Entity("InventoryAPI.Models.Shift", b =>
                {
                    b.Property<int>("ShiftId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ShiftId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ShiftId"));

                    b.Property<decimal?>("ClosingBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ClosingBalance");

                    b.Property<int>("CostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("EndTime");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("Notes");

                    b.Property<decimal>("OpeningBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("OpeningBalance");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("StartTime");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Status");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("ShiftId");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("UserId");

                    b.ToTable("Shifts");
                });

            modelBuilder.Entity("InventoryAPI.Models.StockOnHand", b =>
                {
                    b.Property<int>("StockId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("StockId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StockId"));

                    b.Property<decimal?>("AverageCost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("AverageCost");

                    b.Property<decimal>("BaseQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("BaseQuantity");

                    b.Property<int>("CostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("CostPrice");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastUpdated");

                    b.Property<decimal?>("NetCost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("NetCost");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Quantity");

                    b.Property<decimal?>("ReturnVariance")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ReturnVariance");

                    b.Property<decimal?>("SalesPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("SalesPrice");

                    b.Property<int?>("UnitId")
                        .HasColumnType("int")
                        .HasColumnName("UnitId");

                    b.HasKey("StockId");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("ProductId");

                    b.HasIndex("UnitId");

                    b.ToTable("StockOnHand");
                });

            modelBuilder.Entity("InventoryAPI.Models.StockTakeDetail", b =>
                {
                    b.Property<int>("StockTakeDetailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("StockTakeDetailId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StockTakeDetailId"));

                    b.Property<decimal?>("ActualQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ActualQuantity");

                    b.Property<int?>("BatchId")
                        .HasColumnType("int")
                        .HasColumnName("BatchId");

                    b.Property<decimal?>("ClosingQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("ClosingQuantity");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("CostPrice");

                    b.Property<int?>("CountedById")
                        .HasColumnType("int")
                        .HasColumnName("CountedById");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("LastCountDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastCountDate");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<decimal?>("OpeningQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("OpeningQuantity");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    b.Property<int>("StockTakeId")
                        .HasColumnType("int")
                        .HasColumnName("StockTakeId");

                    b.Property<int?>("StockTakeTypeId")
                        .HasColumnType("int")
                        .HasColumnName("StockTakeTypeId");

                    b.Property<decimal>("SystemQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("SystemQuantity");

                    b.Property<int>("UnitId")
                        .HasColumnType("int")
                        .HasColumnName("UnitId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<decimal?>("VarianceQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("VarianceQuantity");

                    b.Property<decimal?>("VarianceValue")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("VarianceValue");

                    b.HasKey("StockTakeDetailId");

                    b.HasIndex("BatchId");

                    b.HasIndex("CountedById");

                    b.HasIndex("ProductId");

                    b.HasIndex("StockTakeId");

                    b.HasIndex("StockTakeTypeId");

                    b.HasIndex("UnitId");

                    b.ToTable("StockTakeDetail");
                });

            modelBuilder.Entity("InventoryAPI.Models.StockTakeHeader", b =>
                {
                    b.Property<int>("StockTakeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("StockTakeId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StockTakeId"));

                    b.Property<int?>("CompletedById")
                        .HasColumnType("int")
                        .HasColumnName("CompletedById");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("CompletedDate");

                    b.Property<int>("CostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("CreatedById")
                        .HasColumnType("int")
                        .HasColumnName("CreatedById");

                    b.Property<int?>("FinalizedById")
                        .HasColumnType("int")
                        .HasColumnName("FinalizedById");

                    b.Property<DateTime?>("FinalizedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("FinalizedDate");

                    b.Property<int>("FiscalYearId")
                        .HasColumnType("int")
                        .HasColumnName("FiscalYearId");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("bit")
                        .HasColumnName("IsFinalized");

                    b.Property<bool>("IsReopened")
                        .HasColumnType("bit")
                        .HasColumnName("IsReopened");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<int?>("PeriodId")
                        .HasColumnType("int")
                        .HasColumnName("PeriodId");

                    b.Property<int?>("ReopenedById")
                        .HasColumnType("int")
                        .HasColumnName("ReopenedById");

                    b.Property<DateTime?>("ReopenedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ReopenedDate");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Status");

                    b.Property<DateTime>("StockTakeDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("StockTakeDate");

                    b.Property<string>("StockTakeNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("StockTakeNumber");

                    b.Property<int?>("StockTakeTypeId")
                        .HasColumnType("int")
                        .HasColumnName("StockTakeTypeId");

                    b.Property<decimal?>("TotalVariance")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TotalVariance");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("StockTakeId");

                    b.HasIndex("CompletedById");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FinalizedById");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("PeriodId");

                    b.HasIndex("ReopenedById");

                    b.HasIndex("StockTakeTypeId");

                    b.ToTable("StockTakeHeader");
                });

            modelBuilder.Entity("InventoryAPI.Models.StockTakeType", b =>
                {
                    b.Property<int>("StockTakeTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("StockTakeTypeId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StockTakeTypeId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("StockTakeTypeId");

                    b.ToTable("StockTakeType");
                });

            modelBuilder.Entity("InventoryAPI.Models.Store", b =>
                {
                    b.Property<int>("StoreId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("StoreId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StoreId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsSalesPoint")
                        .HasColumnType("bit")
                        .HasColumnName("IsSalesPoint");

                    b.Property<int?>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("LocationId");

                    b.Property<string>("LogoPath")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("LogoPath");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("StoreId");

                    b.HasIndex("LocationId");

                    b.ToTable("Store");
                });

            modelBuilder.Entity("InventoryAPI.Models.Supplier", b =>
                {
                    b.Property<int>("SupplierId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SupplierId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SupplierId"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Address");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("City");

                    b.Property<string>("ContactPerson")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("ContactPerson");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Country");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Email");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Name");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Phone");

                    b.Property<string>("PostalCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("PostalCode");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("State");

                    b.Property<string>("TaxNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("TaxNumber");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("SupplierId");

                    b.ToTable("Supplier");
                });

            modelBuilder.Entity("InventoryAPI.Models.Tax", b =>
                {
                    b.Property<int>("TaxId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("TaxId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TaxId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit")
                        .HasColumnName("IsDefault");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Name");

                    b.Property<decimal>("Rate")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Rate");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("TaxId");

                    b.ToTable("Tax");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionDetail", b =>
                {
                    b.Property<int>("TransactionDetailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("TransactionDetailId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TransactionDetailId"));

                    b.Property<int?>("BatchId")
                        .HasColumnType("int")
                        .HasColumnName("BatchId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<decimal?>("DiscountAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("DiscountAmount");

                    b.Property<decimal?>("DiscountPercentage")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("DiscountPercentage");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsRecipe")
                        .HasColumnType("bit")
                        .HasColumnName("IsRecipe");

                    b.Property<decimal>("LineTotal")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("LineTotal");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("Quantity");

                    b.Property<decimal?>("TaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TaxAmount");

                    b.Property<int?>("TaxId")
                        .HasColumnType("int")
                        .HasColumnName("TaxId");

                    b.Property<decimal?>("TaxRate")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TaxRate");

                    b.Property<int>("TransactionId")
                        .HasColumnType("int")
                        .HasColumnName("TransactionId");

                    b.Property<int>("UnitId")
                        .HasColumnType("int")
                        .HasColumnName("UnitId");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("UnitPrice");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("TransactionDetailId");

                    b.HasIndex("BatchId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TaxId");

                    b.HasIndex("TransactionId");

                    b.HasIndex("UnitId");

                    b.ToTable("TransactionDetail");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionHeader", b =>
                {
                    b.Property<int>("TransactionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("TransactionId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TransactionId"));

                    b.Property<int?>("ApprovedById")
                        .HasColumnType("int")
                        .HasColumnName("ApprovedById");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ApprovedDate");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("CreatedById")
                        .HasColumnType("int")
                        .HasColumnName("CreatedById");

                    b.Property<int?>("DestinationCostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("DestinationCostCenterId");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("DiscountAmount");

                    b.Property<decimal>("DiscountPercentage")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("DiscountPercentage");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<int?>("ProcessId")
                        .HasColumnType("int")
                        .HasColumnName("ProcessId");

                    b.Property<string>("ReferenceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ReferenceNumber");

                    b.Property<int?>("ShiftId")
                        .HasColumnType("int");

                    b.Property<int?>("SourceCostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("SourceCostCenterId");

                    b.Property<int?>("StageTypeId")
                        .HasColumnType("int")
                        .HasColumnName("StageTypeId");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("Status");

                    b.Property<decimal>("SubTotal")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("SubTotal");

                    b.Property<int?>("SupplierId")
                        .HasColumnType("int")
                        .HasColumnName("SupplierId");

                    b.Property<decimal>("TaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TaxAmount");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("TotalAmount");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("TransactionDate");

                    b.Property<string>("TransactionNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("TransactionNumber");

                    b.Property<int>("TransactionTypeId")
                        .HasColumnType("int")
                        .HasColumnName("TransactionTypeId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("TransactionId");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DestinationCostCenterId");

                    b.HasIndex("ProcessId");

                    b.HasIndex("ShiftId");

                    b.HasIndex("SourceCostCenterId");

                    b.HasIndex("StageTypeId");

                    b.HasIndex("SupplierId");

                    b.HasIndex("TransactionTypeId");

                    b.ToTable("TransactionHeader");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionProcess", b =>
                {
                    b.Property<int>("ProcessId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ProcessId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProcessId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("CreatedById")
                        .HasColumnType("int")
                        .HasColumnName("CreatedById");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("ProcessNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ProcessNumber");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("ProcessId");

                    b.HasIndex("CreatedById");

                    b.ToTable("TransactionProcess");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionStageType", b =>
                {
                    b.Property<int>("StageTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("StageTypeId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StageTypeId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Name");

                    b.Property<int>("Sequence")
                        .HasColumnType("int")
                        .HasColumnName("Sequence");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("StageTypeId");

                    b.ToTable("TransactionStageType");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionType", b =>
                {
                    b.Property<int>("TransactionTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("TransactionTypeId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TransactionTypeId"));

                    b.Property<bool>("AffectsInventory")
                        .HasColumnType("bit")
                        .HasColumnName("AffectsInventory");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsSale")
                        .HasColumnType("bit")
                        .HasColumnName("IsSale");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Name");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("TransactionTypeId");

                    b.ToTable("TransactionType");
                });

            modelBuilder.Entity("InventoryAPI.Models.Unit", b =>
                {
                    b.Property<int>("UnitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("UnitId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UnitId"));

                    b.Property<string>("Abbreviation")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("Abbreviation");

                    b.Property<decimal>("BaseConversionFactor")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("BaseConversionFactor");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Name");

                    b.Property<int?>("UnitGroupId")
                        .HasColumnType("int")
                        .HasColumnName("UnitGroupId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.HasKey("UnitId");

                    b.ToTable("Unit");
                });

            modelBuilder.Entity("InventoryAPI.Models.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("DefaultLanguage")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("DefaultLanguage");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)")
                        .HasColumnName("Email");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("FirstName");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastLoginDate");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("LastName");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("PasswordHash");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("PhoneNumber");

                    b.Property<int>("RoleId")
                        .HasColumnType("int")
                        .HasColumnName("RoleId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("Username");

                    b.HasKey("UserId");

                    b.HasIndex("RoleId");

                    b.ToTable("User");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserCostCenterAccess", b =>
                {
                    b.Property<int>("AccessId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("AccessId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AccessId"));

                    b.Property<bool>("CanApprove")
                        .HasColumnType("bit")
                        .HasColumnName("CanApprove");

                    b.Property<bool>("CanCreate")
                        .HasColumnType("bit")
                        .HasColumnName("CanCreate");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("bit")
                        .HasColumnName("CanDelete");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("bit")
                        .HasColumnName("CanEdit");

                    b.Property<bool>("CanView")
                        .HasColumnType("bit")
                        .HasColumnName("CanView");

                    b.Property<int>("CostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("AccessId");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("UserId");

                    b.ToTable("UserCostCenterAccess");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserFormActionAccess", b =>
                {
                    b.Property<int>("AccessId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("AccessId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AccessId"));

                    b.Property<int>("ActionId")
                        .HasColumnType("int")
                        .HasColumnName("ActionId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("HasAccess")
                        .HasColumnType("bit")
                        .HasColumnName("HasAccess");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("AccessId");

                    b.HasIndex("ActionId");

                    b.HasIndex("UserId");

                    b.ToTable("UserFormActionAccess");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserProductAccess", b =>
                {
                    b.Property<int>("ProductAccessId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ProductAccessId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductAccessId"));

                    b.Property<bool>("CanAdjustPrices")
                        .HasColumnType("bit")
                        .HasColumnName("CanAdjustPrices");

                    b.Property<bool>("CanCreate")
                        .HasColumnType("bit")
                        .HasColumnName("CanCreate");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("bit")
                        .HasColumnName("CanDelete");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("bit")
                        .HasColumnName("CanEdit");

                    b.Property<bool>("CanManageRecipes")
                        .HasColumnType("bit")
                        .HasColumnName("CanManageRecipes");

                    b.Property<bool>("CanView")
                        .HasColumnType("bit")
                        .HasColumnName("CanView");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("DepartmentId");

                    b.Property<int?>("GroupId")
                        .HasColumnType("int")
                        .HasColumnName("GroupId");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<int?>("ProductId")
                        .HasColumnType("int")
                        .HasColumnName("ProductId");

                    b.Property<int?>("SubGroupId")
                        .HasColumnType("int")
                        .HasColumnName("SubGroupId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("ProductAccessId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("GroupId");

                    b.HasIndex("ProductId");

                    b.HasIndex("SubGroupId");

                    b.HasIndex("UserId");

                    b.ToTable("UserProductAccess");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserReportAccess", b =>
                {
                    b.Property<int>("ReportAccessId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ReportAccessId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ReportAccessId"));

                    b.Property<bool>("CanExport")
                        .HasColumnType("bit")
                        .HasColumnName("CanExport");

                    b.Property<bool>("CanSchedule")
                        .HasColumnType("bit")
                        .HasColumnName("CanSchedule");

                    b.Property<bool>("CanView")
                        .HasColumnType("bit")
                        .HasColumnName("CanView");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<string>("ReportCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ReportCode");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("ReportAccessId");

                    b.HasIndex("UserId");

                    b.ToTable("UserReportAccess");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserStockTakeAccess", b =>
                {
                    b.Property<int>("StockTakeAccessId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("StockTakeAccessId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StockTakeAccessId"));

                    b.Property<bool>("CanCount")
                        .HasColumnType("bit")
                        .HasColumnName("CanCount");

                    b.Property<bool>("CanCreate")
                        .HasColumnType("bit")
                        .HasColumnName("CanCreate");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("bit")
                        .HasColumnName("CanDelete");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("bit")
                        .HasColumnName("CanEdit");

                    b.Property<bool>("CanFinalize")
                        .HasColumnType("bit")
                        .HasColumnName("CanFinalize");

                    b.Property<bool>("CanReopen")
                        .HasColumnType("bit")
                        .HasColumnName("CanReopen");

                    b.Property<bool>("CanView")
                        .HasColumnType("bit")
                        .HasColumnName("CanView");

                    b.Property<int?>("CostCenterId")
                        .HasColumnType("int")
                        .HasColumnName("CostCenterId");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("StockTakeAccessId");

                    b.HasIndex("CostCenterId");

                    b.HasIndex("UserId");

                    b.ToTable("UserStockTakeAccess");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserTransactionAccess", b =>
                {
                    b.Property<int>("TransactionAccessId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("TransactionAccessId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TransactionAccessId"));

                    b.Property<bool>("CanApprove")
                        .HasColumnType("bit")
                        .HasColumnName("CanApprove");

                    b.Property<bool>("CanCreate")
                        .HasColumnType("bit")
                        .HasColumnName("CanCreate");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("bit")
                        .HasColumnName("CanDelete");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("bit")
                        .HasColumnName("CanEdit");

                    b.Property<bool>("CanProcess")
                        .HasColumnType("bit")
                        .HasColumnName("CanProcess");

                    b.Property<bool>("CanReject")
                        .HasColumnType("bit")
                        .HasColumnName("CanReject");

                    b.Property<bool>("CanView")
                        .HasColumnType("bit")
                        .HasColumnName("CanView");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<decimal?>("MaxApprovalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("MaxApprovalAmount");

                    b.Property<int?>("StageTypeId")
                        .HasColumnType("int")
                        .HasColumnName("StageTypeId");

                    b.Property<int?>("TransactionTypeId")
                        .HasColumnType("int")
                        .HasColumnName("TransactionTypeId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("TransactionAccessId");

                    b.HasIndex("StageTypeId");

                    b.HasIndex("TransactionTypeId");

                    b.HasIndex("UserId");

                    b.ToTable("UserTransactionAccess");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserTransactionTypeAccess", b =>
                {
                    b.Property<int>("TransactionTypeAccessId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("TransactionTypeAccessId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TransactionTypeAccessId"));

                    b.Property<bool>("CanApprove")
                        .HasColumnType("bit")
                        .HasColumnName("CanApprove");

                    b.Property<bool>("CanCreate")
                        .HasColumnType("bit")
                        .HasColumnName("CanCreate");

                    b.Property<bool>("CanDelete")
                        .HasColumnType("bit")
                        .HasColumnName("CanDelete");

                    b.Property<bool>("CanEdit")
                        .HasColumnType("bit")
                        .HasColumnName("CanEdit");

                    b.Property<bool>("CanReject")
                        .HasColumnType("bit")
                        .HasColumnName("CanReject");

                    b.Property<bool>("CanView")
                        .HasColumnType("bit")
                        .HasColumnName("CanView");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreatedAt");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("IsActive");

                    b.Property<decimal?>("MaxApprovalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("MaxApprovalAmount");

                    b.Property<int>("TransactionTypeId")
                        .HasColumnType("int")
                        .HasColumnName("TransactionTypeId");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("UpdatedAt");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("TransactionTypeAccessId");

                    b.HasIndex("TransactionTypeId");

                    b.HasIndex("UserId");

                    b.ToTable("UserTransactionTypeAccess");
                });

            modelBuilder.Entity("InventoryAPI.Models.Batch", b =>
                {
                    b.HasOne("InventoryAPI.Models.CostCenter", "CostCenter")
                        .WithMany("Batches")
                        .HasForeignKey("CostCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Product", "Product")
                        .WithMany("Batches")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Unit", "Unit")
                        .WithMany()
                        .HasForeignKey("UnitId");

                    b.Navigation("CostCenter");

                    b.Navigation("Product");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("InventoryAPI.Models.CostCenter", b =>
                {
                    b.HasOne("InventoryAPI.Models.Store", "Store")
                        .WithMany("CostCenters")
                        .HasForeignKey("StoreId");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("InventoryAPI.Models.CostCenterFiscalYear", b =>
                {
                    b.HasOne("InventoryAPI.Models.CostCenter", "CostCenter")
                        .WithMany("CostCenterFiscalYears")
                        .HasForeignKey("CostCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.FiscalYear", "FiscalYear")
                        .WithMany("CostCenterFiscalYears")
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CostCenter");

                    b.Navigation("FiscalYear");
                });

            modelBuilder.Entity("InventoryAPI.Models.FiscalYear", b =>
                {
                    b.HasOne("InventoryAPI.Models.User", "ClosedBy")
                        .WithMany()
                        .HasForeignKey("ClosedById");

                    b.Navigation("ClosedBy");
                });

            modelBuilder.Entity("InventoryAPI.Models.FormAction", b =>
                {
                    b.HasOne("InventoryAPI.Models.ApplicationForm", "ApplicationForm")
                        .WithMany("FormActions")
                        .HasForeignKey("FormId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApplicationForm");
                });

            modelBuilder.Entity("InventoryAPI.Models.Payment", b =>
                {
                    b.HasOne("InventoryAPI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Currency", "Currency")
                        .WithMany("Payments")
                        .HasForeignKey("CurrencyId");

                    b.HasOne("InventoryAPI.Models.PaymentMethod", "PaymentMethod")
                        .WithMany("Payments")
                        .HasForeignKey("PaymentMethodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Shift", "Shift")
                        .WithMany("Payments")
                        .HasForeignKey("ShiftId");

                    b.HasOne("InventoryAPI.Models.TransactionHeader", null)
                        .WithMany("Payments")
                        .HasForeignKey("TransactionHeaderTransactionId");

                    b.HasOne("InventoryAPI.Models.TransactionHeader", "Transaction")
                        .WithMany()
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Currency");

                    b.Navigation("PaymentMethod");

                    b.Navigation("Shift");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("InventoryAPI.Models.Period", b =>
                {
                    b.HasOne("InventoryAPI.Models.User", "ClosedBy")
                        .WithMany()
                        .HasForeignKey("ClosedById");

                    b.HasOne("InventoryAPI.Models.FiscalYear", "FiscalYear")
                        .WithMany("Periods")
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ClosedBy");

                    b.Navigation("FiscalYear");
                });

            modelBuilder.Entity("InventoryAPI.Models.PeriodClose", b =>
                {
                    b.HasOne("InventoryAPI.Models.User", "ClosedBy")
                        .WithMany()
                        .HasForeignKey("ClosedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.CostCenter", "CostCenter")
                        .WithMany("PeriodCloses")
                        .HasForeignKey("CostCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.FiscalYear", "FiscalYear")
                        .WithMany("PeriodCloses")
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Period", "Period")
                        .WithMany("PeriodCloses")
                        .HasForeignKey("PeriodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Product", "Product")
                        .WithMany("PeriodCloses")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.StockTakeHeader", "StockTakeHeader")
                        .WithMany("PeriodCloses")
                        .HasForeignKey("StockTakeId");

                    b.Navigation("ClosedBy");

                    b.Navigation("CostCenter");

                    b.Navigation("FiscalYear");

                    b.Navigation("Period");

                    b.Navigation("Product");

                    b.Navigation("StockTakeHeader");
                });

            modelBuilder.Entity("InventoryAPI.Models.Product", b =>
                {
                    b.HasOne("InventoryAPI.Models.Brand", "Brand")
                        .WithMany("Products")
                        .HasForeignKey("BrandId");

                    b.HasOne("InventoryAPI.Models.Department", "Department")
                        .WithMany("Products")
                        .HasForeignKey("DepartmentId");

                    b.HasOne("InventoryAPI.Models.ProductGroup", "ProductGroup")
                        .WithMany("Products")
                        .HasForeignKey("GroupId");

                    b.HasOne("InventoryAPI.Models.Unit", "SalesUnit")
                        .WithMany("ProductsWithSalesUnit")
                        .HasForeignKey("SalesUnitId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("InventoryAPI.Models.ProductSubGroup", "ProductSubGroup")
                        .WithMany("Products")
                        .HasForeignKey("SubGroupId");

                    b.HasOne("InventoryAPI.Models.Tax", "Tax")
                        .WithMany("Products")
                        .HasForeignKey("TaxId");

                    b.HasOne("InventoryAPI.Models.Unit", "Unit")
                        .WithMany("Products")
                        .HasForeignKey("UnitId");

                    b.Navigation("Brand");

                    b.Navigation("Department");

                    b.Navigation("ProductGroup");

                    b.Navigation("ProductSubGroup");

                    b.Navigation("SalesUnit");

                    b.Navigation("Tax");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("InventoryAPI.Models.ProductCostCenterLink", b =>
                {
                    b.HasOne("InventoryAPI.Models.CostCenter", "CostCenter")
                        .WithMany("ProductCostCenterLinks")
                        .HasForeignKey("CostCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Product", "Product")
                        .WithMany("ProductCostCenterLinks")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CostCenter");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("InventoryAPI.Models.ProductGroup", b =>
                {
                    b.HasOne("InventoryAPI.Models.Department", "Department")
                        .WithMany("ProductGroups")
                        .HasForeignKey("DepartmentId");

                    b.Navigation("Department");
                });

            modelBuilder.Entity("InventoryAPI.Models.ProductSubGroup", b =>
                {
                    b.HasOne("InventoryAPI.Models.ProductGroup", "ProductGroup")
                        .WithMany("ProductSubGroups")
                        .HasForeignKey("GroupId");

                    b.Navigation("ProductGroup");
                });

            modelBuilder.Entity("InventoryAPI.Models.Recipe", b =>
                {
                    b.HasOne("InventoryAPI.Models.Product", "Product")
                        .WithMany("Recipes")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Unit", "Unit")
                        .WithMany("Recipes")
                        .HasForeignKey("UnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("InventoryAPI.Models.RecipeIngredient", b =>
                {
                    b.HasOne("InventoryAPI.Models.Product", "Product")
                        .WithMany("RecipeIngredients")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Recipe", "Recipe")
                        .WithMany("RecipeIngredients")
                        .HasForeignKey("RecipeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Unit", "Unit")
                        .WithMany("RecipeIngredients")
                        .HasForeignKey("UnitId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Recipe");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("InventoryAPI.Models.RoleFormActionAccess", b =>
                {
                    b.HasOne("InventoryAPI.Models.FormAction", "FormAction")
                        .WithMany("RoleFormActionAccesses")
                        .HasForeignKey("ActionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Role", "Role")
                        .WithMany("RoleFormActionAccesses")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FormAction");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("InventoryAPI.Models.Shift", b =>
                {
                    b.HasOne("InventoryAPI.Models.CostCenter", "CostCenter")
                        .WithMany()
                        .HasForeignKey("CostCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CostCenter");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InventoryAPI.Models.StockOnHand", b =>
                {
                    b.HasOne("InventoryAPI.Models.CostCenter", "CostCenter")
                        .WithMany("StockOnHands")
                        .HasForeignKey("CostCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Product", "Product")
                        .WithMany("StockOnHands")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Unit", "Unit")
                        .WithMany("StockOnHands")
                        .HasForeignKey("UnitId");

                    b.Navigation("CostCenter");

                    b.Navigation("Product");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("InventoryAPI.Models.StockTakeDetail", b =>
                {
                    b.HasOne("InventoryAPI.Models.Batch", "Batch")
                        .WithMany("StockTakeDetails")
                        .HasForeignKey("BatchId");

                    b.HasOne("InventoryAPI.Models.User", "CountedBy")
                        .WithMany()
                        .HasForeignKey("CountedById");

                    b.HasOne("InventoryAPI.Models.Product", "Product")
                        .WithMany("StockTakeDetails")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.StockTakeHeader", "StockTakeHeader")
                        .WithMany("StockTakeDetails")
                        .HasForeignKey("StockTakeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.StockTakeType", "StockTakeType")
                        .WithMany("StockTakeDetails")
                        .HasForeignKey("StockTakeTypeId");

                    b.HasOne("InventoryAPI.Models.Unit", "Unit")
                        .WithMany()
                        .HasForeignKey("UnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Batch");

                    b.Navigation("CountedBy");

                    b.Navigation("Product");

                    b.Navigation("StockTakeHeader");

                    b.Navigation("StockTakeType");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("InventoryAPI.Models.StockTakeHeader", b =>
                {
                    b.HasOne("InventoryAPI.Models.User", "CompletedBy")
                        .WithMany()
                        .HasForeignKey("CompletedById");

                    b.HasOne("InventoryAPI.Models.CostCenter", "CostCenter")
                        .WithMany("StockTakeHeaders")
                        .HasForeignKey("CostCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.User", "FinalizedBy")
                        .WithMany()
                        .HasForeignKey("FinalizedById");

                    b.HasOne("InventoryAPI.Models.FiscalYear", "FiscalYear")
                        .WithMany("StockTakeHeaders")
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Period", "Period")
                        .WithMany("StockTakeHeaders")
                        .HasForeignKey("PeriodId");

                    b.HasOne("InventoryAPI.Models.User", "ReopenedBy")
                        .WithMany()
                        .HasForeignKey("ReopenedById");

                    b.HasOne("InventoryAPI.Models.StockTakeType", "StockTakeType")
                        .WithMany("StockTakeHeaders")
                        .HasForeignKey("StockTakeTypeId");

                    b.Navigation("CompletedBy");

                    b.Navigation("CostCenter");

                    b.Navigation("CreatedBy");

                    b.Navigation("FinalizedBy");

                    b.Navigation("FiscalYear");

                    b.Navigation("Period");

                    b.Navigation("ReopenedBy");

                    b.Navigation("StockTakeType");
                });

            modelBuilder.Entity("InventoryAPI.Models.Store", b =>
                {
                    b.HasOne("InventoryAPI.Models.Location", "Location")
                        .WithMany("Stores")
                        .HasForeignKey("LocationId");

                    b.Navigation("Location");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionDetail", b =>
                {
                    b.HasOne("InventoryAPI.Models.Batch", "Batch")
                        .WithMany("TransactionDetails")
                        .HasForeignKey("BatchId");

                    b.HasOne("InventoryAPI.Models.Product", "Product")
                        .WithMany("TransactionDetails")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Tax", "Tax")
                        .WithMany("TransactionDetails")
                        .HasForeignKey("TaxId");

                    b.HasOne("InventoryAPI.Models.TransactionHeader", "TransactionHeader")
                        .WithMany("TransactionDetails")
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.Unit", "Unit")
                        .WithMany("TransactionDetails")
                        .HasForeignKey("UnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Batch");

                    b.Navigation("Product");

                    b.Navigation("Tax");

                    b.Navigation("TransactionHeader");

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionHeader", b =>
                {
                    b.HasOne("InventoryAPI.Models.User", "ApprovedBy")
                        .WithMany()
                        .HasForeignKey("ApprovedById");

                    b.HasOne("InventoryAPI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.CostCenter", "DestinationCostCenter")
                        .WithMany("DestinationTransactions")
                        .HasForeignKey("DestinationCostCenterId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("InventoryAPI.Models.TransactionProcess", "TransactionProcess")
                        .WithMany("TransactionHeaders")
                        .HasForeignKey("ProcessId");

                    b.HasOne("InventoryAPI.Models.Shift", null)
                        .WithMany("TransactionHeaders")
                        .HasForeignKey("ShiftId");

                    b.HasOne("InventoryAPI.Models.CostCenter", "SourceCostCenter")
                        .WithMany("SourceTransactions")
                        .HasForeignKey("SourceCostCenterId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("InventoryAPI.Models.TransactionStageType", "StageType")
                        .WithMany("TransactionHeaders")
                        .HasForeignKey("StageTypeId");

                    b.HasOne("InventoryAPI.Models.Supplier", "Supplier")
                        .WithMany("TransactionHeaders")
                        .HasForeignKey("SupplierId");

                    b.HasOne("InventoryAPI.Models.TransactionType", "TransactionType")
                        .WithMany("TransactionHeaders")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovedBy");

                    b.Navigation("CreatedBy");

                    b.Navigation("DestinationCostCenter");

                    b.Navigation("SourceCostCenter");

                    b.Navigation("StageType");

                    b.Navigation("Supplier");

                    b.Navigation("TransactionProcess");

                    b.Navigation("TransactionType");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionProcess", b =>
                {
                    b.HasOne("InventoryAPI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("InventoryAPI.Models.User", b =>
                {
                    b.HasOne("InventoryAPI.Models.Role", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserCostCenterAccess", b =>
                {
                    b.HasOne("InventoryAPI.Models.CostCenter", "CostCenter")
                        .WithMany("UserCostCenterAccesses")
                        .HasForeignKey("CostCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.User", "User")
                        .WithMany("UserCostCenterAccesses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CostCenter");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserFormActionAccess", b =>
                {
                    b.HasOne("InventoryAPI.Models.FormAction", "FormAction")
                        .WithMany("UserFormActionAccesses")
                        .HasForeignKey("ActionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.User", "User")
                        .WithMany("UserFormActionAccesses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FormAction");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserProductAccess", b =>
                {
                    b.HasOne("InventoryAPI.Models.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId");

                    b.HasOne("InventoryAPI.Models.ProductGroup", "ProductGroup")
                        .WithMany()
                        .HasForeignKey("GroupId");

                    b.HasOne("InventoryAPI.Models.Product", "Product")
                        .WithMany("UserProductAccesses")
                        .HasForeignKey("ProductId");

                    b.HasOne("InventoryAPI.Models.ProductSubGroup", "ProductSubGroup")
                        .WithMany()
                        .HasForeignKey("SubGroupId");

                    b.HasOne("InventoryAPI.Models.User", "User")
                        .WithMany("UserProductAccesses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Product");

                    b.Navigation("ProductGroup");

                    b.Navigation("ProductSubGroup");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserReportAccess", b =>
                {
                    b.HasOne("InventoryAPI.Models.User", "User")
                        .WithMany("UserReportAccesses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserStockTakeAccess", b =>
                {
                    b.HasOne("InventoryAPI.Models.CostCenter", "CostCenter")
                        .WithMany("UserStockTakeAccesses")
                        .HasForeignKey("CostCenterId");

                    b.HasOne("InventoryAPI.Models.User", "User")
                        .WithMany("UserStockTakeAccesses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CostCenter");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserTransactionAccess", b =>
                {
                    b.HasOne("InventoryAPI.Models.TransactionStageType", "StageType")
                        .WithMany("UserTransactionAccesses")
                        .HasForeignKey("StageTypeId");

                    b.HasOne("InventoryAPI.Models.TransactionType", "TransactionType")
                        .WithMany("UserTransactionAccesses")
                        .HasForeignKey("TransactionTypeId");

                    b.HasOne("InventoryAPI.Models.User", "User")
                        .WithMany("UserTransactionAccesses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StageType");

                    b.Navigation("TransactionType");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InventoryAPI.Models.UserTransactionTypeAccess", b =>
                {
                    b.HasOne("InventoryAPI.Models.TransactionType", "TransactionType")
                        .WithMany("UserTransactionTypeAccesses")
                        .HasForeignKey("TransactionTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("InventoryAPI.Models.User", "User")
                        .WithMany("UserTransactionTypeAccesses")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TransactionType");

                    b.Navigation("User");
                });

            modelBuilder.Entity("InventoryAPI.Models.ApplicationForm", b =>
                {
                    b.Navigation("FormActions");
                });

            modelBuilder.Entity("InventoryAPI.Models.Batch", b =>
                {
                    b.Navigation("StockTakeDetails");

                    b.Navigation("TransactionDetails");
                });

            modelBuilder.Entity("InventoryAPI.Models.Brand", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("InventoryAPI.Models.CostCenter", b =>
                {
                    b.Navigation("Batches");

                    b.Navigation("CostCenterFiscalYears");

                    b.Navigation("DestinationTransactions");

                    b.Navigation("PeriodCloses");

                    b.Navigation("ProductCostCenterLinks");

                    b.Navigation("SourceTransactions");

                    b.Navigation("StockOnHands");

                    b.Navigation("StockTakeHeaders");

                    b.Navigation("UserCostCenterAccesses");

                    b.Navigation("UserStockTakeAccesses");
                });

            modelBuilder.Entity("InventoryAPI.Models.Currency", b =>
                {
                    b.Navigation("Payments");
                });

            modelBuilder.Entity("InventoryAPI.Models.Department", b =>
                {
                    b.Navigation("ProductGroups");

                    b.Navigation("Products");
                });

            modelBuilder.Entity("InventoryAPI.Models.FiscalYear", b =>
                {
                    b.Navigation("CostCenterFiscalYears");

                    b.Navigation("PeriodCloses");

                    b.Navigation("Periods");

                    b.Navigation("StockTakeHeaders");
                });

            modelBuilder.Entity("InventoryAPI.Models.FormAction", b =>
                {
                    b.Navigation("RoleFormActionAccesses");

                    b.Navigation("UserFormActionAccesses");
                });

            modelBuilder.Entity("InventoryAPI.Models.Location", b =>
                {
                    b.Navigation("Stores");
                });

            modelBuilder.Entity("InventoryAPI.Models.PaymentMethod", b =>
                {
                    b.Navigation("Payments");
                });

            modelBuilder.Entity("InventoryAPI.Models.Period", b =>
                {
                    b.Navigation("PeriodCloses");

                    b.Navigation("StockTakeHeaders");
                });

            modelBuilder.Entity("InventoryAPI.Models.Product", b =>
                {
                    b.Navigation("Batches");

                    b.Navigation("PeriodCloses");

                    b.Navigation("ProductCostCenterLinks");

                    b.Navigation("RecipeIngredients");

                    b.Navigation("Recipes");

                    b.Navigation("StockOnHands");

                    b.Navigation("StockTakeDetails");

                    b.Navigation("TransactionDetails");

                    b.Navigation("UserProductAccesses");
                });

            modelBuilder.Entity("InventoryAPI.Models.ProductGroup", b =>
                {
                    b.Navigation("ProductSubGroups");

                    b.Navigation("Products");
                });

            modelBuilder.Entity("InventoryAPI.Models.ProductSubGroup", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("InventoryAPI.Models.Recipe", b =>
                {
                    b.Navigation("RecipeIngredients");
                });

            modelBuilder.Entity("InventoryAPI.Models.Role", b =>
                {
                    b.Navigation("RoleFormActionAccesses");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("InventoryAPI.Models.Shift", b =>
                {
                    b.Navigation("Payments");

                    b.Navigation("TransactionHeaders");
                });

            modelBuilder.Entity("InventoryAPI.Models.StockTakeHeader", b =>
                {
                    b.Navigation("PeriodCloses");

                    b.Navigation("StockTakeDetails");
                });

            modelBuilder.Entity("InventoryAPI.Models.StockTakeType", b =>
                {
                    b.Navigation("StockTakeDetails");

                    b.Navigation("StockTakeHeaders");
                });

            modelBuilder.Entity("InventoryAPI.Models.Store", b =>
                {
                    b.Navigation("CostCenters");
                });

            modelBuilder.Entity("InventoryAPI.Models.Supplier", b =>
                {
                    b.Navigation("TransactionHeaders");
                });

            modelBuilder.Entity("InventoryAPI.Models.Tax", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("TransactionDetails");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionHeader", b =>
                {
                    b.Navigation("Payments");

                    b.Navigation("TransactionDetails");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionProcess", b =>
                {
                    b.Navigation("TransactionHeaders");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionStageType", b =>
                {
                    b.Navigation("TransactionHeaders");

                    b.Navigation("UserTransactionAccesses");
                });

            modelBuilder.Entity("InventoryAPI.Models.TransactionType", b =>
                {
                    b.Navigation("TransactionHeaders");

                    b.Navigation("UserTransactionAccesses");

                    b.Navigation("UserTransactionTypeAccesses");
                });

            modelBuilder.Entity("InventoryAPI.Models.Unit", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("ProductsWithSalesUnit");

                    b.Navigation("RecipeIngredients");

                    b.Navigation("Recipes");

                    b.Navigation("StockOnHands");

                    b.Navigation("TransactionDetails");
                });

            modelBuilder.Entity("InventoryAPI.Models.User", b =>
                {
                    b.Navigation("UserCostCenterAccesses");

                    b.Navigation("UserFormActionAccesses");

                    b.Navigation("UserProductAccesses");

                    b.Navigation("UserReportAccesses");

                    b.Navigation("UserStockTakeAccesses");

                    b.Navigation("UserTransactionAccesses");

                    b.Navigation("UserTransactionTypeAccesses");
                });
#pragma warning restore 612, 618
        }
    }
}
