using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("Role")]
    public class Role
    {
        [Key]
        [Column("RoleId")]
        public int RoleId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Name")]
        public string Name { get; set; }

        [StringLength(255)]
        [Column("Description")]
        public string Description { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        [Column("Permissions")]
        public string Permissions { get; set; }

        [Column("DefaultCanDeleteCostCenters")]
        public bool DefaultCanDeleteCostCenters { get; set; }

        [Column("DefaultCanApproveCostCenters")]
        public bool DefaultCanApproveCostCenters { get; set; }

        // Default Transaction Permissions
        [Column("DefaultCanViewTransactions")]
        public bool DefaultCanViewTransactions { get; set; }

        [Column("DefaultCanCreateTransactions")]
        public bool DefaultCanCreateTransactions { get; set; }

        [Column("DefaultCanEditTransactions")]
        public bool DefaultCanEditTransactions { get; set; }

        [Column("DefaultCanDeleteTransactions")]
        public bool DefaultCanDeleteTransactions { get; set; }

        [Column("DefaultCanApproveTransactions")]
        public bool DefaultCanApproveTransactions { get; set; }

        [Column("DefaultCanRejectTransactions")]
        public bool DefaultCanRejectTransactions { get; set; }

        [Column("DefaultCanProcessTransactions")]
        public bool DefaultCanProcessTransactions { get; set; }

        [Column("DefaultMaxApprovalAmount")]
        public decimal? DefaultMaxApprovalAmount { get; set; }

        // Default Stock Take Permissions
        [Column("DefaultCanViewStockTakes")]
        public bool DefaultCanViewStockTakes { get; set; }

        [Column("DefaultCanCreateStockTakes")]
        public bool DefaultCanCreateStockTakes { get; set; }

        [Column("DefaultCanEditStockTakes")]
        public bool DefaultCanEditStockTakes { get; set; }

        [Column("DefaultCanDeleteStockTakes")]
        public bool DefaultCanDeleteStockTakes { get; set; }

        [Column("DefaultCanCountStockTakes")]
        public bool DefaultCanCountStockTakes { get; set; }

        [Column("DefaultCanFinalizeStockTakes")]
        public bool DefaultCanFinalizeStockTakes { get; set; }

        [Column("DefaultCanReopenStockTakes")]
        public bool DefaultCanReopenStockTakes { get; set; }

        // Default Report Permissions
        [Column("DefaultCanViewReports")]
        public bool DefaultCanViewReports { get; set; }

        [Column("DefaultCanExportReports")]
        public bool DefaultCanExportReports { get; set; }

        [Column("DefaultCanScheduleReports")]
        public bool DefaultCanScheduleReports { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        public virtual ICollection<User> Users { get; set; }
        public virtual ICollection<RoleFormActionAccess> RoleFormActionAccesses { get; set; }
    }
}
