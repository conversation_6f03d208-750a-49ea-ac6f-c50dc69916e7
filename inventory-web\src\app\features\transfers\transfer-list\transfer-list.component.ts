import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlaceholderComponent } from '../../../shared/components/placeholder/placeholder.component';

@Component({
  selector: 'app-transfer-list',
  standalone: true,
  imports: [CommonModule, PlaceholderComponent],
  template: `
    <app-placeholder 
      title="Transfers Management" 
      message="The transfers management feature is coming soon. This will allow you to transfer inventory between cost centers."
      icon="bi-arrow-repeat"
      backLink="/dashboard"
      backLabel="Back to Dashboard">
    </app-placeholder>
  `
})
export class TransferListComponent {}
