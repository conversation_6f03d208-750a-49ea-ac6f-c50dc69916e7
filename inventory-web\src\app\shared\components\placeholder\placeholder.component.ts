import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-placeholder',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './placeholder.component.html',
  styleUrls: ['./placeholder.component.scss']
})
export class PlaceholderComponent {
  @Input() title = 'Coming Soon';
  @Input() message = 'This feature is under development and will be available soon.';
  @Input() icon = 'bi-tools';
  @Input() backLink = '/dashboard';
  @Input() backLabel = 'Back to Dashboard';
}
