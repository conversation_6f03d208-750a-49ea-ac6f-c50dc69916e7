import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface MenuItem {
  label: string;
  icon: string;
  route: string;
  children?: MenuItem[];
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss']
})
export class SidebarComponent {
  menuItems: MenuItem[] = [
    {
      label: 'Dashboard',
      icon: 'bi-speedometer2',
      route: '/dashboard'
    },
    {
      label: 'Products',
      icon: 'bi-box',
      route: '/products'
    },
    {
      label: 'Transactions',
      icon: 'bi-arrow-left-right',
      route: '#',
      children: [
        {
          label: 'Order Requests',
          icon: 'bi-cart',
          route: '/order-requests'
        },
        {
          label: 'Receiving',
          icon: 'bi-truck',
          route: '/receiving'
        },
        {
          label: 'Transfers',
          icon: 'bi-arrow-repeat',
          route: '/transfers'
        }
      ]
    },
    {
      label: 'Cost Centers',
      icon: 'bi-building',
      route: '/cost-centers'
    },
    {
      label: 'Suppliers',
      icon: 'bi-people',
      route: '/suppliers'
    },
    {
      label: 'Reports',
      icon: 'bi-file-earmark-bar-graph',
      route: '/reports'
    },
    {
      label: 'Settings',
      icon: 'bi-gear',
      route: '/settings'
    }
  ];

  expandedMenus: { [key: string]: boolean } = {};

  toggleMenu(label: string): void {
    this.expandedMenus[label] = !this.expandedMenus[label];
  }

  isExpanded(label: string): boolean {
    return this.expandedMenus[label] || false;
  }
}
