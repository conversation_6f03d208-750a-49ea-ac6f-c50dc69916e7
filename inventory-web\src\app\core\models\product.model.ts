export interface Product {
  productId: number;
  code: string;
  name: string;
  brandId?: number;
  brandName?: string;
  unitId?: number;
  unitName?: string;
  unitGroupId?: number;
  departmentId?: number;
  departmentName?: string;
  groupId?: number;
  groupName?: string;
  subGroupId?: number;
  subGroupName?: string;
  costPrice?: number;
  averageCost?: number;
  salesPrice?: number;
  minStock?: number;
  maxStock?: number;
  reorderPoint?: number;
  notes?: string;
  isStockItem: boolean;
  isRecipe: boolean;
  hasExpiry: boolean;
  isProduction: boolean;
  isSaleable: boolean;
  taxId?: number;
  taxName?: string;
  taxRate?: number;
  salesUnitId?: number;
  salesUnitName?: string;
  salesUnitConversionFactor?: number;
  allowDiscount: boolean;
  createdAt: Date;
  updatedAt?: Date;
  isActive: boolean;
}
