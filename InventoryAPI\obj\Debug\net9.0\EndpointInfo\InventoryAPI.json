{"openapi": "3.0.4", "info": {"title": "InventoryAPI", "version": "1.0"}, "paths": {"/api/Auth/Login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}}}}, "/api/CostCenters": {"get": {"tags": ["CostCenters"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenter"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenter"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenter"}}}}}}}, "post": {"tags": ["CostCenters"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CostCenter"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}}}}}}, "/api/CostCenters/{id}": {"get": {"tags": ["CostCenters"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CostCenter"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}}}}}, "put": {"tags": ["CostCenters"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CostCenter"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["CostCenters"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/CostCenters/ByStore/{storeId}": {"get": {"tags": ["CostCenters"], "parameters": [{"name": "storeId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenter"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenter"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenter"}}}}}}}}, "/api/Departments": {"get": {"tags": ["Departments"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Department"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Department"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Department"}}}}}}}, "post": {"tags": ["Departments"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Department"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Department"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Department"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Department"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Department"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Department"}}}}}}}, "/api/Departments/{id}": {"get": {"tags": ["Departments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Department"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Department"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Department"}}}}}}, "put": {"tags": ["Departments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Department"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Department"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Department"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Departments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/OrderRequest": {"get": {"tags": ["OrderRequest"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}}}}}}}, "post": {"tags": ["OrderRequest"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderRequestDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderRequestDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderRequestDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeader"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeader"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeader"}}}}}}}, "/api/OrderRequest/{id}": {"get": {"tags": ["OrderRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderRequestDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderRequestDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderRequestDTO"}}}}}}, "put": {"tags": ["OrderRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderRequestDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderRequestDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderRequestDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["OrderRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/OrderRequest/{id}/Submit": {"post": {"tags": ["OrderRequest"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}}}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Product"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Product"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Product"}}}}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Product"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Product"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Receiving": {"get": {"tags": ["Receiving"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}}}}}}}, "post": {"tags": ["Receiving"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceivingDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReceivingDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ReceivingDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeader"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeader"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeader"}}}}}}}, "/api/Receiving/{id}": {"get": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ReceivingDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ReceivingDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReceivingDTO"}}}}}}, "put": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceivingDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReceivingDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ReceivingDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Receiving"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Suppliers": {"get": {"tags": ["Suppliers"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Supplier"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Supplier"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Supplier"}}}}}}}, "post": {"tags": ["Suppliers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Supplier"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Supplier"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}}}}}}, "/api/Suppliers/{id}": {"get": {"tags": ["Suppliers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Supplier"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}}}}}, "put": {"tags": ["Suppliers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Supplier"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Supplier"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Suppliers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Transfers": {"get": {"tags": ["Transfers"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}}}}}}}, "post": {"tags": ["Transfers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TransferDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransactionHeader"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransactionHeader"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransactionHeader"}}}}}}}, "/api/Transfers/{id}": {"get": {"tags": ["Transfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransferDTO"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransferDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferDTO"}}}}}}, "put": {"tags": ["Transfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TransferDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Transfers"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"ApplicationForm": {"required": ["formName"], "type": "object", "properties": {"formId": {"type": "integer", "format": "int32"}, "formName": {"maxLength": 100, "minLength": 0, "type": "string"}, "formDescription": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "formCategory": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "formPath": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "formActions": {"type": "array", "items": {"$ref": "#/components/schemas/FormAction"}, "nullable": true}}, "additionalProperties": false}, "Batch": {"required": ["batchNumber"], "type": "object", "properties": {"batchId": {"type": "integer", "format": "int32"}, "batchNumber": {"maxLength": 150, "minLength": 0, "type": "string"}, "productId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "manufactureDate": {"type": "string", "format": "date-time", "nullable": true}, "expiryDate": {"type": "string", "format": "date-time", "nullable": true}, "initialQuantity": {"type": "number", "format": "double"}, "currentQuantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "isOpen": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "product": {"$ref": "#/components/schemas/Product"}, "costCenter": {"$ref": "#/components/schemas/CostCenter"}, "unit": {"$ref": "#/components/schemas/Unit"}, "transactionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionDetail"}, "nullable": true}, "stockTakeDetails": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeDetail"}, "nullable": true}}, "additionalProperties": false}, "Brand": {"required": ["name"], "type": "object", "properties": {"brandId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}, "nullable": true}}, "additionalProperties": false}, "CostCenter": {"required": ["name"], "type": "object", "properties": {"costCenterId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "storeId": {"type": "integer", "format": "int32", "nullable": true}, "typeId": {"type": "integer", "format": "int32", "nullable": true}, "typeName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "autoTransfer": {"type": "boolean"}, "isSalesPoint": {"type": "boolean"}, "abbreviation": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "store": {"$ref": "#/components/schemas/Store"}, "costCenterFiscalYears": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterFiscalYear"}, "nullable": true}, "stockOnHands": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHand"}, "nullable": true}, "productCostCenterLinks": {"type": "array", "items": {"$ref": "#/components/schemas/ProductCostCenterLink"}, "nullable": true}, "batches": {"type": "array", "items": {"$ref": "#/components/schemas/Batch"}, "nullable": true}, "sourceTransactions": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}, "nullable": true}, "destinationTransactions": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}, "nullable": true}, "stockTakeHeaders": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeader"}, "nullable": true}, "periodCloses": {"type": "array", "items": {"$ref": "#/components/schemas/PeriodClose"}, "nullable": true}, "userCostCenterAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserCostCenterAccess"}, "nullable": true}, "userStockTakeAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserStockTakeAccess"}, "nullable": true}}, "additionalProperties": false}, "CostCenterFiscalYear": {"type": "object", "properties": {"costCenterFiscalYearId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "fiscalYearId": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "costCenter": {"$ref": "#/components/schemas/CostCenter"}, "fiscalYear": {"$ref": "#/components/schemas/FiscalYear"}}, "additionalProperties": false}, "Currency": {"type": "object", "properties": {"currencyId": {"type": "integer", "format": "int32"}, "code": {"maxLength": 3, "minLength": 0, "type": "string", "nullable": true}, "name": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "symbol": {"maxLength": 5, "minLength": 0, "type": "string", "nullable": true}, "exchangeRate": {"type": "number", "format": "double"}, "isBaseCurrency": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "payments": {"type": "array", "items": {"$ref": "#/components/schemas/Payment"}, "nullable": true}}, "additionalProperties": false}, "Department": {"required": ["name"], "type": "object", "properties": {"departmentId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "productGroups": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroup"}, "nullable": true}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}, "nullable": true}}, "additionalProperties": false}, "FiscalYear": {"type": "object", "properties": {"fiscalYearId": {"type": "integer", "format": "int32"}, "year": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "isClosed": {"type": "boolean"}, "closedById": {"type": "integer", "format": "int32", "nullable": true}, "closedDate": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "closedBy": {"$ref": "#/components/schemas/User"}, "periods": {"type": "array", "items": {"$ref": "#/components/schemas/Period"}, "nullable": true}, "costCenterFiscalYears": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenterFiscalYear"}, "nullable": true}, "stockTakeHeaders": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeader"}, "nullable": true}, "periodCloses": {"type": "array", "items": {"$ref": "#/components/schemas/PeriodClose"}, "nullable": true}}, "additionalProperties": false}, "FormAction": {"required": ["actionCode", "actionName"], "type": "object", "properties": {"actionId": {"type": "integer", "format": "int32"}, "formId": {"type": "integer", "format": "int32"}, "actionName": {"maxLength": 100, "minLength": 0, "type": "string"}, "actionDescription": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "actionCode": {"maxLength": 50, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "applicationForm": {"$ref": "#/components/schemas/ApplicationForm"}, "roleFormActionAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/RoleFormActionAccess"}, "nullable": true}, "userFormActionAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserFormActionAccess"}, "nullable": true}}, "additionalProperties": false}, "Location": {"required": ["name"], "type": "object", "properties": {"locationId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "address": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "state": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "country": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "postalCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "stores": {"type": "array", "items": {"$ref": "#/components/schemas/Store"}, "nullable": true}}, "additionalProperties": false}, "LoginModel": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginResponse": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "expiration": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "OrderRequestDTO": {"type": "object", "properties": {"transactionId": {"type": "integer", "format": "int32"}, "transactionNumber": {"type": "string", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "sourceCostCenterId": {"type": "integer", "format": "int32", "nullable": true}, "sourceCostCenterName": {"type": "string", "nullable": true}, "destinationCostCenterId": {"type": "integer", "format": "int32", "nullable": true}, "destinationCostCenterName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "subTotal": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderRequestItemDTO"}, "nullable": true}}, "additionalProperties": false}, "OrderRequestItemDTO": {"type": "object", "properties": {"transactionDetailId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32"}, "unitName": {"type": "string", "nullable": true}, "unitPrice": {"type": "number", "format": "double"}, "lineTotal": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Payment": {"type": "object", "properties": {"paymentId": {"type": "integer", "format": "int32"}, "transactionId": {"type": "integer", "format": "int32"}, "paymentMethodId": {"type": "integer", "format": "int32"}, "amount": {"type": "number", "format": "double"}, "changeAmount": {"type": "number", "format": "double", "nullable": true}, "pointsUsed": {"type": "number", "format": "double", "nullable": true}, "pointsRate": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}, "paymentDate": {"type": "string", "format": "date-time"}, "createdById": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "currencyId": {"type": "integer", "format": "int32", "nullable": true}, "shiftId": {"type": "integer", "format": "int32", "nullable": true}, "transaction": {"$ref": "#/components/schemas/TransactionHeader"}, "paymentMethod": {"$ref": "#/components/schemas/PaymentMethod"}, "createdBy": {"$ref": "#/components/schemas/User"}, "currency": {"$ref": "#/components/schemas/Currency"}, "shift": {"$ref": "#/components/schemas/Shift"}}, "additionalProperties": false}, "PaymentMethod": {"required": ["name"], "type": "object", "properties": {"paymentMethodId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "accountNumber": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "isPointsSystem": {"type": "boolean"}, "pointsConversionRate": {"type": "number", "format": "double", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "payments": {"type": "array", "items": {"$ref": "#/components/schemas/Payment"}, "nullable": true}}, "additionalProperties": false}, "Period": {"type": "object", "properties": {"periodId": {"type": "integer", "format": "int32"}, "periodNumber": {"type": "integer", "format": "int32"}, "fiscalYearId": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "isClosed": {"type": "boolean"}, "closedById": {"type": "integer", "format": "int32", "nullable": true}, "closedDate": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "fiscalYear": {"$ref": "#/components/schemas/FiscalYear"}, "closedBy": {"$ref": "#/components/schemas/User"}, "stockTakeHeaders": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeader"}, "nullable": true}, "periodCloses": {"type": "array", "items": {"$ref": "#/components/schemas/PeriodClose"}, "nullable": true}}, "additionalProperties": false}, "PeriodClose": {"type": "object", "properties": {"periodCloseId": {"type": "integer", "format": "int32"}, "periodId": {"type": "integer", "format": "int32"}, "fiscalYearId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "openingQuantity": {"type": "number", "format": "double"}, "closingQuantity": {"type": "number", "format": "double"}, "openingValue": {"type": "number", "format": "double"}, "closingValue": {"type": "number", "format": "double"}, "averageCost": {"type": "number", "format": "double", "nullable": true}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "stockTakeId": {"type": "integer", "format": "int32", "nullable": true}, "closedDate": {"type": "string", "format": "date-time"}, "closedById": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "period": {"$ref": "#/components/schemas/Period"}, "fiscalYear": {"$ref": "#/components/schemas/FiscalYear"}, "product": {"$ref": "#/components/schemas/Product"}, "costCenter": {"$ref": "#/components/schemas/CostCenter"}, "stockTake": {"$ref": "#/components/schemas/StockTakeHeader"}, "closedBy": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false}, "Product": {"required": ["code", "name"], "type": "object", "properties": {"productId": {"type": "integer", "format": "int32"}, "code": {"maxLength": 50, "minLength": 0, "type": "string"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "brandId": {"type": "integer", "format": "int32", "nullable": true}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "unitGroupId": {"type": "integer", "format": "int32", "nullable": true}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}, "groupId": {"type": "integer", "format": "int32", "nullable": true}, "subGroupId": {"type": "integer", "format": "int32", "nullable": true}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "averageCost": {"type": "number", "format": "double", "nullable": true}, "salesPrice": {"type": "number", "format": "double", "nullable": true}, "minStock": {"type": "number", "format": "double", "nullable": true}, "maxStock": {"type": "number", "format": "double", "nullable": true}, "reorderPoint": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}, "isStockItem": {"type": "boolean"}, "isRecipe": {"type": "boolean"}, "hasExpiry": {"type": "boolean"}, "isProduction": {"type": "boolean"}, "isSaleable": {"type": "boolean"}, "taxId": {"type": "integer", "format": "int32", "nullable": true}, "salesUnitId": {"type": "integer", "format": "int32", "nullable": true}, "salesUnitConversionFactor": {"type": "number", "format": "double", "nullable": true}, "allowDiscount": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "brand": {"$ref": "#/components/schemas/Brand"}, "unit": {"$ref": "#/components/schemas/Unit"}, "department": {"$ref": "#/components/schemas/Department"}, "productGroup": {"$ref": "#/components/schemas/ProductGroup"}, "productSubGroup": {"$ref": "#/components/schemas/ProductSubGroup"}, "tax": {"$ref": "#/components/schemas/Tax"}, "salesUnit": {"$ref": "#/components/schemas/Unit"}, "recipes": {"type": "array", "items": {"$ref": "#/components/schemas/Recipe"}, "nullable": true}, "recipeIngredients": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeIngredient"}, "nullable": true}, "stockOnHands": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHand"}, "nullable": true}, "productCostCenterLinks": {"type": "array", "items": {"$ref": "#/components/schemas/ProductCostCenterLink"}, "nullable": true}, "batches": {"type": "array", "items": {"$ref": "#/components/schemas/Batch"}, "nullable": true}, "transactionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionDetail"}, "nullable": true}, "stockTakeDetails": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeDetail"}, "nullable": true}, "periodCloses": {"type": "array", "items": {"$ref": "#/components/schemas/PeriodClose"}, "nullable": true}, "userProductAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserProductAccess"}, "nullable": true}}, "additionalProperties": false}, "ProductCostCenterLink": {"type": "object", "properties": {"linkId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "maximumStock": {"type": "number", "format": "double", "nullable": true}, "minimumStock": {"type": "number", "format": "double", "nullable": true}, "reorderPoint": {"type": "number", "format": "double", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "product": {"$ref": "#/components/schemas/Product"}, "costCenter": {"$ref": "#/components/schemas/CostCenter"}}, "additionalProperties": false}, "ProductGroup": {"required": ["name"], "type": "object", "properties": {"groupId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "department": {"$ref": "#/components/schemas/Department"}, "productSubGroups": {"type": "array", "items": {"$ref": "#/components/schemas/ProductSubGroup"}, "nullable": true}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}, "nullable": true}}, "additionalProperties": false}, "ProductSubGroup": {"required": ["name"], "type": "object", "properties": {"subGroupId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "groupId": {"type": "integer", "format": "int32", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "productGroup": {"$ref": "#/components/schemas/ProductGroup"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}, "nullable": true}}, "additionalProperties": false}, "ReceivingDTO": {"type": "object", "properties": {"transactionId": {"type": "integer", "format": "int32"}, "transactionNumber": {"type": "string", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "supplierId": {"type": "integer", "format": "int32", "nullable": true}, "supplierName": {"type": "string", "nullable": true}, "referenceNumber": {"type": "string", "nullable": true}, "destinationCostCenterId": {"type": "integer", "format": "int32", "nullable": true}, "destinationCostCenterName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "subTotal": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "discountAmount": {"type": "number", "format": "double"}, "discountPercentage": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ReceivingItemDTO"}, "nullable": true}}, "additionalProperties": false}, "ReceivingItemDTO": {"type": "object", "properties": {"transactionDetailId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "orderQuantity": {"type": "number", "format": "double"}, "receivedQuantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32"}, "unitName": {"type": "string", "nullable": true}, "unitPrice": {"type": "number", "format": "double"}, "taxId": {"type": "integer", "format": "int32", "nullable": true}, "taxRate": {"type": "number", "format": "double", "nullable": true}, "taxAmount": {"type": "number", "format": "double", "nullable": true}, "discountAmount": {"type": "number", "format": "double", "nullable": true}, "discountPercentage": {"type": "number", "format": "double", "nullable": true}, "lineTotal": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Recipe": {"type": "object", "properties": {"recipeId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "description": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "yield": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32"}, "cost": {"type": "number", "format": "double"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "product": {"$ref": "#/components/schemas/Product"}, "unit": {"$ref": "#/components/schemas/Unit"}, "recipeIngredients": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeIngredient"}, "nullable": true}}, "additionalProperties": false}, "RecipeIngredient": {"type": "object", "properties": {"recipeIngredientId": {"type": "integer", "format": "int32"}, "recipeId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "quantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32"}, "cost": {"type": "number", "format": "double"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "recipe": {"$ref": "#/components/schemas/Recipe"}, "product": {"$ref": "#/components/schemas/Product"}, "unit": {"$ref": "#/components/schemas/Unit"}}, "additionalProperties": false}, "Role": {"required": ["name"], "type": "object", "properties": {"roleId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "isAdmin": {"type": "boolean"}, "isSystem": {"type": "boolean"}, "defaultCanViewCostCenters": {"type": "boolean"}, "defaultCanCreateCostCenters": {"type": "boolean"}, "defaultCanEditCostCenters": {"type": "boolean"}, "defaultCanDeleteCostCenters": {"type": "boolean"}, "defaultCanApproveCostCenters": {"type": "boolean"}, "defaultCanViewTransactions": {"type": "boolean"}, "defaultCanCreateTransactions": {"type": "boolean"}, "defaultCanEditTransactions": {"type": "boolean"}, "defaultCanDeleteTransactions": {"type": "boolean"}, "defaultCanApproveTransactions": {"type": "boolean"}, "defaultCanRejectTransactions": {"type": "boolean"}, "defaultCanProcessTransactions": {"type": "boolean"}, "defaultMaxApprovalAmount": {"type": "number", "format": "double", "nullable": true}, "defaultCanViewStockTakes": {"type": "boolean"}, "defaultCanCreateStockTakes": {"type": "boolean"}, "defaultCanEditStockTakes": {"type": "boolean"}, "defaultCanDeleteStockTakes": {"type": "boolean"}, "defaultCanCountStockTakes": {"type": "boolean"}, "defaultCanFinalizeStockTakes": {"type": "boolean"}, "defaultCanReopenStockTakes": {"type": "boolean"}, "defaultCanViewReports": {"type": "boolean"}, "defaultCanExportReports": {"type": "boolean"}, "defaultCanScheduleReports": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}, "nullable": true}, "roleFormActionAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/RoleFormActionAccess"}, "nullable": true}}, "additionalProperties": false}, "RoleFormActionAccess": {"type": "object", "properties": {"accessId": {"type": "integer", "format": "int32"}, "roleId": {"type": "integer", "format": "int32"}, "actionId": {"type": "integer", "format": "int32"}, "hasAccess": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "role": {"$ref": "#/components/schemas/Role"}, "formAction": {"$ref": "#/components/schemas/FormAction"}}, "additionalProperties": false}, "Shift": {"type": "object", "properties": {"shiftId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "openingBalance": {"type": "number", "format": "double"}, "closingBalance": {"type": "number", "format": "double", "nullable": true}, "status": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "notes": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "costCenter": {"$ref": "#/components/schemas/CostCenter"}, "user": {"$ref": "#/components/schemas/User"}, "payments": {"type": "array", "items": {"$ref": "#/components/schemas/Payment"}, "nullable": true}, "transactionHeaders": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}, "nullable": true}}, "additionalProperties": false}, "StockOnHand": {"type": "object", "properties": {"stockId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "quantity": {"type": "number", "format": "double"}, "baseQuantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32", "nullable": true}, "averageCost": {"type": "number", "format": "double", "nullable": true}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "returnVariance": {"type": "number", "format": "double", "nullable": true}, "netCost": {"type": "number", "format": "double", "nullable": true}, "salesPrice": {"type": "number", "format": "double", "nullable": true}, "lastUpdated": {"type": "string", "format": "date-time"}, "product": {"$ref": "#/components/schemas/Product"}, "costCenter": {"$ref": "#/components/schemas/CostCenter"}, "unit": {"$ref": "#/components/schemas/Unit"}}, "additionalProperties": false}, "StockTakeDetail": {"type": "object", "properties": {"stockTakeDetailId": {"type": "integer", "format": "int32"}, "stockTakeId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "systemQuantity": {"type": "number", "format": "double"}, "actualQuantity": {"type": "number", "format": "double", "nullable": true}, "varianceQuantity": {"type": "number", "format": "double", "nullable": true}, "unitId": {"type": "integer", "format": "int32"}, "costPrice": {"type": "number", "format": "double", "nullable": true}, "varianceValue": {"type": "number", "format": "double", "nullable": true}, "notes": {"type": "string", "nullable": true}, "openingQuantity": {"type": "number", "format": "double", "nullable": true}, "closingQuantity": {"type": "number", "format": "double", "nullable": true}, "stockTakeTypeId": {"type": "integer", "format": "int32", "nullable": true}, "lastCountDate": {"type": "string", "format": "date-time", "nullable": true}, "countedById": {"type": "integer", "format": "int32", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "stockTakeHeader": {"$ref": "#/components/schemas/StockTakeHeader"}, "product": {"$ref": "#/components/schemas/Product"}, "batch": {"$ref": "#/components/schemas/Batch"}, "unit": {"$ref": "#/components/schemas/Unit"}, "stockTakeType": {"$ref": "#/components/schemas/StockTakeType"}, "countedBy": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false}, "StockTakeHeader": {"required": ["status", "stockTakeNumber"], "type": "object", "properties": {"stockTakeId": {"type": "integer", "format": "int32"}, "stockTakeNumber": {"maxLength": 50, "minLength": 0, "type": "string"}, "costCenterId": {"type": "integer", "format": "int32"}, "fiscalYearId": {"type": "integer", "format": "int32"}, "periodId": {"type": "integer", "format": "int32", "nullable": true}, "stockTakeDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"maxLength": 20, "minLength": 0, "type": "string"}, "totalVariance": {"type": "number", "format": "double", "nullable": true}, "stockTakeTypeId": {"type": "integer", "format": "int32", "nullable": true}, "createdById": {"type": "integer", "format": "int32"}, "completedById": {"type": "integer", "format": "int32", "nullable": true}, "completedDate": {"type": "string", "format": "date-time", "nullable": true}, "isFinalized": {"type": "boolean"}, "finalizedById": {"type": "integer", "format": "int32", "nullable": true}, "finalizedDate": {"type": "string", "format": "date-time", "nullable": true}, "isReopened": {"type": "boolean"}, "reopenedById": {"type": "integer", "format": "int32", "nullable": true}, "reopenedDate": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "costCenter": {"$ref": "#/components/schemas/CostCenter"}, "fiscalYear": {"$ref": "#/components/schemas/FiscalYear"}, "period": {"$ref": "#/components/schemas/Period"}, "stockTakeType": {"$ref": "#/components/schemas/StockTakeType"}, "createdBy": {"$ref": "#/components/schemas/User"}, "completedBy": {"$ref": "#/components/schemas/User"}, "finalizedBy": {"$ref": "#/components/schemas/User"}, "reopenedBy": {"$ref": "#/components/schemas/User"}, "stockTakeDetails": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeDetail"}, "nullable": true}, "periodCloses": {"type": "array", "items": {"$ref": "#/components/schemas/PeriodClose"}, "nullable": true}}, "additionalProperties": false}, "StockTakeType": {"required": ["name"], "type": "object", "properties": {"stockTakeTypeId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "stockTakeHeaders": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeHeader"}, "nullable": true}, "stockTakeDetails": {"type": "array", "items": {"$ref": "#/components/schemas/StockTakeDetail"}, "nullable": true}}, "additionalProperties": false}, "Store": {"required": ["name"], "type": "object", "properties": {"storeId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "locationId": {"type": "integer", "format": "int32", "nullable": true}, "isSalesPoint": {"type": "boolean"}, "logoPath": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "location": {"$ref": "#/components/schemas/Location"}, "costCenters": {"type": "array", "items": {"$ref": "#/components/schemas/CostCenter"}, "nullable": true}}, "additionalProperties": false}, "Supplier": {"required": ["name"], "type": "object", "properties": {"supplierId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 150, "minLength": 0, "type": "string"}, "contactPerson": {"maxLength": 150, "minLength": 0, "type": "string", "nullable": true}, "email": {"maxLength": 150, "minLength": 0, "type": "string", "nullable": true}, "phone": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "address": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "state": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "country": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "postalCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "taxNumber": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "transactionHeaders": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}, "nullable": true}}, "additionalProperties": false}, "Tax": {"required": ["name"], "type": "object", "properties": {"taxId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 100, "minLength": 0, "type": "string"}, "rate": {"type": "number", "format": "double"}, "isDefault": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}, "nullable": true}, "transactionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionDetail"}, "nullable": true}}, "additionalProperties": false}, "TransactionDetail": {"type": "object", "properties": {"transactionDetailId": {"type": "integer", "format": "int32"}, "transactionId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "quantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32"}, "unitPrice": {"type": "number", "format": "double"}, "taxId": {"type": "integer", "format": "int32", "nullable": true}, "taxRate": {"type": "number", "format": "double", "nullable": true}, "taxAmount": {"type": "number", "format": "double", "nullable": true}, "discountAmount": {"type": "number", "format": "double", "nullable": true}, "discountPercentage": {"type": "number", "format": "double", "nullable": true}, "lineTotal": {"type": "number", "format": "double"}, "notes": {"type": "string", "nullable": true}, "isRecipe": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "transactionHeader": {"$ref": "#/components/schemas/TransactionHeader"}, "product": {"$ref": "#/components/schemas/Product"}, "batch": {"$ref": "#/components/schemas/Batch"}, "unit": {"$ref": "#/components/schemas/Unit"}, "tax": {"$ref": "#/components/schemas/Tax"}}, "additionalProperties": false}, "TransactionHeader": {"required": ["status", "transactionNumber"], "type": "object", "properties": {"transactionId": {"type": "integer", "format": "int32"}, "transactionNumber": {"maxLength": 50, "minLength": 0, "type": "string"}, "processId": {"type": "integer", "format": "int32", "nullable": true}, "stageTypeId": {"type": "integer", "format": "int32", "nullable": true}, "transactionTypeId": {"type": "integer", "format": "int32"}, "sourceCostCenterId": {"type": "integer", "format": "int32", "nullable": true}, "destinationCostCenterId": {"type": "integer", "format": "int32", "nullable": true}, "supplierId": {"type": "integer", "format": "int32", "nullable": true}, "referenceNumber": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "subTotal": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "discountAmount": {"type": "number", "format": "double"}, "discountPercentage": {"type": "number", "format": "double"}, "status": {"maxLength": 20, "minLength": 0, "type": "string"}, "createdById": {"type": "integer", "format": "int32"}, "approvedById": {"type": "integer", "format": "int32", "nullable": true}, "approvedDate": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "transactionProcess": {"$ref": "#/components/schemas/TransactionProcess"}, "stageType": {"$ref": "#/components/schemas/TransactionStageType"}, "transactionType": {"$ref": "#/components/schemas/TransactionType"}, "sourceCostCenter": {"$ref": "#/components/schemas/CostCenter"}, "destinationCostCenter": {"$ref": "#/components/schemas/CostCenter"}, "supplier": {"$ref": "#/components/schemas/Supplier"}, "createdBy": {"$ref": "#/components/schemas/User"}, "approvedBy": {"$ref": "#/components/schemas/User"}, "transactionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionDetail"}, "nullable": true}, "payments": {"type": "array", "items": {"$ref": "#/components/schemas/Payment"}, "nullable": true}}, "additionalProperties": false}, "TransactionProcess": {"required": ["processNumber"], "type": "object", "properties": {"processId": {"type": "integer", "format": "int32"}, "processNumber": {"maxLength": 50, "minLength": 0, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "createdById": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "createdBy": {"$ref": "#/components/schemas/User"}, "transactionHeaders": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}, "nullable": true}}, "additionalProperties": false}, "TransactionStageType": {"required": ["name"], "type": "object", "properties": {"stageTypeId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "transactionHeaders": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}, "nullable": true}, "userTransactionAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserTransactionAccess"}, "nullable": true}}, "additionalProperties": false}, "TransactionType": {"required": ["name"], "type": "object", "properties": {"transactionTypeId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "description": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "affectsInventory": {"type": "boolean"}, "isSale": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "transactionHeaders": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionHeader"}, "nullable": true}, "userTransactionAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserTransactionAccess"}, "nullable": true}, "userTransactionTypeAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserTransactionTypeAccess"}, "nullable": true}}, "additionalProperties": false}, "TransferDTO": {"type": "object", "properties": {"transactionId": {"type": "integer", "format": "int32"}, "transactionNumber": {"type": "string", "nullable": true}, "transactionDate": {"type": "string", "format": "date-time"}, "sourceCostCenterId": {"type": "integer", "format": "int32", "nullable": true}, "sourceCostCenterName": {"type": "string", "nullable": true}, "destinationCostCenterId": {"type": "integer", "format": "int32", "nullable": true}, "destinationCostCenterName": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "subTotal": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}, "totalAmount": {"type": "number", "format": "double"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/TransferItemDTO"}, "nullable": true}}, "additionalProperties": false}, "TransferItemDTO": {"type": "object", "properties": {"transactionDetailId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32"}, "productCode": {"type": "string", "nullable": true}, "productName": {"type": "string", "nullable": true}, "batchId": {"type": "integer", "format": "int32", "nullable": true}, "batchNumber": {"type": "string", "nullable": true}, "orderQuantity": {"type": "number", "format": "double"}, "transferQuantity": {"type": "number", "format": "double"}, "unitId": {"type": "integer", "format": "int32"}, "unitName": {"type": "string", "nullable": true}, "unitPrice": {"type": "number", "format": "double"}, "lineTotal": {"type": "number", "format": "double"}}, "additionalProperties": false}, "Unit": {"required": ["name"], "type": "object", "properties": {"unitId": {"type": "integer", "format": "int32"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "abbreviation": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "unitGroupId": {"type": "integer", "format": "int32", "nullable": true}, "baseConversionFactor": {"type": "number", "format": "double"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}, "nullable": true}, "productsWithSalesUnit": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}, "nullable": true}, "recipes": {"type": "array", "items": {"$ref": "#/components/schemas/Recipe"}, "nullable": true}, "recipeIngredients": {"type": "array", "items": {"$ref": "#/components/schemas/RecipeIngredient"}, "nullable": true}, "stockOnHands": {"type": "array", "items": {"$ref": "#/components/schemas/StockOnHand"}, "nullable": true}, "transactionDetails": {"type": "array", "items": {"$ref": "#/components/schemas/TransactionDetail"}, "nullable": true}}, "additionalProperties": false}, "User": {"required": ["passwordHash", "username"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "username": {"maxLength": 50, "minLength": 0, "type": "string"}, "email": {"maxLength": 150, "minLength": 0, "type": "string", "nullable": true}, "passwordHash": {"maxLength": 255, "minLength": 0, "type": "string"}, "firstName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "lastName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "phoneNumber": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "roleId": {"type": "integer", "format": "int32"}, "defaultLanguage": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "lastLoginDate": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "role": {"$ref": "#/components/schemas/Role"}, "userCostCenterAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserCostCenterAccess"}, "nullable": true}, "userTransactionAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserTransactionAccess"}, "nullable": true}, "userStockTakeAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserStockTakeAccess"}, "nullable": true}, "userReportAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserReportAccess"}, "nullable": true}, "userProductAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserProductAccess"}, "nullable": true}, "userTransactionTypeAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserTransactionTypeAccess"}, "nullable": true}, "userFormActionAccesses": {"type": "array", "items": {"$ref": "#/components/schemas/UserFormActionAccess"}, "nullable": true}}, "additionalProperties": false}, "UserCostCenterAccess": {"type": "object", "properties": {"accessId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32"}, "canView": {"type": "boolean"}, "canCreate": {"type": "boolean"}, "canEdit": {"type": "boolean"}, "canDelete": {"type": "boolean"}, "canApprove": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/User"}, "costCenter": {"$ref": "#/components/schemas/CostCenter"}}, "additionalProperties": false}, "UserFormActionAccess": {"type": "object", "properties": {"accessId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "actionId": {"type": "integer", "format": "int32"}, "hasAccess": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/User"}, "formAction": {"$ref": "#/components/schemas/FormAction"}}, "additionalProperties": false}, "UserProductAccess": {"type": "object", "properties": {"productAccessId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "productId": {"type": "integer", "format": "int32", "nullable": true}, "departmentId": {"type": "integer", "format": "int32", "nullable": true}, "groupId": {"type": "integer", "format": "int32", "nullable": true}, "subGroupId": {"type": "integer", "format": "int32", "nullable": true}, "canView": {"type": "boolean"}, "canCreate": {"type": "boolean"}, "canEdit": {"type": "boolean"}, "canDelete": {"type": "boolean"}, "canManageRecipes": {"type": "boolean"}, "canAdjustPrices": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/User"}, "product": {"$ref": "#/components/schemas/Product"}, "department": {"$ref": "#/components/schemas/Department"}, "productGroup": {"$ref": "#/components/schemas/ProductGroup"}, "productSubGroup": {"$ref": "#/components/schemas/ProductSubGroup"}}, "additionalProperties": false}, "UserReportAccess": {"required": ["reportCode"], "type": "object", "properties": {"reportAccessId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "reportCode": {"maxLength": 50, "minLength": 0, "type": "string"}, "canView": {"type": "boolean"}, "canExport": {"type": "boolean"}, "canSchedule": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false}, "UserStockTakeAccess": {"type": "object", "properties": {"stockTakeAccessId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "costCenterId": {"type": "integer", "format": "int32", "nullable": true}, "canView": {"type": "boolean"}, "canCreate": {"type": "boolean"}, "canEdit": {"type": "boolean"}, "canDelete": {"type": "boolean"}, "canCount": {"type": "boolean"}, "canFinalize": {"type": "boolean"}, "canReopen": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/User"}, "costCenter": {"$ref": "#/components/schemas/CostCenter"}}, "additionalProperties": false}, "UserTransactionAccess": {"type": "object", "properties": {"transactionAccessId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "transactionTypeId": {"type": "integer", "format": "int32", "nullable": true}, "stageTypeId": {"type": "integer", "format": "int32", "nullable": true}, "canView": {"type": "boolean"}, "canCreate": {"type": "boolean"}, "canEdit": {"type": "boolean"}, "canDelete": {"type": "boolean"}, "canApprove": {"type": "boolean"}, "canReject": {"type": "boolean"}, "canProcess": {"type": "boolean"}, "maxApprovalAmount": {"type": "number", "format": "double", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/User"}, "transactionType": {"$ref": "#/components/schemas/TransactionType"}, "stageType": {"$ref": "#/components/schemas/TransactionStageType"}}, "additionalProperties": false}, "UserTransactionTypeAccess": {"type": "object", "properties": {"transactionTypeAccessId": {"type": "integer", "format": "int32"}, "userId": {"type": "integer", "format": "int32"}, "transactionTypeId": {"type": "integer", "format": "int32"}, "canView": {"type": "boolean"}, "canCreate": {"type": "boolean"}, "canEdit": {"type": "boolean"}, "canDelete": {"type": "boolean"}, "canApprove": {"type": "boolean"}, "canReject": {"type": "boolean"}, "maxApprovalAmount": {"type": "number", "format": "double", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "isActive": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/User"}, "transactionType": {"$ref": "#/components/schemas/TransactionType"}}, "additionalProperties": false}}}}