<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
    <script>
        function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            fetch('http://localhost:5201/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => {
                document.getElementById('status').textContent = `Status: ${response.status} ${response.statusText}`;
                return response.text();
            })
            .then(text => {
                document.getElementById('response').textContent = text;
                try {
                    const json = JSON.parse(text);
                    document.getElementById('parsed').textContent = 'Parsed JSON: ' + JSON.stringify(json, null, 2);
                } catch (e) {
                    document.getElementById('parsed').textContent = 'Error parsing JSON: ' + e.message;
                }
            })
            .catch(error => {
                document.getElementById('error').textContent = 'Error: ' + error.message;
            });
        }
    </script>
</head>
<body>
    <h1>Test API</h1>
    <div>
        <label for="username">Username:</label>
        <input type="text" id="username" value="admin">
    </div>
    <div>
        <label for="password">Password:</label>
        <input type="password" id="password" value="admin">
    </div>
    <div>
        <button onclick="testLogin()">Test Login</button>
    </div>
    <div>
        <h2>Results:</h2>
        <div id="status"></div>
        <div id="error" style="color: red;"></div>
        <h3>Raw Response:</h3>
        <pre id="response" style="background-color: #f0f0f0; padding: 10px;"></pre>
        <h3>Parsed Response:</h3>
        <pre id="parsed" style="background-color: #f0f0f0; padding: 10px;"></pre>
    </div>
</body>
</html>
