using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace InventoryAPI.Models
{
    [Table("Recipes")]
    public class Recipe
    {
        [Key]
        [Column("RecipeId")]
        public int RecipeId { get; set; }

        [Column("ProductId")]
        public int ProductId { get; set; }

        [Column("Name")]
        [StringLength(100)]
        public string Name { get; set; }

        [Column("Description")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Column("Yield")]
        public decimal Yield { get; set; }

        [Column("UnitId")]
        public int UnitId { get; set; }

        [Column("Cost")]
        [Precision(18, 4)]
        public decimal Cost { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; }

        // Related collections
        public virtual ICollection<RecipeIngredient> RecipeIngredients { get; set; }
    }
}
