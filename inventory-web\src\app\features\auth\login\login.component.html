<div class="login-container">
  <h2 class="mb-4">Sign In</h2>
  
  <div *ngIf="errorMessage" class="alert alert-danger">
    {{ errorMessage }}
  </div>
  
  <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
    <div class="mb-3">
      <label for="username" class="form-label">Username</label>
      <div class="input-group">
        <span class="input-group-text"><i class="bi bi-person"></i></span>
        <input 
          type="text" 
          class="form-control" 
          id="username" 
          formControlName="username" 
          placeholder="Enter your username"
          [ngClass]="{'is-invalid': loginForm.get('username')?.invalid && loginForm.get('username')?.touched}"
        >
      </div>
      <div *ngIf="loginForm.get('username')?.invalid && loginForm.get('username')?.touched" class="invalid-feedback d-block">
        Username is required
      </div>
    </div>
    
    <div class="mb-4">
      <label for="password" class="form-label">Password</label>
      <div class="input-group">
        <span class="input-group-text"><i class="bi bi-lock"></i></span>
        <input 
          type="password" 
          class="form-control" 
          id="password" 
          formControlName="password" 
          placeholder="Enter your password"
          [ngClass]="{'is-invalid': loginForm.get('password')?.invalid && loginForm.get('password')?.touched}"
        >
      </div>
      <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="invalid-feedback d-block">
        Password is required
      </div>
    </div>
    
    <div class="d-grid">
      <button 
        type="submit" 
        class="btn btn-primary" 
        [disabled]="loginForm.invalid || isLoading"
      >
        <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        Sign In
      </button>
    </div>
  </form>
  
  <div class="mt-3 text-center">
    <a href="#" class="text-decoration-none">Forgot password?</a>
  </div>
</div>
