using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("UserTransactionAccess")]
    public class UserTransactionAccess
    {
        [Key]
        [Column("TransactionAccessId")]
        public int TransactionAccessId { get; set; }

        [Column("UserId")]
        public int UserId { get; set; }

        [Column("TransactionTypeId")]
        public int? TransactionTypeId { get; set; }

        [Column("StageTypeId")]
        public int? StageTypeId { get; set; }

        [Column("CanView")]
        public bool CanView { get; set; }

        [Column("CanCreate")]
        public bool CanCreate { get; set; }

        [Column("CanEdit")]
        public bool CanEdit { get; set; }

        [Column("CanDelete")]
        public bool CanDelete { get; set; }

        [Column("CanApprove")]
        public bool CanApprove { get; set; }

        [Column("CanReject")]
        public bool CanReject { get; set; }

        [Column("CanProcess")]
        public bool CanProcess { get; set; }

        [Column("MaxApprovalAmount")]
        public decimal? MaxApprovalAmount { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [ForeignKey("TransactionTypeId")]
        public virtual TransactionType TransactionType { get; set; }

        [ForeignKey("StageTypeId")]
        public virtual TransactionStageType StageType { get; set; }
    }
}
