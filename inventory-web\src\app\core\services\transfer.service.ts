import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TransactionHeader, TransferDTO } from '../models/transaction.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TransferService {
  private apiUrl = `${environment.apiUrl}/transfers`;

  constructor(private http: HttpClient) { }

  getTransfers(): Observable<TransactionHeader[]> {
    return this.http.get<TransactionHeader[]>(this.apiUrl);
  }

  getTransfer(id: number): Observable<TransferDTO> {
    return this.http.get<TransferDTO>(`${this.apiUrl}/${id}`);
  }

  createTransfer(transfer: TransferDTO): Observable<TransactionHeader> {
    return this.http.post<TransactionHeader>(this.apiUrl, transfer);
  }

  updateTransfer(id: number, transfer: TransferDTO): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, transfer);
  }

  deleteTransfer(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
