using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("RoleFormActionAccess")]
    public class RoleFormActionAccess
    {
        [Key]
        [Column("AccessId")]
        public int AccessId { get; set; }

        [Column("RoleId")]
        public int RoleId { get; set; }

        [Column("ActionId")]
        public int ActionId { get; set; }

        [Column("HasAccess")]
        public bool HasAccess { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("RoleId")]
        public virtual Role Role { get; set; }

        [ForeignKey("ActionId")]
        public virtual FormAction FormAction { get; set; }
    }
}
