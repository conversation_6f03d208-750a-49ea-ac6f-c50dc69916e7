using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("Period")]
    public class Period
    {
        [Key]
        [Column("PeriodId")]
        public int PeriodId { get; set; }

        [Column("PeriodNumber")]
        public int PeriodNumber { get; set; }

        [Column("FiscalYearId")]
        public int FiscalYearId { get; set; }

        [Column("StartDate")]
        public DateTime StartDate { get; set; }

        [Column("EndDate")]
        public DateTime EndDate { get; set; }

        [Column("IsClosed")]
        public bool IsClosed { get; set; }

        [Column("ClosedById")]
        public int? ClosedById { get; set; }

        [Column("ClosedDate")]
        public DateTime? ClosedDate { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("FiscalYearId")]
        public virtual FiscalYear FiscalYear { get; set; }

        [ForeignKey("ClosedById")]
        public virtual User ClosedBy { get; set; }

        // Related collections
        public virtual ICollection<StockTakeHeader> StockTakeHeaders { get; set; }
        public virtual ICollection<PeriodClose> PeriodCloses { get; set; }
    }
}
