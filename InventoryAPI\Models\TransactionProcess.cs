using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("TransactionProcess")]
    public class TransactionProcess
    {
        [Key]
        [Column("ProcessId")]
        public int ProcessId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("ProcessNumber")]
        public string ProcessNumber { get; set; }

        [StringLength(255)]
        [Column("Description")]
        public string Description { get; set; }

        [Column("CreatedById")]
        public int CreatedById { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("CreatedById")]
        public virtual User CreatedBy { get; set; }

        // Related collections
        public virtual ICollection<TransactionHeader> TransactionHeaders { get; set; }
    }
}
