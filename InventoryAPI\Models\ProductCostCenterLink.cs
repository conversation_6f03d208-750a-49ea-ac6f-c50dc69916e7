using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("ProductCostCenterLink")]
    public class ProductCostCenterLink
    {
        [Key]
        [Column("LinkId")]
        public int LinkId { get; set; }

        [Column("ProductId")]
        public int ProductId { get; set; }

        [Column("CostCenterId")]
        public int CostCenterId { get; set; }

        [Column("MaximumStock")]
        public decimal? MaximumStock { get; set; }

        [Column("MinimumStock")]
        public decimal? MinimumStock { get; set; }

        [Column("ReorderPoint")]
        public decimal? ReorderPoint { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("CostCenterId")]
        public virtual CostCenter CostCenter { get; set; }
    }
}
