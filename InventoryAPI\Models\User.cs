using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("User")]
    public class User
    {
        [Key]
        [Column("UserId")]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Username")]
        public string Username { get; set; }

        [StringLength(150)]
        [Column("Email")]
        public string Email { get; set; }

        [Required]
        [StringLength(255)]
        [Column("PasswordHash")]
        public string PasswordHash { get; set; }

        [StringLength(100)]
        [Column("FirstName")]
        public string FirstName { get; set; }

        [StringLength(100)]
        [Column("LastName")]
        public string LastName { get; set; }

        [StringLength(50)]
        [Column("PhoneNumber")]
        public string PhoneNumber { get; set; }

        [Column("RoleId")]
        public int RoleId { get; set; }

        [StringLength(10)]
        [Column("DefaultLanguage")]
        public string DefaultLanguage { get; set; }

        [Column("LastLoginDate")]
        public DateTime? LastLoginDate { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("RoleId")]
        public virtual Role Role { get; set; }

        // Related collections
        public virtual ICollection<UserCostCenterAccess> UserCostCenterAccesses { get; set; }
        public virtual ICollection<UserTransactionAccess> UserTransactionAccesses { get; set; }
        public virtual ICollection<UserStockTakeAccess> UserStockTakeAccesses { get; set; }
        public virtual ICollection<UserReportAccess> UserReportAccesses { get; set; }
        public virtual ICollection<UserProductAccess> UserProductAccesses { get; set; }
        public virtual ICollection<UserTransactionTypeAccess> UserTransactionTypeAccesses { get; set; }
        public virtual ICollection<UserFormActionAccess> UserFormActionAccesses { get; set; }
    }
}
