-- Script to create a new user in the Inventory Management System
-- Replace the values below with your desired user information

-- Example 1: Create a regular user
INSERT INTO [User] (
    [<PERSON>rname], 
    [Email], 
    [PasswordHash], 
    [FirstName], 
    [LastName], 
    [PhoneNumber],
    [RoleId], 
    [DefaultLanguage], 
    [CreatedAt], 
    [IsActive]
)
VALUES (
    'sami',                    -- Username (must be unique)
    '<EMAIL>',        -- Email (must be unique)
    'password123',                 -- Password (stored as plain text - not secure!)
    '<PERSON>',                        -- First Name
    'Doe',                         -- Last Name
    '+1234567890',                 -- Phone Number
2
    'en',                          -- Default Language
    GETDATE(),                     -- Created At
    1                              -- Is Active (1=Active, 0=Inactive)
);
