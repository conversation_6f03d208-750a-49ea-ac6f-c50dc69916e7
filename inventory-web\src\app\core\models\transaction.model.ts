export interface TransactionHeader {
  transactionId: number;
  transactionNumber: string;
  processId?: number;
  stageTypeId?: number;
  transactionTypeId: number;
  transactionTypeName?: string;
  sourceCostCenterId?: number;
  sourceCostCenterName?: string;
  destinationCostCenterId?: number;
  destinationCostCenterName?: string;
  supplierId?: number;
  supplierName?: string;
  referenceNumber?: string;
  transactionDate: Date;
  notes?: string;
  subTotal: number;
  taxAmount: number;
  totalAmount: number;
  discountAmount: number;
  discountPercentage: number;
  status: string;
  createdById: number;
  createdByName?: string;
  approvedById?: number;
  approvedByName?: string;
  approvedDate?: Date;
  createdAt: Date;
  updatedAt?: Date;
  isActive: boolean;
  details?: TransactionDetail[];
}

export interface TransactionDetail {
  transactionDetailId: number;
  transactionId: number;
  productId: number;
  productCode?: string;
  productName?: string;
  batchId?: number;
  batchNumber?: string;
  quantity: number;
  unitId: number;
  unitName?: string;
  unitPrice: number;
  taxId?: number;
  taxRate?: number;
  taxAmount?: number;
  discountAmount?: number;
  discountPercentage?: number;
  lineTotal: number;
  notes?: string;
  isRecipe: boolean;
  createdAt: Date;
  updatedAt?: Date;
  isActive: boolean;
}

// Order Request DTOs
export interface OrderRequestDTO {
  transactionId?: number;
  transactionNumber?: string;
  transactionDate: Date;
  sourceCostCenterId?: number;
  sourceCostCenterName?: string;
  destinationCostCenterId?: number;
  destinationCostCenterName?: string;
  notes?: string;
  status?: string;
  subTotal?: number;
  taxAmount?: number;
  totalAmount?: number;
  items: OrderRequestItemDTO[];
}

export interface OrderRequestItemDTO {
  transactionDetailId?: number;
  productId: number;
  productCode?: string;
  productName?: string;
  quantity: number;
  unitId: number;
  unitName?: string;
  unitPrice: number;
  lineTotal?: number;
}

// Receiving DTOs
export interface ReceivingDTO {
  transactionId?: number;
  transactionNumber?: string;
  transactionDate: Date;
  supplierId?: number;
  supplierName?: string;
  referenceNumber?: string;
  destinationCostCenterId?: number;
  destinationCostCenterName?: string;
  notes?: string;
  status?: string;
  subTotal?: number;
  taxAmount?: number;
  discountAmount?: number;
  discountPercentage?: number;
  totalAmount?: number;
  items: ReceivingItemDTO[];
}

export interface ReceivingItemDTO {
  transactionDetailId?: number;
  productId: number;
  productCode?: string;
  productName?: string;
  batchId?: number;
  batchNumber?: string;
  orderQuantity?: number;
  receivedQuantity: number;
  unitId: number;
  unitName?: string;
  unitPrice: number;
  taxId?: number;
  taxRate?: number;
  taxAmount?: number;
  discountAmount?: number;
  discountPercentage?: number;
  lineTotal?: number;
}

// Transfer DTOs
export interface TransferDTO {
  transactionId?: number;
  transactionNumber?: string;
  transactionDate: Date;
  sourceCostCenterId?: number;
  sourceCostCenterName?: string;
  destinationCostCenterId?: number;
  destinationCostCenterName?: string;
  notes?: string;
  status?: string;
  subTotal?: number;
  taxAmount?: number;
  totalAmount?: number;
  items: TransferItemDTO[];
}

export interface TransferItemDTO {
  transactionDetailId?: number;
  productId: number;
  productCode?: string;
  productName?: string;
  batchId?: number;
  batchNumber?: string;
  orderQuantity?: number;
  transferQuantity: number;
  unitId: number;
  unitName?: string;
  unitPrice: number;
  lineTotal?: number;
}
