using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("FiscalYear")]
    public class FiscalYear
    {
        [Key]
        [Column("FiscalYearId")]
        public int FiscalYearId { get; set; }

        [Column("Year")]
        public int Year { get; set; }

        [Column("StartDate")]
        public DateTime StartDate { get; set; }

        [Column("EndDate")]
        public DateTime EndDate { get; set; }

        [Column("IsClosed")]
        public bool IsClosed { get; set; }

        [Column("ClosedById")]
        public int? ClosedById { get; set; }

        [Column("ClosedDate")]
        public DateTime? ClosedDate { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("ClosedById")]
        public virtual User ClosedBy { get; set; }

        // Related collections
        public virtual ICollection<Period> Periods { get; set; }
        public virtual ICollection<CostCenterFiscalYear> CostCenterFiscalYears { get; set; }
        public virtual ICollection<StockTakeHeader> StockTakeHeaders { get; set; }
        public virtual ICollection<PeriodClose> PeriodCloses { get; set; }
    }
}
