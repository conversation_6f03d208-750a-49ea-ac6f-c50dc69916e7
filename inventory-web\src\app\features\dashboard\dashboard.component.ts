import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface DashboardCard {
  title: string;
  value: number;
  icon: string;
  color: string;
  route: string;
}

interface RecentTransaction {
  id: number;
  number: string;
  type: string;
  date: Date;
  status: string;
  amount: number;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  cards: DashboardCard[] = [
    {
      title: 'Products',
      value: 256,
      icon: 'bi-box',
      color: 'primary',
      route: '/products'
    },
    {
      title: 'Order Requests',
      value: 12,
      icon: 'bi-cart',
      color: 'success',
      route: '/order-requests'
    },
    {
      title: 'Receiving',
      value: 8,
      icon: 'bi-truck',
      color: 'info',
      route: '/receiving'
    },
    {
      title: 'Transfers',
      value: 15,
      icon: 'bi-arrow-repeat',
      color: 'warning',
      route: '/transfers'
    }
  ];

  recentTransactions: RecentTransaction[] = [
    {
      id: 1,
      number: 'OR-20250509-1234',
      type: 'Order Request',
      date: new Date(2025, 4, 9),
      status: 'Submitted',
      amount: 1250.75
    },
    {
      id: 2,
      number: 'GRN-20250508-5678',
      type: 'Receiving',
      date: new Date(2025, 4, 8),
      status: 'Completed',
      amount: 3450.25
    },
    {
      id: 3,
      number: 'TRF-20250507-9012',
      type: 'Transfer',
      date: new Date(2025, 4, 7),
      status: 'Completed',
      amount: 875.50
    },
    {
      id: 4,
      number: 'OR-20250506-3456',
      type: 'Order Request',
      date: new Date(2025, 4, 6),
      status: 'Draft',
      amount: 2100.00
    },
    {
      id: 5,
      number: 'GRN-20250505-7890',
      type: 'Receiving',
      date: new Date(2025, 4, 5),
      status: 'Completed',
      amount: 1675.30
    }
  ];

  constructor() { }

  ngOnInit(): void {
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'badge bg-success';
      case 'submitted':
        return 'badge bg-info';
      case 'draft':
        return 'badge bg-secondary';
      default:
        return 'badge bg-primary';
    }
  }
}
