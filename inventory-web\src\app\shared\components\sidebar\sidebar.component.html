<div class="sidebar">
  <div class="sidebar-header">
    <h5 class="mb-0">Menu</h5>
  </div>
  <div class="sidebar-content">
    <ul class="nav flex-column">
      <ng-container *ngFor="let item of menuItems">
        <!-- Menu item with children -->
        <li class="nav-item" *ngIf="item.children">
          <a class="nav-link" (click)="toggleMenu(item.label)" [class.active]="isExpanded(item.label)">
            <i class="bi {{ item.icon }} me-2"></i>
            {{ item.label }}
            <i class="bi" [ngClass]="isExpanded(item.label) ? 'bi-chevron-down' : 'bi-chevron-right'" style="float: right;"></i>
          </a>
          <ul class="nav flex-column submenu" [ngClass]="{'show': isExpanded(item.label)}">
            <li class="nav-item" *ngFor="let child of item.children">
              <a class="nav-link" [routerLink]="child.route" routerLinkActive="active">
                <i class="bi {{ child.icon }} me-2"></i>
                {{ child.label }}
              </a>
            </li>
          </ul>
        </li>
        
        <!-- Menu item without children -->
        <li class="nav-item" *ngIf="!item.children">
          <a class="nav-link" [routerLink]="item.route" routerLinkActive="active">
            <i class="bi {{ item.icon }} me-2"></i>
            {{ item.label }}
          </a>
        </li>
      </ng-container>
    </ul>
  </div>
</div>
