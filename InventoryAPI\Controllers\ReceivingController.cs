using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using InventoryAPI.Data;
using InventoryAPI.Models;
using InventoryAPI.DTOs;

namespace InventoryAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReceivingController : ControllerBase
    {
        private readonly InventoryDbContext _context;

        public ReceivingController(InventoryDbContext context)
        {
            _context = context;
        }

        // GET: api/Receiving
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TransactionHeader>>> GetReceivings()
        {
            // Get all transactions of type "Receiving"
            var receivingType = await _context.TransactionTypes
                .FirstOrDefaultAsync(t => t.Name == "Receiving");

            if (receivingType == null)
            {
                return NotFound("Receiving transaction type not found");
            }

            return await _context.TransactionHeaders
                .Include(t => t.TransactionType)
                .Include(t => t.SourceCostCenter)
                .Include(t => t.DestinationCostCenter)
                .Include(t => t.Supplier)
                .Where(t => t.TransactionTypeId == receivingType.TransactionTypeId && t.IsActive)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        // GET: api/Receiving/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ReceivingDTO>> GetReceiving(int id)
        {
            var transaction = await _context.TransactionHeaders
                .Include(t => t.TransactionType)
                .Include(t => t.SourceCostCenter)
                .Include(t => t.DestinationCostCenter)
                .Include(t => t.Supplier)
                .FirstOrDefaultAsync(t => t.TransactionId == id && t.IsActive);

            if (transaction == null)
            {
                return NotFound();
            }

            var details = await _context.TransactionDetails
                .Include(d => d.Product)
                .Include(d => d.Unit)
                .Include(d => d.Batch)
                .Where(d => d.TransactionId == id && d.IsActive)
                .ToListAsync();

            var receivingDTO = new ReceivingDTO
            {
                TransactionId = transaction.TransactionId,
                TransactionNumber = transaction.TransactionNumber,
                TransactionDate = transaction.TransactionDate,
                SupplierId = transaction.SupplierId,
                SupplierName = transaction.Supplier?.Name,
                ReferenceNumber = transaction.ReferenceNumber,
                DestinationCostCenterId = transaction.DestinationCostCenterId,
                DestinationCostCenterName = transaction.DestinationCostCenter?.Name,
                Notes = transaction.Notes,
                Status = transaction.Status,
                SubTotal = transaction.SubTotal,
                TaxAmount = transaction.TaxAmount,
                DiscountAmount = transaction.DiscountAmount,
                DiscountPercentage = transaction.DiscountPercentage,
                TotalAmount = transaction.TotalAmount,
                Items = details.Select(d => new ReceivingItemDTO
                {
                    TransactionDetailId = d.TransactionDetailId,
                    ProductId = d.ProductId,
                    ProductCode = d.Product.Code,
                    ProductName = d.Product.Name,
                    BatchId = d.BatchId,
                    BatchNumber = d.Batch?.BatchNumber,
                    OrderQuantity = d.Quantity, // This should be linked to the original order
                    ReceivedQuantity = d.Quantity,
                    UnitId = d.UnitId,
                    UnitName = d.Unit.Name,
                    UnitPrice = d.UnitPrice,
                    TaxId = d.TaxId,
                    TaxRate = d.TaxRate,
                    TaxAmount = d.TaxAmount,
                    DiscountAmount = d.DiscountAmount,
                    DiscountPercentage = d.DiscountPercentage,
                    LineTotal = d.LineTotal
                }).ToList()
            };

            return receivingDTO;
        }

        // POST: api/Receiving
        [HttpPost]
        public async Task<ActionResult<TransactionHeader>> CreateReceiving(ReceivingDTO receivingDTO)
        {
            // Get the Receiving transaction type
            var receivingType = await _context.TransactionTypes
                .FirstOrDefaultAsync(t => t.Name == "Receiving");

            if (receivingType == null)
            {
                return BadRequest("Receiving transaction type not found");
            }

            // Create the transaction header
            var transaction = new TransactionHeader
            {
                TransactionNumber = GenerateTransactionNumber(),
                TransactionTypeId = receivingType.TransactionTypeId,
                SupplierId = receivingDTO.SupplierId,
                ReferenceNumber = receivingDTO.ReferenceNumber,
                DestinationCostCenterId = receivingDTO.DestinationCostCenterId,
                TransactionDate = receivingDTO.TransactionDate,
                Notes = receivingDTO.Notes,
                Status = "Draft",
                SubTotal = receivingDTO.Items.Sum(i => i.ReceivedQuantity * i.UnitPrice),
                TaxAmount = receivingDTO.Items.Sum(i => i.TaxAmount ?? 0),
                DiscountAmount = receivingDTO.DiscountAmount,
                DiscountPercentage = receivingDTO.DiscountPercentage,
                TotalAmount = receivingDTO.Items.Sum(i => i.LineTotal),
                CreatedById = 1, // Replace with actual user ID from authentication
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _context.TransactionHeaders.Add(transaction);
            await _context.SaveChangesAsync();

            // Create the transaction details
            foreach (var item in receivingDTO.Items)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.TransactionId,
                    ProductId = item.ProductId,
                    BatchId = item.BatchId,
                    Quantity = item.ReceivedQuantity,
                    UnitId = item.UnitId,
                    UnitPrice = item.UnitPrice,
                    TaxId = item.TaxId,
                    TaxRate = item.TaxRate,
                    TaxAmount = item.TaxAmount,
                    DiscountAmount = item.DiscountAmount,
                    DiscountPercentage = item.DiscountPercentage,
                    LineTotal = item.LineTotal,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                _context.TransactionDetails.Add(detail);

                // Update stock on hand
                await UpdateStockOnHand(item.ProductId, receivingDTO.DestinationCostCenterId.Value, item.ReceivedQuantity, item.UnitPrice);
            }

            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetReceiving), new { id = transaction.TransactionId }, transaction);
        }

        // PUT: api/Receiving/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateReceiving(int id, ReceivingDTO receivingDTO)
        {
            if (id != receivingDTO.TransactionId)
            {
                return BadRequest();
            }

            var transaction = await _context.TransactionHeaders.FindAsync(id);
            if (transaction == null)
            {
                return NotFound();
            }

            // Update transaction header
            transaction.SupplierId = receivingDTO.SupplierId;
            transaction.ReferenceNumber = receivingDTO.ReferenceNumber;
            transaction.DestinationCostCenterId = receivingDTO.DestinationCostCenterId;
            transaction.TransactionDate = receivingDTO.TransactionDate;
            transaction.Notes = receivingDTO.Notes;
            transaction.SubTotal = receivingDTO.Items.Sum(i => i.ReceivedQuantity * i.UnitPrice);
            transaction.TaxAmount = receivingDTO.Items.Sum(i => i.TaxAmount ?? 0);
            transaction.DiscountAmount = receivingDTO.DiscountAmount;
            transaction.DiscountPercentage = receivingDTO.DiscountPercentage;
            transaction.TotalAmount = receivingDTO.Items.Sum(i => i.LineTotal);
            transaction.UpdatedAt = DateTime.UtcNow;

            _context.Entry(transaction).State = EntityState.Modified;

            // Get existing details to reverse stock changes
            var existingDetails = await _context.TransactionDetails
                .Where(d => d.TransactionId == id)
                .ToListAsync();

            // Reverse stock changes
            foreach (var detail in existingDetails)
            {
                await UpdateStockOnHand(detail.ProductId, transaction.DestinationCostCenterId.Value, -detail.Quantity, detail.UnitPrice);
                _context.TransactionDetails.Remove(detail);
            }

            // Add new details and update stock
            foreach (var item in receivingDTO.Items)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.TransactionId,
                    ProductId = item.ProductId,
                    BatchId = item.BatchId,
                    Quantity = item.ReceivedQuantity,
                    UnitId = item.UnitId,
                    UnitPrice = item.UnitPrice,
                    TaxId = item.TaxId,
                    TaxRate = item.TaxRate,
                    TaxAmount = item.TaxAmount,
                    DiscountAmount = item.DiscountAmount,
                    DiscountPercentage = item.DiscountPercentage,
                    LineTotal = item.LineTotal,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                _context.TransactionDetails.Add(detail);

                // Update stock on hand
                await UpdateStockOnHand(item.ProductId, transaction.DestinationCostCenterId.Value, item.ReceivedQuantity, item.UnitPrice);
            }

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TransactionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/Receiving/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteReceiving(int id)
        {
            var transaction = await _context.TransactionHeaders
                .Include(t => t.TransactionDetails)
                .FirstOrDefaultAsync(t => t.TransactionId == id);
                
            if (transaction == null)
            {
                return NotFound();
            }

            // Reverse stock changes
            foreach (var detail in transaction.TransactionDetails)
            {
                await UpdateStockOnHand(detail.ProductId, transaction.DestinationCostCenterId.Value, -detail.Quantity, detail.UnitPrice);
            }

            // Soft delete
            transaction.IsActive = false;
            transaction.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool TransactionExists(int id)
        {
            return _context.TransactionHeaders.Any(e => e.TransactionId == id);
        }

        private string GenerateTransactionNumber()
        {
            // Generate a unique transaction number
            // Format: GRN-YYYYMMDD-XXXX
            string prefix = "GRN";
            string date = DateTime.Now.ToString("yyyyMMdd");
            string random = new Random().Next(1000, 9999).ToString();
            
            return $"{prefix}-{date}-{random}";
        }

        private async Task UpdateStockOnHand(int productId, int costCenterId, decimal quantity, decimal unitPrice)
        {
            // Find existing stock record
            var stock = await _context.StockOnHands
                .FirstOrDefaultAsync(s => s.ProductId == productId && s.CostCenterId == costCenterId);

            if (stock == null)
            {
                // Create new stock record if it doesn't exist
                stock = new StockOnHand
                {
                    ProductId = productId,
                    CostCenterId = costCenterId,
                    Quantity = quantity,
                    BaseQuantity = quantity, // Assuming base unit for now
                    UnitId = await _context.Products
                        .Where(p => p.ProductId == productId)
                        .Select(p => p.UnitId)
                        .FirstOrDefaultAsync(),
                    CostPrice = unitPrice,
                    AverageCost = unitPrice,
                    LastUpdated = DateTime.UtcNow
                };

                _context.StockOnHands.Add(stock);
            }
            else
            {
                // Update existing stock record
                decimal oldQuantity = stock.Quantity;
                decimal oldCost = stock.AverageCost ?? unitPrice;
                
                stock.Quantity += quantity;
                
                // Calculate new average cost if adding stock
                if (quantity > 0)
                {
                    stock.AverageCost = ((oldQuantity * oldCost) + (quantity * unitPrice)) / (oldQuantity + quantity);
                }
                
                stock.CostPrice = unitPrice; // Update with latest cost
                stock.LastUpdated = DateTime.UtcNow;
                
                _context.Entry(stock).State = EntityState.Modified;
            }
        }
    }
}
