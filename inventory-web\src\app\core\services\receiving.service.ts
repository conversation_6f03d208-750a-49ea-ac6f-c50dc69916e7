import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ReceivingDTO, TransactionHeader } from '../models/transaction.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ReceivingService {
  private apiUrl = `${environment.apiUrl}/receiving`;

  constructor(private http: HttpClient) { }

  getReceivings(): Observable<TransactionHeader[]> {
    return this.http.get<TransactionHeader[]>(this.apiUrl);
  }

  getReceiving(id: number): Observable<ReceivingDTO> {
    return this.http.get<ReceivingDTO>(`${this.apiUrl}/${id}`);
  }

  createReceiving(receiving: ReceivingDTO): Observable<TransactionHeader> {
    return this.http.post<TransactionHeader>(this.apiUrl, receiving);
  }

  updateReceiving(id: number, receiving: ReceivingDTO): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, receiving);
  }

  deleteReceiving(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
