<div class="container-fluid">
  <div class="page-header d-flex justify-content-between align-items-center">
    <div>
      <h1>Order Request Details</h1>
      <p class="text-muted">View order request information</p>
    </div>
    <div>
      <a routerLink="/order-requests" class="btn btn-outline-secondary me-2">
        <i class="bi bi-arrow-left me-2"></i>Back to List
      </a>
      <div class="btn-group" *ngIf="orderRequest">
        <a [routerLink]="['/order-requests', orderRequest.transactionId, 'edit']" class="btn btn-primary" *ngIf="orderRequest.status === 'Draft'">
          <i class="bi bi-pencil me-2"></i>Edit
        </a>
        <button type="button" class="btn btn-success" *ngIf="orderRequest.status === 'Draft'" (click)="submitOrderRequest()" [disabled]="isSubmitting">
          <i class="bi bi-check-circle me-2"></i>Submit
        </button>
        <button type="button" class="btn btn-danger" *ngIf="orderRequest.status === 'Draft'" (click)="deleteOrderRequest()" [disabled]="isSubmitting">
          <i class="bi bi-trash me-2"></i>Delete
        </button>
      </div>
    </div>
  </div>

  <div *ngIf="isLoading">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div *ngIf="errorMessage" class="alert alert-danger">
    {{ errorMessage }}
  </div>

  <div *ngIf="!isLoading && orderRequest">
    <div class="row">
      <div class="col-md-8">
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">Order Request Information</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label fw-bold">Order Number</label>
                <p>{{ orderRequest.transactionNumber }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-bold">Date</label>
                <p>{{ orderRequest.transactionDate | date:'MMM d, y' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-bold">Source Cost Center</label>
                <p>{{ orderRequest.sourceCostCenterName || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-bold">Destination Cost Center</label>
                <p>{{ orderRequest.destinationCostCenterName || 'N/A' }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <label class="form-label fw-bold">Status</label>
                <p><span [ngClass]="getStatusClass(orderRequest.status || 'Draft')">{{ orderRequest.status || 'Draft' }}</span></p>
              </div>
              <div class="col-md-12 mb-3">
                <label class="form-label fw-bold">Notes</label>
                <p>{{ orderRequest.notes || 'No notes' }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">Items</h5>
          </div>
          <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table mb-0">
                <thead>
                  <tr>
                    <th style="width: 40%">Product</th>
                    <th style="width: 15%">Quantity</th>
                    <th style="width: 15%">Unit</th>
                    <th style="width: 15%">Price</th>
                    <th style="width: 15%">Total</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngIf="!orderRequest.items || orderRequest.items.length === 0">
                    <td colspan="5" class="text-center py-3">
                      No items in this order request.
                    </td>
                  </tr>
                  <tr *ngFor="let item of orderRequest.items">
                    <td>
                      <div>{{ item.productName }}</div>
                      <small class="text-muted">{{ item.productCode }}</small>
                    </td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.unitName }}</td>
                    <td>{{ item.unitPrice | currency }}</td>
                    <td>{{ item.quantity * item.unitPrice | currency }}</td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr>
                    <td colspan="4" class="text-end fw-bold">Total:</td>
                    <td>{{ calculateTotal() | currency }}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="card mb-4">
          <div class="card-header">
            <h5 class="mb-0">Actions</h5>
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <a [routerLink]="['/order-requests', orderRequest.transactionId, 'edit']" class="btn btn-primary" *ngIf="orderRequest.status === 'Draft'">
                <i class="bi bi-pencil me-2"></i>Edit Order Request
              </a>
              <button type="button" class="btn btn-success" *ngIf="orderRequest.status === 'Draft'" (click)="submitOrderRequest()" [disabled]="isSubmitting">
                <i class="bi bi-check-circle me-2"></i>Submit Order Request
              </button>
              <button type="button" class="btn btn-danger" *ngIf="orderRequest.status === 'Draft'" (click)="deleteOrderRequest()" [disabled]="isSubmitting">
                <i class="bi bi-trash me-2"></i>Delete Order Request
              </button>
              <a routerLink="/order-requests" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back to List
              </a>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">Order Request Workflow</h5>
          </div>
          <div class="card-body">
            <ul class="list-group list-group-flush">
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span>
                  <i class="bi bi-pencil-square me-2"></i>Draft
                </span>
                <span class="badge bg-secondary" *ngIf="orderRequest.status === 'Draft'">Current</span>
                <span class="badge bg-success" *ngIf="orderRequest.status !== 'Draft'">Completed</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span>
                  <i class="bi bi-send me-2"></i>Submitted
                </span>
                <span class="badge bg-secondary" *ngIf="orderRequest.status === 'Submitted'">Current</span>
                <span class="badge bg-success" *ngIf="['Approved', 'Completed'].includes(orderRequest.status || '')">Completed</span>
                <span class="badge bg-light text-dark" *ngIf="orderRequest.status === 'Draft'">Pending</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span>
                  <i class="bi bi-check-circle me-2"></i>Approved
                </span>
                <span class="badge bg-secondary" *ngIf="orderRequest.status === 'Approved'">Current</span>
                <span class="badge bg-success" *ngIf="orderRequest.status === 'Completed'">Completed</span>
                <span class="badge bg-light text-dark" *ngIf="['Draft', 'Submitted'].includes(orderRequest.status || '')">Pending</span>
              </li>
              <li class="list-group-item d-flex justify-content-between align-items-center">
                <span>
                  <i class="bi bi-check-all me-2"></i>Completed
                </span>
                <span class="badge bg-secondary" *ngIf="orderRequest.status === 'Completed'">Current</span>
                <span class="badge bg-light text-dark" *ngIf="['Draft', 'Submitted', 'Approved'].includes(orderRequest.status || '')">Pending</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
