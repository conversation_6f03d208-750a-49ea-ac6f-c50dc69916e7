{"version": 3, "sources": ["../../../../../node_modules/@auth0/angular-jwt/fesm2020/auth0-angular-jwt.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, NgModule, Optional, SkipSelf } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { map, mergeMap } from 'rxjs/operators';\nimport { defer, of } from 'rxjs';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\nconst JWT_OPTIONS = new InjectionToken('JWT_OPTIONS');\n\n/* eslint-disable no-bitwise */\nclass JwtHelperService {\n  constructor(config = null) {\n    this.tokenGetter = config && config.tokenGetter || function () {};\n  }\n  urlBase64Decode(str) {\n    let output = str.replace(/-/g, '+').replace(/_/g, '/');\n    switch (output.length % 4) {\n      case 0:\n        {\n          break;\n        }\n      case 2:\n        {\n          output += '==';\n          break;\n        }\n      case 3:\n        {\n          output += '=';\n          break;\n        }\n      default:\n        {\n          throw new Error('Illegal base64url string!');\n        }\n    }\n    return this.b64DecodeUnicode(output);\n  }\n  // credits for decoder goes to https://github.com/atk\n  b64decode(str) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n    let output = '';\n    str = String(str).replace(/=+$/, '');\n    if (str.length % 4 === 1) {\n      throw new Error(`'atob' failed: The string to be decoded is not correctly encoded.`);\n    }\n    for (\n    // initialize result and counters\n    let bc = 0, bs, buffer, idx = 0;\n    // get next character\n    buffer = str.charAt(idx++);\n    // character found in table? initialize bit storage and add its ascii value;\n    ~buffer && (bs = bc % 4 ? bs * 64 + buffer : buffer,\n    // and if not first of each 4 characters,\n    // convert the first 8 bits to one ascii character\n    bc++ % 4) ? output += String.fromCharCode(255 & bs >> (-2 * bc & 6)) : 0) {\n      // try to find character in table (0-63, not found => -1)\n      buffer = chars.indexOf(buffer);\n    }\n    return output;\n  }\n  b64DecodeUnicode(str) {\n    return decodeURIComponent(Array.prototype.map.call(this.b64decode(str), c => {\n      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n    }).join(''));\n  }\n  decodeToken(token = this.tokenGetter()) {\n    if (token instanceof Promise) {\n      return token.then(t => this._decodeToken(t));\n    }\n    return this._decodeToken(token);\n  }\n  _decodeToken(token) {\n    if (!token || token === '') {\n      return null;\n    }\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n      throw new Error(`The inspected token doesn't appear to be a JWT. Check to make sure it has three parts and see https://jwt.io for more.`);\n    }\n    const decoded = this.urlBase64Decode(parts[1]);\n    if (!decoded) {\n      throw new Error('Cannot decode the token.');\n    }\n    return JSON.parse(decoded);\n  }\n  getTokenExpirationDate(token = this.tokenGetter()) {\n    if (token instanceof Promise) {\n      return token.then(t => this._getTokenExpirationDate(t));\n    }\n    return this._getTokenExpirationDate(token);\n  }\n  _getTokenExpirationDate(token) {\n    let decoded;\n    decoded = this.decodeToken(token);\n    if (!decoded || !decoded.hasOwnProperty('exp')) {\n      return null;\n    }\n    const date = new Date(0);\n    date.setUTCSeconds(decoded.exp);\n    return date;\n  }\n  isTokenExpired(token = this.tokenGetter(), offsetSeconds) {\n    if (token instanceof Promise) {\n      return token.then(t => this._isTokenExpired(t, offsetSeconds));\n    }\n    return this._isTokenExpired(token, offsetSeconds);\n  }\n  _isTokenExpired(token, offsetSeconds) {\n    if (!token || token === '') {\n      return true;\n    }\n    const date = this.getTokenExpirationDate(token);\n    offsetSeconds = offsetSeconds || 0;\n    if (date === null) {\n      return false;\n    }\n    return !(date.valueOf() > new Date().valueOf() + offsetSeconds * 1000);\n  }\n  getAuthScheme(authScheme, request) {\n    if (typeof authScheme === 'function') {\n      return authScheme(request);\n    }\n    return authScheme;\n  }\n}\nJwtHelperService.ɵfac = function JwtHelperService_Factory(t) {\n  return new (t || JwtHelperService)(i0.ɵɵinject(JWT_OPTIONS));\n};\nJwtHelperService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JwtHelperService,\n  factory: JwtHelperService.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JwtHelperService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [JWT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\nconst fromPromiseOrValue = input => {\n  if (input instanceof Promise) {\n    return defer(() => input);\n  }\n  return of(input);\n};\nclass JwtInterceptor {\n  constructor(config, jwtHelper, document) {\n    this.jwtHelper = jwtHelper;\n    this.document = document;\n    this.standardPorts = ['80', '443'];\n    this.tokenGetter = config.tokenGetter;\n    this.headerName = config.headerName || 'Authorization';\n    this.authScheme = config.authScheme || config.authScheme === '' ? config.authScheme : 'Bearer ';\n    this.allowedDomains = config.allowedDomains || [];\n    this.disallowedRoutes = config.disallowedRoutes || [];\n    this.throwNoTokenError = config.throwNoTokenError || false;\n    this.skipWhenExpired = config.skipWhenExpired;\n  }\n  isAllowedDomain(request) {\n    const requestUrl = new URL(request.url, this.document.location.origin);\n    // If the host equals the current window origin,\n    // the domain is allowed by default\n    if (requestUrl.host === this.document.location.host) {\n      return true;\n    }\n    // If not the current domain, check the allowed list\n    const hostName = `${requestUrl.hostname}${requestUrl.port && !this.standardPorts.includes(requestUrl.port) ? ':' + requestUrl.port : ''}`;\n    return this.allowedDomains.findIndex(domain => typeof domain === 'string' ? domain === hostName : domain instanceof RegExp ? domain.test(hostName) : false) > -1;\n  }\n  isDisallowedRoute(request) {\n    const requestedUrl = new URL(request.url, this.document.location.origin);\n    return this.disallowedRoutes.findIndex(route => {\n      if (typeof route === 'string') {\n        const parsedRoute = new URL(route, this.document.location.origin);\n        return parsedRoute.hostname === requestedUrl.hostname && parsedRoute.pathname === requestedUrl.pathname;\n      }\n      if (route instanceof RegExp) {\n        return route.test(request.url);\n      }\n      return false;\n    }) > -1;\n  }\n  handleInterception(token, request, next) {\n    const authScheme = this.jwtHelper.getAuthScheme(this.authScheme, request);\n    if (!token && this.throwNoTokenError) {\n      throw new Error('Could not get token from tokenGetter function.');\n    }\n    let tokenIsExpired = of(false);\n    if (this.skipWhenExpired) {\n      tokenIsExpired = token ? fromPromiseOrValue(this.jwtHelper.isTokenExpired(token)) : of(true);\n    }\n    if (token) {\n      return tokenIsExpired.pipe(map(isExpired => isExpired && this.skipWhenExpired ? request.clone() : request.clone({\n        setHeaders: {\n          [this.headerName]: `${authScheme}${token}`\n        }\n      })), mergeMap(innerRequest => next.handle(innerRequest)));\n    }\n    return next.handle(request);\n  }\n  intercept(request, next) {\n    if (!this.isAllowedDomain(request) || this.isDisallowedRoute(request)) {\n      return next.handle(request);\n    }\n    const token = this.tokenGetter(request);\n    return fromPromiseOrValue(token).pipe(mergeMap(asyncToken => {\n      return this.handleInterception(asyncToken, request, next);\n    }));\n  }\n}\nJwtInterceptor.ɵfac = function JwtInterceptor_Factory(t) {\n  return new (t || JwtInterceptor)(i0.ɵɵinject(JWT_OPTIONS), i0.ɵɵinject(JwtHelperService), i0.ɵɵinject(DOCUMENT));\n};\nJwtInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JwtInterceptor,\n  factory: JwtInterceptor.ɵfac\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JwtInterceptor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [JWT_OPTIONS]\n      }]\n    }, {\n      type: JwtHelperService\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nclass JwtModule {\n  constructor(parentModule) {\n    if (parentModule) {\n      throw new Error(`JwtModule is already loaded. It should only be imported in your application's main module.`);\n    }\n  }\n  static forRoot(options) {\n    return {\n      ngModule: JwtModule,\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: JwtInterceptor,\n        multi: true\n      }, options.jwtOptionsProvider || {\n        provide: JWT_OPTIONS,\n        useValue: options.config\n      }, JwtHelperService]\n    };\n  }\n}\nJwtModule.ɵfac = function JwtModule_Factory(t) {\n  return new (t || JwtModule)(i0.ɵɵinject(JwtModule, 12));\n};\nJwtModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: JwtModule\n});\nJwtModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JwtModule, [{\n    type: NgModule\n  }], function () {\n    return [{\n      type: JwtModule,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }];\n  }, null);\n})();\n\n/*\n * Public API Surface of angular-jwt\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { JWT_OPTIONS, JwtHelperService, JwtInterceptor, JwtModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,cAAc,IAAI,eAAe,aAAa;AAGpD,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,SAAS,MAAM;AACzB,SAAK,cAAc,UAAU,OAAO,eAAe,WAAY;AAAA,IAAC;AAAA,EAClE;AAAA,EACA,gBAAgB,KAAK;AACnB,QAAI,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACrD,YAAQ,OAAO,SAAS,GAAG;AAAA,MACzB,KAAK,GACH;AACE;AAAA,MACF;AAAA,MACF,KAAK,GACH;AACE,kBAAU;AACV;AAAA,MACF;AAAA,MACF,KAAK,GACH;AACE,kBAAU;AACV;AAAA,MACF;AAAA,MACF,SACE;AACE,cAAM,IAAI,MAAM,2BAA2B;AAAA,MAC7C;AAAA,IACJ;AACA,WAAO,KAAK,iBAAiB,MAAM;AAAA,EACrC;AAAA;AAAA,EAEA,UAAU,KAAK;AACb,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,UAAM,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AACnC,QAAI,IAAI,SAAS,MAAM,GAAG;AACxB,YAAM,IAAI,MAAM,mEAAmE;AAAA,IACrF;AACA;AAAA,UAEI,KAAK,GAAG,IAAI,QAAQ,MAAM;AAAA;AAAA,MAE9B,SAAS,IAAI,OAAO,KAAK;AAAA;AAAA,MAEzB,CAAC,WAAW,KAAK,KAAK,IAAI,KAAK,KAAK,SAAS;AAAA;AAAA,MAG7C,OAAO,KAAK,UAAU,OAAO,aAAa,MAAM,OAAO,KAAK,KAAK,EAAE,IAAI;AAAA,MAAG;AAExE,eAAS,MAAM,QAAQ,MAAM;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,KAAK;AACpB,WAAO,mBAAmB,MAAM,UAAU,IAAI,KAAK,KAAK,UAAU,GAAG,GAAG,OAAK;AAC3E,aAAO,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;AAAA,IAC7D,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,EACb;AAAA,EACA,YAAY,QAAQ,KAAK,YAAY,GAAG;AACtC,QAAI,iBAAiB,SAAS;AAC5B,aAAO,MAAM,KAAK,OAAK,KAAK,aAAa,CAAC,CAAC;AAAA,IAC7C;AACA,WAAO,KAAK,aAAa,KAAK;AAAA,EAChC;AAAA,EACA,aAAa,OAAO;AAClB,QAAI,CAAC,SAAS,UAAU,IAAI;AAC1B,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,QAAI,MAAM,WAAW,GAAG;AACtB,YAAM,IAAI,MAAM,wHAAwH;AAAA,IAC1I;AACA,UAAM,UAAU,KAAK,gBAAgB,MAAM,CAAC,CAAC;AAC7C,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC5C;AACA,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B;AAAA,EACA,uBAAuB,QAAQ,KAAK,YAAY,GAAG;AACjD,QAAI,iBAAiB,SAAS;AAC5B,aAAO,MAAM,KAAK,OAAK,KAAK,wBAAwB,CAAC,CAAC;AAAA,IACxD;AACA,WAAO,KAAK,wBAAwB,KAAK;AAAA,EAC3C;AAAA,EACA,wBAAwB,OAAO;AAC7B,QAAI;AACJ,cAAU,KAAK,YAAY,KAAK;AAChC,QAAI,CAAC,WAAW,CAAC,QAAQ,eAAe,KAAK,GAAG;AAC9C,aAAO;AAAA,IACT;AACA,UAAM,OAAO,oBAAI,KAAK,CAAC;AACvB,SAAK,cAAc,QAAQ,GAAG;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,eAAe,QAAQ,KAAK,YAAY,GAAG,eAAe;AACxD,QAAI,iBAAiB,SAAS;AAC5B,aAAO,MAAM,KAAK,OAAK,KAAK,gBAAgB,GAAG,aAAa,CAAC;AAAA,IAC/D;AACA,WAAO,KAAK,gBAAgB,OAAO,aAAa;AAAA,EAClD;AAAA,EACA,gBAAgB,OAAO,eAAe;AACpC,QAAI,CAAC,SAAS,UAAU,IAAI;AAC1B,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK,uBAAuB,KAAK;AAC9C,oBAAgB,iBAAiB;AACjC,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,WAAO,EAAE,KAAK,QAAQ,KAAI,oBAAI,KAAK,GAAE,QAAQ,IAAI,gBAAgB;AAAA,EACnE;AAAA,EACA,cAAc,YAAY,SAAS;AACjC,QAAI,OAAO,eAAe,YAAY;AACpC,aAAO,WAAW,OAAO;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACF;AACA,iBAAiB,OAAO,SAAS,yBAAyB,GAAG;AAC3D,SAAO,KAAK,KAAK,kBAAqB,SAAS,WAAW,CAAC;AAC7D;AACA,iBAAiB,QAA0B,mBAAmB;AAAA,EAC5D,OAAO;AAAA,EACP,SAAS,iBAAiB;AAC5B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,qBAAqB,WAAS;AAClC,MAAI,iBAAiB,SAAS;AAC5B,WAAO,MAAM,MAAM,KAAK;AAAA,EAC1B;AACA,SAAO,GAAG,KAAK;AACjB;AACA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,QAAQ,WAAW,UAAU;AACvC,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,gBAAgB,CAAC,MAAM,KAAK;AACjC,SAAK,cAAc,OAAO;AAC1B,SAAK,aAAa,OAAO,cAAc;AACvC,SAAK,aAAa,OAAO,cAAc,OAAO,eAAe,KAAK,OAAO,aAAa;AACtF,SAAK,iBAAiB,OAAO,kBAAkB,CAAC;AAChD,SAAK,mBAAmB,OAAO,oBAAoB,CAAC;AACpD,SAAK,oBAAoB,OAAO,qBAAqB;AACrD,SAAK,kBAAkB,OAAO;AAAA,EAChC;AAAA,EACA,gBAAgB,SAAS;AACvB,UAAM,aAAa,IAAI,IAAI,QAAQ,KAAK,KAAK,SAAS,SAAS,MAAM;AAGrE,QAAI,WAAW,SAAS,KAAK,SAAS,SAAS,MAAM;AACnD,aAAO;AAAA,IACT;AAEA,UAAM,WAAW,GAAG,WAAW,QAAQ,GAAG,WAAW,QAAQ,CAAC,KAAK,cAAc,SAAS,WAAW,IAAI,IAAI,MAAM,WAAW,OAAO,EAAE;AACvI,WAAO,KAAK,eAAe,UAAU,YAAU,OAAO,WAAW,WAAW,WAAW,WAAW,kBAAkB,SAAS,OAAO,KAAK,QAAQ,IAAI,KAAK,IAAI;AAAA,EAChK;AAAA,EACA,kBAAkB,SAAS;AACzB,UAAM,eAAe,IAAI,IAAI,QAAQ,KAAK,KAAK,SAAS,SAAS,MAAM;AACvE,WAAO,KAAK,iBAAiB,UAAU,WAAS;AAC9C,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,cAAc,IAAI,IAAI,OAAO,KAAK,SAAS,SAAS,MAAM;AAChE,eAAO,YAAY,aAAa,aAAa,YAAY,YAAY,aAAa,aAAa;AAAA,MACjG;AACA,UAAI,iBAAiB,QAAQ;AAC3B,eAAO,MAAM,KAAK,QAAQ,GAAG;AAAA,MAC/B;AACA,aAAO;AAAA,IACT,CAAC,IAAI;AAAA,EACP;AAAA,EACA,mBAAmB,OAAO,SAAS,MAAM;AACvC,UAAM,aAAa,KAAK,UAAU,cAAc,KAAK,YAAY,OAAO;AACxE,QAAI,CAAC,SAAS,KAAK,mBAAmB;AACpC,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AACA,QAAI,iBAAiB,GAAG,KAAK;AAC7B,QAAI,KAAK,iBAAiB;AACxB,uBAAiB,QAAQ,mBAAmB,KAAK,UAAU,eAAe,KAAK,CAAC,IAAI,GAAG,IAAI;AAAA,IAC7F;AACA,QAAI,OAAO;AACT,aAAO,eAAe,KAAK,IAAI,eAAa,aAAa,KAAK,kBAAkB,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA,QAC9G,YAAY;AAAA,UACV,CAAC,KAAK,UAAU,GAAG,GAAG,UAAU,GAAG,KAAK;AAAA,QAC1C;AAAA,MACF,CAAC,CAAC,GAAG,SAAS,kBAAgB,KAAK,OAAO,YAAY,CAAC,CAAC;AAAA,IAC1D;AACA,WAAO,KAAK,OAAO,OAAO;AAAA,EAC5B;AAAA,EACA,UAAU,SAAS,MAAM;AACvB,QAAI,CAAC,KAAK,gBAAgB,OAAO,KAAK,KAAK,kBAAkB,OAAO,GAAG;AACrE,aAAO,KAAK,OAAO,OAAO;AAAA,IAC5B;AACA,UAAM,QAAQ,KAAK,YAAY,OAAO;AACtC,WAAO,mBAAmB,KAAK,EAAE,KAAK,SAAS,gBAAc;AAC3D,aAAO,KAAK,mBAAmB,YAAY,SAAS,IAAI;AAAA,IAC1D,CAAC,CAAC;AAAA,EACJ;AACF;AACA,eAAe,OAAO,SAAS,uBAAuB,GAAG;AACvD,SAAO,KAAK,KAAK,gBAAmB,SAAS,WAAW,GAAM,SAAS,gBAAgB,GAAM,SAAS,QAAQ,CAAC;AACjH;AACA,eAAe,QAA0B,mBAAmB;AAAA,EAC1D,OAAO;AAAA,EACP,SAAS,eAAe;AAC1B,CAAC;AAAA,CACA,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,YAAY,cAAc;AACxB,QAAI,cAAc;AAChB,YAAM,IAAI,MAAM,4FAA4F;AAAA,IAC9G;AAAA,EACF;AAAA,EACA,OAAO,QAAQ,SAAS;AACtB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT,GAAG,QAAQ,sBAAsB;AAAA,QAC/B,SAAS;AAAA,QACT,UAAU,QAAQ;AAAA,MACpB,GAAG,gBAAgB;AAAA,IACrB;AAAA,EACF;AACF;AACA,UAAU,OAAO,SAAS,kBAAkB,GAAG;AAC7C,SAAO,KAAK,KAAK,WAAc,SAAS,WAAW,EAAE,CAAC;AACxD;AACA,UAAU,OAAyB,iBAAiB;AAAA,EAClD,MAAM;AACR,CAAC;AACD,UAAU,OAAyB,iBAAiB,CAAC,CAAC;AAAA,CACrD,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,GAAG;AAAA,QACD,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;", "names": []}