using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("UserReportAccess")]
    public class UserReportAccess
    {
        [Key]
        [Column("ReportAccessId")]
        public int ReportAccessId { get; set; }

        [Column("UserId")]
        public int UserId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("ReportCode")]
        public string ReportCode { get; set; }

        [Column("CanView")]
        public bool CanView { get; set; }

        [Column("CanExport")]
        public bool CanExport { get; set; }

        [Column("CanSchedule")]
        public bool CanSchedule { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }
    }
}
