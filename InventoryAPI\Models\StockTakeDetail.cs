using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("StockTakeDetail")]
    public class StockTakeDetail
    {
        [Key]
        [Column("StockTakeDetailId")]
        public int StockTakeDetailId { get; set; }

        [Column("StockTakeId")]
        public int StockTakeId { get; set; }

        [Column("ProductId")]
        public int ProductId { get; set; }

        [Column("BatchId")]
        public int? BatchId { get; set; }

        [Column("SystemQuantity")]
        public decimal SystemQuantity { get; set; }

        [Column("ActualQuantity")]
        public decimal? ActualQuantity { get; set; }

        [Column("VarianceQuantity")]
        public decimal? VarianceQuantity { get; set; }

        [Column("UnitId")]
        public int UnitId { get; set; }

        [Column("CostPrice")]
        public decimal? CostPrice { get; set; }

        [Column("VarianceValue")]
        public decimal? VarianceValue { get; set; }

        [Column("Notes")]
        public string Notes { get; set; }

        [Column("OpeningQuantity")]
        public decimal? OpeningQuantity { get; set; }

        [Column("ClosingQuantity")]
        public decimal? ClosingQuantity { get; set; }

        [Column("StockTakeTypeId")]
        public int? StockTakeTypeId { get; set; }

        [Column("LastCountDate")]
        public DateTime? LastCountDate { get; set; }

        [Column("CountedById")]
        public int? CountedById { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("StockTakeId")]
        public virtual StockTakeHeader StockTakeHeader { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("BatchId")]
        public virtual Batch Batch { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; }

        [ForeignKey("StockTakeTypeId")]
        public virtual StockTakeType StockTakeType { get; set; }

        [ForeignKey("CountedById")]
        public virtual User CountedBy { get; set; }
    }
}
