using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace InventoryAPI.Models
{
    [Table("Currencies")]
    public class Currency
    {
        [Key]
        [Column("CurrencyId")]
        public int CurrencyId { get; set; }

        [Column("Code")]
        [StringLength(3)]
        public string Code { get; set; }

        [Column("Name")]
        [StringLength(50)]
        public string Name { get; set; }

        [Column("Symbol")]
        [StringLength(5)]
        public string Symbol { get; set; }

        [Column("ExchangeRate")]
        [Precision(18, 6)]
        public decimal ExchangeRate { get; set; }

        [Column("IsBaseCurrency")]
        public bool IsBaseCurrency { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        // Related collections
        public virtual ICollection<Payment> Payments { get; set; }
    }
}
