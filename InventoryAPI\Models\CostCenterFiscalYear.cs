using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("CostCenterFiscalYear")]
    public class CostCenterFiscalYear
    {
        [Key]
        [Column("CostCenterFiscalYearId")]
        public int CostCenterFiscalYearId { get; set; }

        [Column("CostCenterId")]
        public int CostCenterId { get; set; }

        [Column("FiscalYearId")]
        public int FiscalYearId { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        [ForeignKey("CostCenterId")]
        public virtual CostCenter CostCenter { get; set; }

        [ForeignKey("FiscalYearId")]
        public virtual FiscalYear FiscalYear { get; set; }
    }
}
