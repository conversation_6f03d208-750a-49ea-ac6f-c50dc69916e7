-- Create Role table first since Use<PERSON> references it
CREATE TABLE [Role] (
    [RoleId] int NOT NULL IDENTITY,
    [Name] nvarchar(50) NOT NULL,
    [Description] nvarchar(255) NOT NULL,
    [IsAdmin] bit NOT NULL,
    [IsSystem] bit NOT NULL,
    [DefaultCanViewCostCenters] bit NOT NULL,
    [DefaultCanCreateCostCenters] bit NOT NULL,
    [DefaultCanEditCostCenters] bit NOT NULL,
    [DefaultCanDeleteCostCenters] bit NOT NULL,
    [DefaultCanApproveCostCenters] bit NOT NULL,
    [DefaultCanViewTransactions] bit NOT NULL,
    [DefaultCanCreateTransactions] bit NOT NULL,
    [DefaultCanEditTransactions] bit NOT NULL,
    [DefaultCanDeleteTransactions] bit NOT NULL,
    [DefaultCanApproveTransactions] bit NOT NULL,
    [DefaultCanRejectTransactions] bit NOT NULL,
    [DefaultCanProcessTransactions] bit NOT NULL,
    [DefaultMaxApprovalAmount] decimal(18,2) NULL,
    [DefaultCanViewStockTakes] bit NOT NULL,
    [DefaultCanCreateStockTakes] bit NOT NULL,
    [DefaultCanEditStockTakes] bit NOT NULL,
    [DefaultCanDeleteStockTakes] bit NOT NULL,
    [DefaultCanCountStockTakes] bit NOT NULL,
    [DefaultCanFinalizeStockTakes] bit NOT NULL,
    [DefaultCanReopenStockTakes] bit NOT NULL,
    [DefaultCanViewReports] bit NOT NULL,
    [DefaultCanExportReports] bit NOT NULL,
    [DefaultCanScheduleReports] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Role] PRIMARY KEY ([RoleId])
);

-- Create User table
CREATE TABLE [User] (
    [UserId] int NOT NULL IDENTITY,
    [Username] nvarchar(50) NOT NULL,
    [Email] nvarchar(150) NOT NULL,
    [PasswordHash] nvarchar(255) NOT NULL,
    [FirstName] nvarchar(100) NOT NULL,
    [LastName] nvarchar(100) NOT NULL,
    [PhoneNumber] nvarchar(50) NOT NULL,
    [RoleId] int NOT NULL,
    [DefaultLanguage] nvarchar(10) NOT NULL,
    [LastLoginDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_User] PRIMARY KEY ([UserId]),
    CONSTRAINT [FK_User_Role_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Role] ([RoleId]) ON DELETE CASCADE
);

-- Insert a default admin role
INSERT INTO [Role] (
    [Name], [Description], [IsAdmin], [IsSystem],
    [DefaultCanViewCostCenters], [DefaultCanCreateCostCenters], [DefaultCanEditCostCenters], 
    [DefaultCanDeleteCostCenters], [DefaultCanApproveCostCenters],
    [DefaultCanViewTransactions], [DefaultCanCreateTransactions], [DefaultCanEditTransactions], 
    [DefaultCanDeleteTransactions], [DefaultCanApproveTransactions], [DefaultCanRejectTransactions], 
    [DefaultCanProcessTransactions], [DefaultMaxApprovalAmount],
    [DefaultCanViewStockTakes], [DefaultCanCreateStockTakes], [DefaultCanEditStockTakes], 
    [DefaultCanDeleteStockTakes], [DefaultCanCountStockTakes], [DefaultCanFinalizeStockTakes], 
    [DefaultCanReopenStockTakes],
    [DefaultCanViewReports], [DefaultCanExportReports], [DefaultCanScheduleReports],
    [CreatedAt], [IsActive]
)
VALUES (
    'Administrator', 'System Administrator', 1, 1,
    1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, NULL,
    1, 1, 1, 1, 1, 1, 1,
    1, 1, 1,
    GETDATE(), 1
);

-- Insert a default admin user (username: admin, password: admin)
INSERT INTO [User] (
    [Username], [Email], [PasswordHash], [FirstName], [LastName], [PhoneNumber],
    [RoleId], [DefaultLanguage], [CreatedAt], [IsActive]
)
VALUES (
    'admin', '<EMAIL>', 'admin', 'System', 'Administrator', '123456789',
    1, 'en', GETDATE(), 1
);
