import { Routes } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';

import { MainLayoutComponent } from './shared/layouts/main-layout/main-layout.component';
import { AuthLayoutComponent } from './shared/layouts/auth-layout/auth-layout.component';

export const routes: Routes = [
  {
    path: '',
    component: MainLayoutComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
      },
      {
        path: 'order-requests',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/order-requests/order-request-list/order-request-list.component').then(m => m.OrderRequestListComponent)
          },
          {
            path: 'new',
            loadComponent: () => import('./features/order-requests/order-request-form/order-request-form.component').then(m => m.OrderRequestFormComponent)
          },
          {
            path: ':id',
            loadComponent: () => import('./features/order-requests/order-request-detail/order-request-detail.component').then(m => m.OrderRequestDetailComponent)
          },
          {
            path: ':id/edit',
            loadComponent: () => import('./features/order-requests/order-request-form/order-request-form.component').then(m => m.OrderRequestFormComponent)
          }
        ]
      },
      {
        path: 'receiving',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/receiving/receiving-list/receiving-list.component').then(m => m.ReceivingListComponent)
          }
        ]
      },
      {
        path: 'transfers',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/transfers/transfer-list/transfer-list.component').then(m => m.TransferListComponent)
          }
        ]
      },
      {
        path: 'products',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/products/product-list/product-list.component').then(m => m.ProductListComponent)
          }
        ]
      },
      {
        path: 'suppliers',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/suppliers/supplier-list/supplier-list.component').then(m => m.SupplierListComponent)
          }
        ]
      },
      {
        path: 'cost-centers',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/cost-centers/cost-center-list/cost-center-list.component').then(m => m.CostCenterListComponent)
          }
        ]
      }
    ]
  },
  {
    path: 'auth',
    component: AuthLayoutComponent,
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      },
      {
        path: 'login',
        loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
      }
    ]
  },
  {
    path: '**',
    redirectTo: 'dashboard'
  }
];
