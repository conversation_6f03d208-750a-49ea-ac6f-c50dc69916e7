using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using InventoryAPI.Data;
using InventoryAPI.Models;
using InventoryAPI.DTOs;

namespace InventoryAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class OrderRequestController : ControllerBase
    {
        private readonly InventoryDbContext _context;

        public OrderRequestController(InventoryDbContext context)
        {
            _context = context;
        }

        // GET: api/OrderRequest
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TransactionHeader>>> GetOrderRequests()
        {
            // Get all transactions of type "Order Request"
            var orderRequestType = await _context.TransactionTypes
                .FirstOrDefaultAsync(t => t.Name == "Order Request");

            if (orderRequestType == null)
            {
                return NotFound("Order Request transaction type not found");
            }

            return await _context.TransactionHeaders
                .Include(t => t.TransactionType)
                .Include(t => t.SourceCostCenter)
                .Include(t => t.DestinationCostCenter)
                .Where(t => t.TransactionTypeId == orderRequestType.TransactionTypeId && t.IsActive)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        // GET: api/OrderRequest/5
        [HttpGet("{id}")]
        public async Task<ActionResult<OrderRequestDTO>> GetOrderRequest(int id)
        {
            var transaction = await _context.TransactionHeaders
                .Include(t => t.TransactionType)
                .Include(t => t.SourceCostCenter)
                .Include(t => t.DestinationCostCenter)
                .FirstOrDefaultAsync(t => t.TransactionId == id && t.IsActive);

            if (transaction == null)
            {
                return NotFound();
            }

            var details = await _context.TransactionDetails
                .Include(d => d.Product)
                .Include(d => d.Unit)
                .Where(d => d.TransactionId == id && d.IsActive)
                .ToListAsync();

            var orderRequestDTO = new OrderRequestDTO
            {
                TransactionId = transaction.TransactionId,
                TransactionNumber = transaction.TransactionNumber,
                TransactionDate = transaction.TransactionDate,
                SourceCostCenterId = transaction.SourceCostCenterId,
                SourceCostCenterName = transaction.SourceCostCenter?.Name,
                DestinationCostCenterId = transaction.DestinationCostCenterId,
                DestinationCostCenterName = transaction.DestinationCostCenter?.Name,
                Notes = transaction.Notes,
                Status = transaction.Status,
                SubTotal = transaction.SubTotal,
                TaxAmount = transaction.TaxAmount,
                TotalAmount = transaction.TotalAmount,
                Items = details.Select(d => new OrderRequestItemDTO
                {
                    TransactionDetailId = d.TransactionDetailId,
                    ProductId = d.ProductId,
                    ProductCode = d.Product.Code,
                    ProductName = d.Product.Name,
                    Quantity = d.Quantity,
                    UnitId = d.UnitId,
                    UnitName = d.Unit.Name,
                    UnitPrice = d.UnitPrice,
                    LineTotal = d.LineTotal
                }).ToList()
            };

            return orderRequestDTO;
        }

        // POST: api/OrderRequest
        [HttpPost]
        public async Task<ActionResult<TransactionHeader>> CreateOrderRequest(OrderRequestDTO orderRequestDTO)
        {
            // Get the Order Request transaction type
            var orderRequestType = await _context.TransactionTypes
                .FirstOrDefaultAsync(t => t.Name == "Order Request");

            if (orderRequestType == null)
            {
                return BadRequest("Order Request transaction type not found");
            }

            // Create the transaction header
            var transaction = new TransactionHeader
            {
                TransactionNumber = GenerateTransactionNumber(),
                TransactionTypeId = orderRequestType.TransactionTypeId,
                SourceCostCenterId = orderRequestDTO.SourceCostCenterId,
                DestinationCostCenterId = orderRequestDTO.DestinationCostCenterId,
                TransactionDate = orderRequestDTO.TransactionDate,
                Notes = orderRequestDTO.Notes,
                Status = "Draft",
                SubTotal = orderRequestDTO.Items.Sum(i => i.Quantity * i.UnitPrice),
                TaxAmount = 0, // Calculate tax if needed
                TotalAmount = orderRequestDTO.Items.Sum(i => i.Quantity * i.UnitPrice),
                CreatedById = 1, // Replace with actual user ID from authentication
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _context.TransactionHeaders.Add(transaction);
            await _context.SaveChangesAsync();

            // Create the transaction details
            foreach (var item in orderRequestDTO.Items)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.TransactionId,
                    ProductId = item.ProductId,
                    Quantity = item.Quantity,
                    UnitId = item.UnitId,
                    UnitPrice = item.UnitPrice,
                    LineTotal = item.Quantity * item.UnitPrice,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                _context.TransactionDetails.Add(detail);
            }

            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetOrderRequest), new { id = transaction.TransactionId }, transaction);
        }

        // PUT: api/OrderRequest/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateOrderRequest(int id, OrderRequestDTO orderRequestDTO)
        {
            if (id != orderRequestDTO.TransactionId)
            {
                return BadRequest();
            }

            var transaction = await _context.TransactionHeaders.FindAsync(id);
            if (transaction == null)
            {
                return NotFound();
            }

            // Update transaction header
            transaction.SourceCostCenterId = orderRequestDTO.SourceCostCenterId;
            transaction.DestinationCostCenterId = orderRequestDTO.DestinationCostCenterId;
            transaction.TransactionDate = orderRequestDTO.TransactionDate;
            transaction.Notes = orderRequestDTO.Notes;
            transaction.SubTotal = orderRequestDTO.Items.Sum(i => i.Quantity * i.UnitPrice);
            transaction.TotalAmount = transaction.SubTotal + transaction.TaxAmount;
            transaction.UpdatedAt = DateTime.UtcNow;

            _context.Entry(transaction).State = EntityState.Modified;

            // Delete existing details
            var existingDetails = await _context.TransactionDetails
                .Where(d => d.TransactionId == id)
                .ToListAsync();

            foreach (var detail in existingDetails)
            {
                _context.TransactionDetails.Remove(detail);
            }

            // Add new details
            foreach (var item in orderRequestDTO.Items)
            {
                var detail = new TransactionDetail
                {
                    TransactionId = transaction.TransactionId,
                    ProductId = item.ProductId,
                    Quantity = item.Quantity,
                    UnitId = item.UnitId,
                    UnitPrice = item.UnitPrice,
                    LineTotal = item.Quantity * item.UnitPrice,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                _context.TransactionDetails.Add(detail);
            }

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TransactionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/OrderRequest/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteOrderRequest(int id)
        {
            var transaction = await _context.TransactionHeaders.FindAsync(id);
            if (transaction == null)
            {
                return NotFound();
            }

            // Soft delete
            transaction.IsActive = false;
            transaction.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // POST: api/OrderRequest/5/Submit
        [HttpPost("{id}/Submit")]
        public async Task<IActionResult> SubmitOrderRequest(int id)
        {
            var transaction = await _context.TransactionHeaders.FindAsync(id);
            if (transaction == null)
            {
                return NotFound();
            }

            transaction.Status = "Submitted";
            transaction.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool TransactionExists(int id)
        {
            return _context.TransactionHeaders.Any(e => e.TransactionId == id);
        }

        private string GenerateTransactionNumber()
        {
            // Generate a unique transaction number
            // Format: OR-YYYYMMDD-XXXX
            string prefix = "OR";
            string date = DateTime.Now.ToString("yyyyMMdd");
            string random = new Random().Next(1000, 9999).ToString();
            
            return $"{prefix}-{date}-{random}";
        }
    }
}
