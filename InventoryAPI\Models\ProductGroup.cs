using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("ProductGroup")]
    public class ProductGroup
    {
        [Key]
        [Column("GroupId")]
        public int GroupId { get; set; }

        [Required]
        [StringLength(150)]
        [Column("Name")]
        public string Name { get; set; }

        [Column("DepartmentId")]
        public int? DepartmentId { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; }
        
        public virtual ICollection<ProductSubGroup> ProductSubGroups { get; set; }
        public virtual ICollection<Product> Products { get; set; }
    }
}
