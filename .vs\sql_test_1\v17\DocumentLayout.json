{"Version": 1, "WorkspaceRootPath": "D:\\SCM_web\\sql_test_1\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}|InventoryAPI\\InventoryAPI.csproj|d:\\scm_web\\sql_test_1\\inventoryapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}|InventoryAPI\\InventoryAPI.csproj|solutionrelative:inventoryapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}|InventoryAPI\\InventoryAPI.csproj|d:\\scm_web\\sql_test_1\\inventoryapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}|InventoryAPI\\InventoryAPI.csproj|solutionrelative:inventoryapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}|InventoryAPI\\InventoryAPI.csproj|d:\\scm_web\\sql_test_1\\inventoryapi\\inventoryapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}|InventoryAPI\\InventoryAPI.csproj|solutionrelative:inventoryapi\\inventoryapi.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "D:\\SCM_web\\sql_test_1\\InventoryAPI\\appsettings.json", "RelativeDocumentMoniker": "InventoryAPI\\appsettings.json", "ToolTip": "D:\\SCM_web\\sql_test_1\\InventoryAPI\\appsettings.json", "RelativeToolTip": "InventoryAPI\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-15T00:05:29.275Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "launchSettings.json", "DocumentMoniker": "D:\\SCM_web\\sql_test_1\\InventoryAPI\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "InventoryAPI\\Properties\\launchSettings.json", "ToolTip": "D:\\SCM_web\\sql_test_1\\InventoryAPI\\Properties\\launchSettings.json", "RelativeToolTip": "InventoryAPI\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-15T00:05:24.435Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "InventoryAPI", "DocumentMoniker": "D:\\SCM_web\\sql_test_1\\InventoryAPI\\InventoryAPI.csproj", "RelativeDocumentMoniker": "InventoryAPI\\InventoryAPI.csproj", "ToolTip": "D:\\SCM_web\\sql_test_1\\InventoryAPI\\InventoryAPI.csproj", "RelativeToolTip": "InventoryAPI\\InventoryAPI.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-15T00:03:33.834Z", "EditorCaption": ""}]}]}]}