using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("StockOnHand")]
    public class StockOnHand
    {
        [Key]
        [Column("StockId")]
        public int StockId { get; set; }

        [Column("ProductId")]
        public int ProductId { get; set; }

        [Column("CostCenterId")]
        public int CostCenterId { get; set; }

        [Column("Quantity")]
        public decimal Quantity { get; set; }

        [Column("BaseQuantity")]
        public decimal BaseQuantity { get; set; }

        [Column("UnitId")]
        public int? UnitId { get; set; }

        [Column("AverageCost")]
        public decimal? AverageCost { get; set; }

        [Column("CostPrice")]
        public decimal? CostPrice { get; set; }

        [Column("ReturnVariance")]
        public decimal? ReturnVariance { get; set; }

        [Column("NetCost")]
        public decimal? NetCost { get; set; }

        [Column("SalesPrice")]
        public decimal? SalesPrice { get; set; }

        [Column("LastUpdated")]
        public DateTime LastUpdated { get; set; }

        // Navigation properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("CostCenterId")]
        public virtual CostCenter CostCenter { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; }
    }
}
