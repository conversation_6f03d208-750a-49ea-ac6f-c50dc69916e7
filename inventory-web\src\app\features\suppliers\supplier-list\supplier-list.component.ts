import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlaceholderComponent } from '../../../shared/components/placeholder/placeholder.component';

@Component({
  selector: 'app-supplier-list',
  standalone: true,
  imports: [CommonModule, PlaceholderComponent],
  template: `
    <app-placeholder 
      title="Suppliers Management" 
      message="The suppliers management feature is coming soon. This will allow you to manage your suppliers."
      icon="bi-people"
      backLink="/dashboard"
      backLabel="Back to Dashboard">
    </app-placeholder>
  `
})
export class SupplierListComponent {}
