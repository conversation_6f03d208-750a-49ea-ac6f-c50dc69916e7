import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { OrderRequestService } from '../../../core/services/order-request.service';
import { TransactionHeader } from '../../../core/models/transaction.model';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-order-request-list',
  standalone: true,
  imports: [CommonModule, RouterModule, LoadingSpinnerComponent],
  templateUrl: './order-request-list.component.html',
  styleUrls: ['./order-request-list.component.scss']
})
export class OrderRequestListComponent implements OnInit {
  orderRequests: TransactionHeader[] = [];
  isLoading = false;
  errorMessage = '';
  
  constructor(private orderRequestService: OrderRequestService) { }

  ngOnInit(): void {
    this.loadOrderRequests();
  }

  loadOrderRequests(): void {
    this.isLoading = true;
    this.orderRequestService.getOrderRequests()
      .subscribe({
        next: (data) => {
          this.orderRequests = data;
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = 'Failed to load order requests. Please try again later.';
          console.error('Error loading order requests:', error);
          this.isLoading = false;
        }
      });
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'badge bg-secondary';
      case 'submitted':
        return 'badge bg-primary';
      case 'approved':
        return 'badge bg-success';
      case 'rejected':
        return 'badge bg-danger';
      case 'completed':
        return 'badge bg-info';
      default:
        return 'badge bg-secondary';
    }
  }

  deleteOrderRequest(id: number, event: Event): void {
    event.stopPropagation();
    if (confirm('Are you sure you want to delete this order request?')) {
      this.orderRequestService.deleteOrderRequest(id)
        .subscribe({
          next: () => {
            this.orderRequests = this.orderRequests.filter(or => or.transactionId !== id);
          },
          error: (error) => {
            this.errorMessage = 'Failed to delete order request. Please try again later.';
            console.error('Error deleting order request:', error);
          }
        });
    }
  }
}
