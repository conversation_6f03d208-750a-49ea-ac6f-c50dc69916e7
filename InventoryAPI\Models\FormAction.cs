using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("FormAction")]
    public class FormAction
    {
        [Key]
        [Column("ActionId")]
        public int ActionId { get; set; }

        [Column("FormId")]
        public int FormId { get; set; }

        [Required]
        [StringLength(100)]
        [Column("ActionName")]
        public string ActionName { get; set; }

        [StringLength(255)]
        [Column("ActionDescription")]
        public string ActionDescription { get; set; }

        [Required]
        [StringLength(50)]
        [Column("ActionCode")]
        public string ActionCode { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("FormId")]
        public virtual ApplicationForm ApplicationForm { get; set; }

        public virtual ICollection<RoleFormActionAccess> RoleFormActionAccesses { get; set; }
        public virtual ICollection<UserFormActionAccess> UserFormActionAccesses { get; set; }
    }
}
