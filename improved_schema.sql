-- Improved Schema for Inventory Management System
-- Designed for .NET Core and Angular Web Application

USE [master]
GO

CREATE DATABASE [InventoryManagement]
GO

USE [InventoryManagement]
GO

-- Enable foreign key constraints
EXEC sp_MSforeachtable 'ALTER TABLE ? NOCHECK CONSTRAINT ALL'
GO

-- =============================================
-- CORE TABLES
-- =============================================

-- Department Table
CREATE TABLE [dbo].[Department](
    [DepartmentId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Department] PRIMARY KEY CLUSTERED ([DepartmentId] ASC),
    CONSTRAINT [UQ_Department_Name] UNIQUE ([Name])
)
GO

-- Product Group Table
CREATE TABLE [dbo].[ProductGroup](
    [GroupId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [DepartmentId] [int] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_ProductGroup] PRIMARY KEY CLUSTERED ([GroupId] ASC),
    CONSTRAINT [UQ_ProductGroup_Name] UNIQUE ([Name]),
    CONSTRAINT [FK_ProductGroup_Department] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Department] ([DepartmentId])
)
GO

-- Product SubGroup Table
CREATE TABLE [dbo].[ProductSubGroup](
    [SubGroupId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [GroupId] [int] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_ProductSubGroup] PRIMARY KEY CLUSTERED ([SubGroupId] ASC),
    CONSTRAINT [UQ_ProductSubGroup_Name] UNIQUE ([Name]),
    CONSTRAINT [FK_ProductSubGroup_ProductGroup] FOREIGN KEY ([GroupId]) REFERENCES [dbo].[ProductGroup] ([GroupId])
)
GO

-- Unit Table
CREATE TABLE [dbo].[Unit](
    [UnitId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Abbreviation] [nvarchar](10) NULL,
    [UnitGroupId] [int] NULL,
    [BaseConversionFactor] [decimal](18, 6) NOT NULL DEFAULT 1,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Unit] PRIMARY KEY CLUSTERED ([UnitId] ASC),
    CONSTRAINT [UQ_Unit_Name] UNIQUE ([Name])
)
GO

-- Brand Table
CREATE TABLE [dbo].[Brand](
    [BrandId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Brand] PRIMARY KEY CLUSTERED ([BrandId] ASC),
    CONSTRAINT [UQ_Brand_Name] UNIQUE ([Name])
)
GO

-- Tax Table
CREATE TABLE [dbo].[Tax](
    [TaxId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](100) NOT NULL,
    [Rate] [decimal](5, 2) NOT NULL,
    [IsDefault] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Tax] PRIMARY KEY CLUSTERED ([TaxId] ASC),
    CONSTRAINT [UQ_Tax_Name] UNIQUE ([Name])
)
GO

-- Product Table with support for sub-recipes
CREATE TABLE [dbo].[Product](
    [ProductId] [int] IDENTITY(1,1) NOT NULL,
    [Code] [nvarchar](50) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [BrandId] [int] NULL,
    [UnitId] [int] NULL,
    [UnitGroupId] [int] NULL,
    [DepartmentId] [int] NULL,
    [GroupId] [int] NULL,
    [SubGroupId] [int] NULL,
    [CostPrice] [decimal](18, 4) NULL,
    [AverageCost] [decimal](18, 4) NULL,
    [SalesPrice] [decimal](18, 4) NULL,
    [MinStock] [decimal](18, 4) NULL,
    [MaxStock] [decimal](18, 4) NULL,
    [ReorderPoint] [decimal](18, 4) NULL,
    [Notes] [nvarchar](max) NULL,
    [IsStockItem] [bit] NOT NULL DEFAULT 1,
    [IsRecipe] [bit] NOT NULL DEFAULT 0,
    [HasExpiry] [bit] NOT NULL DEFAULT 0,
    [IsProduction] [bit] NOT NULL DEFAULT 0,
    [IsSaleable] [bit] NOT NULL DEFAULT 1,
    [TaxId] [int] NULL,
    [SalesUnitId] [int] NULL,
    [SalesUnitConversionFactor] [decimal](18, 6) NULL,
    [AllowDiscount] [bit] NOT NULL DEFAULT 1,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Product] PRIMARY KEY CLUSTERED ([ProductId] ASC),
    CONSTRAINT [UQ_Product_Code] UNIQUE ([Code]),
    CONSTRAINT [FK_Product_Brand] FOREIGN KEY ([BrandId]) REFERENCES [dbo].[Brand] ([BrandId]),
    CONSTRAINT [FK_Product_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId]),
    CONSTRAINT [FK_Product_Department] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Department] ([DepartmentId]),
    CONSTRAINT [FK_Product_ProductGroup] FOREIGN KEY ([GroupId]) REFERENCES [dbo].[ProductGroup] ([GroupId]),
    CONSTRAINT [FK_Product_ProductSubGroup] FOREIGN KEY ([SubGroupId]) REFERENCES [dbo].[ProductSubGroup] ([SubGroupId]),
    CONSTRAINT [FK_Product_Tax] FOREIGN KEY ([TaxId]) REFERENCES [dbo].[Tax] ([TaxId]),
    CONSTRAINT [FK_Product_SalesUnit] FOREIGN KEY ([SalesUnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- Recipe Table - Enhanced to support sub-recipes
CREATE TABLE [dbo].[Recipe](
    [RecipeId] [int] IDENTITY(1,1) NOT NULL,
    [ProductId] [int] NOT NULL,
    [Name] [nvarchar](150) NULL,
    [Description] [nvarchar](max) NULL,
    [YieldQuantity] [decimal](18, 4) NOT NULL DEFAULT 1,
    [YieldUnitId] [int] NULL,
    [TotalCost] [decimal](18, 4) NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    CONSTRAINT [PK_Recipe] PRIMARY KEY CLUSTERED ([RecipeId] ASC),
    CONSTRAINT [FK_Recipe_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_Recipe_Unit] FOREIGN KEY ([YieldUnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- Recipe Ingredients Table - Supports sub-recipes
CREATE TABLE [dbo].[RecipeIngredient](
    [RecipeIngredientId] [int] IDENTITY(1,1) NOT NULL,
    [RecipeId] [int] NOT NULL,
    [IngredientProductId] [int] NOT NULL,
    [Quantity] [decimal](18, 4) NOT NULL,
    [UnitId] [int] NOT NULL,
    [CostPerUnit] [decimal](18, 4) NULL,
    [TotalCost] [decimal](18, 4) NULL,
    [IsSubRecipe] [bit] NOT NULL DEFAULT 0,
    [WastagePercentage] [decimal](5, 2) NULL DEFAULT 0,
    [Notes] [nvarchar](max) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_RecipeIngredient] PRIMARY KEY CLUSTERED ([RecipeIngredientId] ASC),
    CONSTRAINT [FK_RecipeIngredient_Recipe] FOREIGN KEY ([RecipeId]) REFERENCES [dbo].[Recipe] ([RecipeId]),
    CONSTRAINT [FK_RecipeIngredient_Product] FOREIGN KEY ([IngredientProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_RecipeIngredient_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- Location Table
CREATE TABLE [dbo].[Location](
    [LocationId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [Address] [nvarchar](255) NULL,
    [City] [nvarchar](100) NULL,
    [State] [nvarchar](100) NULL,
    [Country] [nvarchar](100) NULL,
    [PostalCode] [nvarchar](20) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Location] PRIMARY KEY CLUSTERED ([LocationId] ASC),
    CONSTRAINT [UQ_Location_Name] UNIQUE ([Name])
)
GO

-- Store Table
CREATE TABLE [dbo].[Store](
    [StoreId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [LocationId] [int] NULL,
    [IsSalesPoint] [bit] NOT NULL DEFAULT 0,
    [LogoPath] [nvarchar](255) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Store] PRIMARY KEY CLUSTERED ([StoreId] ASC),
    CONSTRAINT [UQ_Store_Name] UNIQUE ([Name]),
    CONSTRAINT [FK_Store_Location] FOREIGN KEY ([LocationId]) REFERENCES [dbo].[Location] ([LocationId])
)
GO

-- Cost Center Table
CREATE TABLE [dbo].[CostCenter](
    [CostCenterId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [StoreId] [int] NULL,
    [TypeId] [int] NULL,
    [TypeName] [nvarchar](100) NULL,
    [AutoTransfer] [bit] NOT NULL DEFAULT 0,
    [IsSalesPoint] [bit] NOT NULL DEFAULT 0,
    [Abbreviation] [nvarchar](50) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_CostCenter] PRIMARY KEY CLUSTERED ([CostCenterId] ASC),
    CONSTRAINT [UQ_CostCenter_Name] UNIQUE ([Name]),
    CONSTRAINT [FK_CostCenter_Store] FOREIGN KEY ([StoreId]) REFERENCES [dbo].[Store] ([StoreId])
)
GO

-- =============================================
-- INVENTORY MANAGEMENT TABLES
-- =============================================

-- Create a sequence for stock take numbers
CREATE SEQUENCE [dbo].[StockTakeNumberSequence]
    START WITH 1
    INCREMENT BY 1;
GO

-- Fiscal Year Table
CREATE TABLE [dbo].[FiscalYear](
    [FiscalYearId] [int] IDENTITY(1,1) NOT NULL,
    [Year] [int] NOT NULL,
    [StartDate] [datetime2] NOT NULL,
    [EndDate] [datetime2] NOT NULL,
    [IsClosed] [bit] NOT NULL DEFAULT 0,
    [ClosedById] [int] NULL,
    [ClosedDate] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_FiscalYear] PRIMARY KEY CLUSTERED ([FiscalYearId] ASC),
    CONSTRAINT [UQ_FiscalYear_Year] UNIQUE ([Year])
)
GO

-- Period Table for tracking fiscal periods
CREATE TABLE [dbo].[Period](
    [PeriodId] [int] IDENTITY(1,1) NOT NULL,
    [PeriodNumber] [int] NOT NULL,
    [FiscalYearId] [int] NOT NULL,
    [StartDate] [datetime2] NOT NULL,
    [EndDate] [datetime2] NOT NULL,
    [IsClosed] [bit] NOT NULL DEFAULT 0,
    [ClosedById] [int] NULL,
    [ClosedDate] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Period] PRIMARY KEY CLUSTERED ([PeriodId] ASC),
    CONSTRAINT [UQ_Period_FiscalYear_Number] UNIQUE ([FiscalYearId], [PeriodNumber]),
    CONSTRAINT [FK_Period_FiscalYear] FOREIGN KEY ([FiscalYearId]) REFERENCES [dbo].[FiscalYear] ([FiscalYearId])
)
GO

-- Cost Center Fiscal Year Link Table
CREATE TABLE [dbo].[CostCenterFiscalYear](
    [CostCenterFiscalYearId] [int] IDENTITY(1,1) NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [FiscalYearId] [int] NOT NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    CONSTRAINT [PK_CostCenterFiscalYear] PRIMARY KEY CLUSTERED ([CostCenterFiscalYearId] ASC),
    CONSTRAINT [UQ_CostCenterFiscalYear] UNIQUE ([CostCenterId], [FiscalYearId]),
    CONSTRAINT [FK_CostCenterFiscalYear_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_CostCenterFiscalYear_FiscalYear] FOREIGN KEY ([FiscalYearId]) REFERENCES [dbo].[FiscalYear] ([FiscalYearId])
)
GO

-- Stock Take Type Table to support different stocktaking methods
CREATE TABLE [dbo].[StockTakeType](
    [StockTakeTypeId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_StockTakeType] PRIMARY KEY CLUSTERED ([StockTakeTypeId] ASC),
    CONSTRAINT [UQ_StockTakeType_Name] UNIQUE ([Name])
)
GO

-- Insert default stock take types
INSERT INTO [dbo].[StockTakeType] ([Name], [Description], [CreatedAt], [IsActive])
VALUES
    ('Counted', 'Physical count by staff', GETDATE(), 1),
    ('System', 'System-generated count', GETDATE(), 1),
    ('Zero Count', 'Zero count for items not found', GETDATE(), 1);
GO

-- Stock On Hand Table
CREATE TABLE [dbo].[StockOnHand](
    [StockId] [int] IDENTITY(1,1) NOT NULL,
    [ProductId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [Quantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [BaseQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [UnitId] [int] NULL,
    [AverageCost] [decimal](18, 4) NULL,
    [CostPrice] [decimal](18, 4) NULL,
    [ReturnVariance] [decimal](18, 4) NULL DEFAULT 0,
    [NetCost] [decimal](18, 4) NULL,
    [SalesPrice] [decimal](18, 4) NULL,
    [LastUpdated] [datetime2] NOT NULL DEFAULT GETDATE(),
    CONSTRAINT [PK_StockOnHand] PRIMARY KEY CLUSTERED ([StockId] ASC),
    CONSTRAINT [UQ_StockOnHand_Product_CostCenter] UNIQUE ([ProductId], [CostCenterId]),
    CONSTRAINT [FK_StockOnHand_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_StockOnHand_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_StockOnHand_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- Product Cost Center Link Table
CREATE TABLE [dbo].[ProductCostCenterLink](
    [LinkId] [int] IDENTITY(1,1) NOT NULL,
    [ProductId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [MaximumStock] [decimal](18, 4) NULL,
    [MinimumStock] [decimal](18, 4) NULL,
    [ReorderPoint] [decimal](18, 4) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_ProductCostCenterLink] PRIMARY KEY CLUSTERED ([LinkId] ASC),
    CONSTRAINT [UQ_ProductCostCenterLink] UNIQUE ([ProductId], [CostCenterId]),
    CONSTRAINT [FK_ProductCostCenterLink_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_ProductCostCenterLink_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId])
)
GO

-- Batch/Lot Tracking Table
CREATE TABLE [dbo].[Batch](
    [BatchId] [int] IDENTITY(1,1) NOT NULL,
    [BatchNumber] [nvarchar](150) NOT NULL,
    [ProductId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [ManufactureDate] [datetime2] NULL,
    [ExpiryDate] [datetime2] NULL,
    [InitialQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [CurrentQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [UnitId] [int] NULL,
    [CostPrice] [decimal](18, 4) NULL,
    [IsOpen] [bit] NOT NULL DEFAULT 1,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Batch] PRIMARY KEY CLUSTERED ([BatchId] ASC),
    CONSTRAINT [UQ_Batch_Number_Product_CostCenter] UNIQUE ([BatchNumber], [ProductId], [CostCenterId]),
    CONSTRAINT [FK_Batch_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_Batch_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_Batch_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId])
)
GO

-- =============================================
-- TRANSACTION TABLES
-- =============================================

-- Transaction Type Table
CREATE TABLE [dbo].[TransactionType](
    [TransactionTypeId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [AffectsInventory] [bit] NOT NULL DEFAULT 1,
    [IsSale] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionType] PRIMARY KEY CLUSTERED ([TransactionTypeId] ASC),
    CONSTRAINT [UQ_TransactionType_Name] UNIQUE ([Name])
)
GO

-- Supplier Table
CREATE TABLE [dbo].[Supplier](
    [SupplierId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [ContactPerson] [nvarchar](150) NULL,
    [Email] [nvarchar](150) NULL,
    [Phone] [nvarchar](50) NULL,
    [Address] [nvarchar](255) NULL,
    [City] [nvarchar](100) NULL,
    [State] [nvarchar](100) NULL,
    [Country] [nvarchar](100) NULL,
    [PostalCode] [nvarchar](20) NULL,
    [TaxNumber] [nvarchar](50) NULL,
    [Notes] [nvarchar](max) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Supplier] PRIMARY KEY CLUSTERED ([SupplierId] ASC),
    CONSTRAINT [UQ_Supplier_Name] UNIQUE ([Name])
)
GO

-- Transaction Process Table - To support the three-part transaction process
CREATE TABLE [dbo].[TransactionProcess](
    [ProcessId] [int] IDENTITY(1,1) NOT NULL,
    [ProcessNumber] [nvarchar](50) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [CreatedById] [int] NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionProcess] PRIMARY KEY CLUSTERED ([ProcessId] ASC),
    CONSTRAINT [UQ_TransactionProcess_ProcessNumber] UNIQUE ([ProcessNumber])
)
GO

-- Transaction Stage Type Table - To define the stages in the transaction process
CREATE TABLE [dbo].[TransactionStageType](
    [StageTypeId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [Sequence] [int] NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionStageType] PRIMARY KEY CLUSTERED ([StageTypeId] ASC),
    CONSTRAINT [UQ_TransactionStageType_Name] UNIQUE ([Name])
)
GO

-- Insert default transaction stage types
INSERT INTO [dbo].[TransactionStageType] ([Name], [Description], [Sequence], [CreatedAt], [IsActive])
VALUES
    ('Request', 'Product request stage', 1, GETDATE(), 1),
    ('Order', 'Product order stage', 2, GETDATE(), 1),
    ('Receiving', 'Product receiving stage', 3, GETDATE(), 1),
    ('Transfer', 'Product transfer to another cost center', 4, GETDATE(), 1),
    ('Adjustment', 'Inventory adjustment', 5, GETDATE(), 1),
    ('Production', 'Production of recipes', 6, GETDATE(), 1),
    ('Return', 'Return from customer', 7, GETDATE(), 1),
    ('ReturnToSupplier', 'Return to supplier', 8, GETDATE(), 1);
GO

-- Transaction Header Table - Enhanced to support the three-part transaction process
CREATE TABLE [dbo].[TransactionHeader](
    [TransactionId] [int] IDENTITY(1,1) NOT NULL,
    [TransactionNumber] [nvarchar](50) NOT NULL,
    [ProcessId] [int] NULL, -- Link to the overall transaction process
    [StageTypeId] [int] NULL, -- Identifies which stage this transaction represents
    [TransactionTypeId] [int] NOT NULL,
    [SourceCostCenterId] [int] NULL,
    [DestinationCostCenterId] [int] NULL,
    [SupplierId] [int] NULL,
    [ReferenceNumber] [nvarchar](50) NULL,
    [TransactionDate] [datetime2] NOT NULL,
    [Notes] [nvarchar](max) NULL,
    [SubTotal] [decimal](18, 4) NOT NULL DEFAULT 0,
    [TaxAmount] [decimal](18, 4) NOT NULL DEFAULT 0,
    [TotalAmount] [decimal](18, 4) NOT NULL DEFAULT 0,
    [DiscountAmount] [decimal](18, 4) NOT NULL DEFAULT 0,
    [DiscountPercentage] [decimal](5, 2) NOT NULL DEFAULT 0,
    [Status] [nvarchar](20) NOT NULL DEFAULT 'Draft', -- Draft, Submitted, Approved, Rejected, Cancelled
    [CreatedById] [int] NOT NULL,
    [ApprovedById] [int] NULL,
    [ApprovedDate] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionHeader] PRIMARY KEY CLUSTERED ([TransactionId] ASC),
    CONSTRAINT [UQ_TransactionHeader_TransactionNumber] UNIQUE ([TransactionNumber]),
    CONSTRAINT [FK_TransactionHeader_TransactionProcess] FOREIGN KEY ([ProcessId]) REFERENCES [dbo].[TransactionProcess] ([ProcessId]),
    CONSTRAINT [FK_TransactionHeader_TransactionStageType] FOREIGN KEY ([StageTypeId]) REFERENCES [dbo].[TransactionStageType] ([StageTypeId]),
    CONSTRAINT [FK_TransactionHeader_TransactionType] FOREIGN KEY ([TransactionTypeId]) REFERENCES [dbo].[TransactionType] ([TransactionTypeId]),
    CONSTRAINT [FK_TransactionHeader_SourceCostCenter] FOREIGN KEY ([SourceCostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_TransactionHeader_DestinationCostCenter] FOREIGN KEY ([DestinationCostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_TransactionHeader_Supplier] FOREIGN KEY ([SupplierId]) REFERENCES [dbo].[Supplier] ([SupplierId])
)
GO

-- Transaction Detail Table
CREATE TABLE [dbo].[TransactionDetail](
    [TransactionDetailId] [int] IDENTITY(1,1) NOT NULL,
    [TransactionId] [int] NOT NULL,
    [ProductId] [int] NOT NULL,
    [BatchId] [int] NULL,
    [Quantity] [decimal](18, 4) NOT NULL,
    [UnitId] [int] NOT NULL,
    [UnitPrice] [decimal](18, 4) NOT NULL,
    [TaxId] [int] NULL,
    [TaxRate] [decimal](5, 2) NULL,
    [TaxAmount] [decimal](18, 4) NULL,
    [DiscountAmount] [decimal](18, 4) NULL DEFAULT 0,
    [DiscountPercentage] [decimal](5, 2) NULL DEFAULT 0,
    [LineTotal] [decimal](18, 4) NOT NULL,
    [Notes] [nvarchar](max) NULL,
    [IsRecipe] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_TransactionDetail] PRIMARY KEY CLUSTERED ([TransactionDetailId] ASC),
    CONSTRAINT [FK_TransactionDetail_TransactionHeader] FOREIGN KEY ([TransactionId]) REFERENCES [dbo].[TransactionHeader] ([TransactionId]),
    CONSTRAINT [FK_TransactionDetail_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_TransactionDetail_Batch] FOREIGN KEY ([BatchId]) REFERENCES [dbo].[Batch] ([BatchId]),
    CONSTRAINT [FK_TransactionDetail_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId]),
    CONSTRAINT [FK_TransactionDetail_Tax] FOREIGN KEY ([TaxId]) REFERENCES [dbo].[Tax] ([TaxId])
)
GO

-- Stock Take Header Table - Enhanced with period tracking and finalization
CREATE TABLE [dbo].[StockTakeHeader](
    [StockTakeId] [int] IDENTITY(1,1) NOT NULL,
    [StockTakeNumber] [nvarchar](50) NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [FiscalYearId] [int] NOT NULL,
    [PeriodId] [int] NULL,
    [StockTakeDate] [datetime2] NOT NULL,
    [Notes] [nvarchar](max) NULL,
    [Status] [nvarchar](20) NOT NULL DEFAULT 'Draft', -- Draft, In Progress, Completed, Cancelled
    [TotalVariance] [decimal](18, 4) NULL,
    [StockTakeTypeId] [int] NULL,
    [CreatedById] [int] NOT NULL,
    [CompletedById] [int] NULL,
    [CompletedDate] [datetime2] NULL,
    [IsFinalized] [bit] NOT NULL DEFAULT 0,
    [FinalizedById] [int] NULL,
    [FinalizedDate] [datetime2] NULL,
    [IsReopened] [bit] NOT NULL DEFAULT 0,
    [ReopenedById] [int] NULL,
    [ReopenedDate] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_StockTakeHeader] PRIMARY KEY CLUSTERED ([StockTakeId] ASC),
    CONSTRAINT [UQ_StockTakeHeader_StockTakeNumber] UNIQUE ([StockTakeNumber]),
    CONSTRAINT [FK_StockTakeHeader_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_StockTakeHeader_FiscalYear] FOREIGN KEY ([FiscalYearId]) REFERENCES [dbo].[FiscalYear] ([FiscalYearId]),
    CONSTRAINT [FK_StockTakeHeader_Period] FOREIGN KEY ([PeriodId]) REFERENCES [dbo].[Period] ([PeriodId]),
    CONSTRAINT [FK_StockTakeHeader_StockTakeType] FOREIGN KEY ([StockTakeTypeId]) REFERENCES [dbo].[StockTakeType] ([StockTakeTypeId]),
    CONSTRAINT [FK_StockTakeHeader_User_CreatedBy] FOREIGN KEY ([CreatedById]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_StockTakeHeader_User_CompletedBy] FOREIGN KEY ([CompletedById]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_StockTakeHeader_User_FinalizedBy] FOREIGN KEY ([FinalizedById]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_StockTakeHeader_User_ReopenedBy] FOREIGN KEY ([ReopenedById]) REFERENCES [dbo].[User] ([UserId])
)
GO

-- Stock Take Detail Table - Enhanced with opening/closing quantities and count tracking
CREATE TABLE [dbo].[StockTakeDetail](
    [StockTakeDetailId] [int] IDENTITY(1,1) NOT NULL,
    [StockTakeId] [int] NOT NULL,
    [ProductId] [int] NOT NULL,
    [BatchId] [int] NULL,
    [SystemQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [ActualQuantity] [decimal](18, 4) NULL,
    [VarianceQuantity] [decimal](18, 4) NULL,
    [UnitId] [int] NOT NULL,
    [CostPrice] [decimal](18, 4) NULL,
    [VarianceValue] [decimal](18, 4) NULL,
    [Notes] [nvarchar](max) NULL,
    [OpeningQuantity] [decimal](18, 4) NULL,
    [ClosingQuantity] [decimal](18, 4) NULL,
    [StockTakeTypeId] [int] NULL,
    [LastCountDate] [datetime2] NULL,
    [CountedById] [int] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_StockTakeDetail] PRIMARY KEY CLUSTERED ([StockTakeDetailId] ASC),
    CONSTRAINT [FK_StockTakeDetail_StockTakeHeader] FOREIGN KEY ([StockTakeId]) REFERENCES [dbo].[StockTakeHeader] ([StockTakeId]),
    CONSTRAINT [FK_StockTakeDetail_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_StockTakeDetail_Batch] FOREIGN KEY ([BatchId]) REFERENCES [dbo].[Batch] ([BatchId]),
    CONSTRAINT [FK_StockTakeDetail_Unit] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Unit] ([UnitId]),
    CONSTRAINT [FK_StockTakeDetail_StockTakeType] FOREIGN KEY ([StockTakeTypeId]) REFERENCES [dbo].[StockTakeType] ([StockTakeTypeId]),
    CONSTRAINT [FK_StockTakeDetail_User_CountedBy] FOREIGN KEY ([CountedById]) REFERENCES [dbo].[User] ([UserId])
)
GO

-- PeriodClose Table - For tracking product quantities at period close
CREATE TABLE [dbo].[PeriodClose](
    [PeriodCloseId] [int] IDENTITY(1,1) NOT NULL,
    [PeriodId] [int] NOT NULL,
    [FiscalYearId] [int] NOT NULL,
    [ProductId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [OpeningQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [ClosingQuantity] [decimal](18, 4) NOT NULL DEFAULT 0,
    [OpeningValue] [decimal](18, 4) NOT NULL DEFAULT 0,
    [ClosingValue] [decimal](18, 4) NOT NULL DEFAULT 0,
    [AverageCost] [decimal](18, 4) NULL,
    [CostPrice] [decimal](18, 4) NULL,
    [StockTakeId] [int] NULL,
    [ClosedDate] [datetime2] NOT NULL,
    [ClosedById] [int] NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_PeriodClose] PRIMARY KEY CLUSTERED ([PeriodCloseId] ASC),
    CONSTRAINT [UQ_PeriodClose_Period_Product_CostCenter] UNIQUE ([PeriodId], [ProductId], [CostCenterId]),
    CONSTRAINT [FK_PeriodClose_Period] FOREIGN KEY ([PeriodId]) REFERENCES [dbo].[Period] ([PeriodId]),
    CONSTRAINT [FK_PeriodClose_FiscalYear] FOREIGN KEY ([FiscalYearId]) REFERENCES [dbo].[FiscalYear] ([FiscalYearId]),
    CONSTRAINT [FK_PeriodClose_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_PeriodClose_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId]),
    CONSTRAINT [FK_PeriodClose_StockTakeHeader] FOREIGN KEY ([StockTakeId]) REFERENCES [dbo].[StockTakeHeader] ([StockTakeId]),
    CONSTRAINT [FK_PeriodClose_User_ClosedBy] FOREIGN KEY ([ClosedById]) REFERENCES [dbo].[User] ([UserId])
)
GO

-- =============================================
-- USER MANAGEMENT TABLES
-- =============================================

-- Role Table - Enhanced with default permissions
CREATE TABLE [dbo].[Role](
    [RoleId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [IsAdmin] [bit] NOT NULL DEFAULT 0, -- Admin roles bypass most permission checks
    [IsSystem] [bit] NOT NULL DEFAULT 0, -- System roles cannot be modified by users
    -- Default Cost Center Permissions
    [DefaultCanViewCostCenters] [bit] NOT NULL DEFAULT 1,
    [DefaultCanCreateCostCenters] [bit] NOT NULL DEFAULT 0,
    [DefaultCanEditCostCenters] [bit] NOT NULL DEFAULT 0,
    [DefaultCanDeleteCostCenters] [bit] NOT NULL DEFAULT 0,
    [DefaultCanApproveCostCenters] [bit] NOT NULL DEFAULT 0,
    -- Default Transaction Permissions
    [DefaultCanViewTransactions] [bit] NOT NULL DEFAULT 1,
    [DefaultCanCreateTransactions] [bit] NOT NULL DEFAULT 0,
    [DefaultCanEditTransactions] [bit] NOT NULL DEFAULT 0,
    [DefaultCanDeleteTransactions] [bit] NOT NULL DEFAULT 0,
    [DefaultCanApproveTransactions] [bit] NOT NULL DEFAULT 0,
    [DefaultCanRejectTransactions] [bit] NOT NULL DEFAULT 0,
    [DefaultCanProcessTransactions] [bit] NOT NULL DEFAULT 0,
    [DefaultMaxApprovalAmount] [decimal](18, 4) NULL,
    -- Default Stock Take Permissions
    [DefaultCanViewStockTakes] [bit] NOT NULL DEFAULT 1,
    [DefaultCanCreateStockTakes] [bit] NOT NULL DEFAULT 0,
    [DefaultCanEditStockTakes] [bit] NOT NULL DEFAULT 0,
    [DefaultCanDeleteStockTakes] [bit] NOT NULL DEFAULT 0,
    [DefaultCanCountStockTakes] [bit] NOT NULL DEFAULT 0,
    [DefaultCanFinalizeStockTakes] [bit] NOT NULL DEFAULT 0,
    [DefaultCanReopenStockTakes] [bit] NOT NULL DEFAULT 0,
    -- Default Report Permissions
    [DefaultCanViewReports] [bit] NOT NULL DEFAULT 0,
    [DefaultCanExportReports] [bit] NOT NULL DEFAULT 0,
    [DefaultCanScheduleReports] [bit] NOT NULL DEFAULT 0,
    -- Timestamps and Active Flag
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Role] PRIMARY KEY CLUSTERED ([RoleId] ASC),
    CONSTRAINT [UQ_Role_Name] UNIQUE ([Name])
)
GO

-- Insert default roles
INSERT INTO [dbo].[Role] (
    [Name], [Description], [IsAdmin], [IsSystem],
    [DefaultCanViewCostCenters], [DefaultCanCreateCostCenters], [DefaultCanEditCostCenters],
    [DefaultCanDeleteCostCenters], [DefaultCanApproveCostCenters],
    [DefaultCanViewTransactions], [DefaultCanCreateTransactions], [DefaultCanEditTransactions],
    [DefaultCanDeleteTransactions], [DefaultCanApproveTransactions], [DefaultCanRejectTransactions],
    [DefaultCanProcessTransactions], [DefaultMaxApprovalAmount],
    [DefaultCanViewStockTakes], [DefaultCanCreateStockTakes], [DefaultCanEditStockTakes],
    [DefaultCanDeleteStockTakes], [DefaultCanCountStockTakes], [DefaultCanFinalizeStockTakes],
    [DefaultCanReopenStockTakes],
    [DefaultCanViewReports], [DefaultCanExportReports], [DefaultCanScheduleReports],
    [CreatedAt], [IsActive]
)
VALUES
-- System Administrator
(
    'System Administrator', 'Full system access', 1, 1,
    1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, NULL,
    1, 1, 1, 1, 1, 1, 1,
    1, 1, 1,
    GETDATE(), 1
),
-- Inventory Manager
(
    'Inventory Manager', 'Manages inventory across all cost centers', 0, 0,
    1, 0, 1, 0, 1,
    1, 1, 1, 0, 1, 1, 1, 10000.00,
    1, 1, 1, 0, 1, 1, 0,
    1, 1, 0,
    GETDATE(), 1
),
-- Store Manager
(
    'Store Manager', 'Manages a specific store', 0, 0,
    1, 0, 1, 0, 1,
    1, 1, 1, 0, 1, 1, 1, 5000.00,
    1, 1, 1, 0, 1, 1, 0,
    1, 1, 0,
    GETDATE(), 1
),
-- Purchasing Officer
(
    'Purchasing Officer', 'Creates and manages purchase orders', 0, 0,
    1, 0, 0, 0, 0,
    1, 1, 1, 0, 0, 0, 1, 0.00,
    1, 0, 0, 0, 0, 0, 0,
    1, 1, 0,
    GETDATE(), 1
),
-- Stock Clerk
(
    'Stock Clerk', 'Performs stock counts and basic inventory tasks', 0, 0,
    1, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0.00,
    1, 0, 1, 0, 1, 0, 0,
    1, 0, 0,
    GETDATE(), 1
),
-- Read Only User
(
    'Read Only User', 'View-only access to the system', 0, 0,
    1, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0.00,
    1, 0, 0, 0, 0, 0, 0,
    1, 0, 0,
    GETDATE(), 1
);
GO

-- User Table
CREATE TABLE [dbo].[User](
    [UserId] [int] IDENTITY(1,1) NOT NULL,
    [Username] [nvarchar](50) NOT NULL,
    [Email] [nvarchar](150) NULL,
    [PasswordHash] [nvarchar](255) NOT NULL,
    [FirstName] [nvarchar](100) NULL,
    [LastName] [nvarchar](100) NULL,
    [PhoneNumber] [nvarchar](50) NULL,
    [RoleId] [int] NOT NULL,
    [DefaultLanguage] [nvarchar](10) NULL DEFAULT 'en',
    [LastLoginDate] [datetime2] NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_User] PRIMARY KEY CLUSTERED ([UserId] ASC),
    CONSTRAINT [UQ_User_Username] UNIQUE ([Username]),
    CONSTRAINT [UQ_User_Email] UNIQUE ([Email]),
    CONSTRAINT [FK_User_Role] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role] ([RoleId])
)
GO

-- User Cost Center Access Table
CREATE TABLE [dbo].[UserCostCenterAccess](
    [AccessId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [CostCenterId] [int] NOT NULL,
    [CanView] [bit] NOT NULL DEFAULT 1,
    [CanCreate] [bit] NOT NULL DEFAULT 0,
    [CanEdit] [bit] NOT NULL DEFAULT 0,
    [CanDelete] [bit] NOT NULL DEFAULT 0,
    [CanApprove] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_UserCostCenterAccess] PRIMARY KEY CLUSTERED ([AccessId] ASC),
    CONSTRAINT [UQ_UserCostCenterAccess] UNIQUE ([UserId], [CostCenterId]),
    CONSTRAINT [FK_UserCostCenterAccess_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_UserCostCenterAccess_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId])
)
GO

-- User Transaction Access Table - Controls access to transactions by type and stage
CREATE TABLE [dbo].[UserTransactionAccess](
    [TransactionAccessId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [TransactionTypeId] [int] NULL, -- NULL means all transaction types
    [StageTypeId] [int] NULL, -- NULL means all stages
    [CanView] [bit] NOT NULL DEFAULT 1,
    [CanCreate] [bit] NOT NULL DEFAULT 0,
    [CanEdit] [bit] NOT NULL DEFAULT 0,
    [CanDelete] [bit] NOT NULL DEFAULT 0,
    [CanApprove] [bit] NOT NULL DEFAULT 0,
    [CanReject] [bit] NOT NULL DEFAULT 0,
    [CanProcess] [bit] NOT NULL DEFAULT 0, -- For moving transactions through stages
    [MaxApprovalAmount] [decimal](18, 4) NULL, -- NULL means no limit
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_UserTransactionAccess] PRIMARY KEY CLUSTERED ([TransactionAccessId] ASC),
    CONSTRAINT [UQ_UserTransactionAccess] UNIQUE ([UserId], [TransactionTypeId], [StageTypeId]),
    CONSTRAINT [FK_UserTransactionAccess_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_UserTransactionAccess_TransactionType] FOREIGN KEY ([TransactionTypeId]) REFERENCES [dbo].[TransactionType] ([TransactionTypeId]),
    CONSTRAINT [FK_UserTransactionAccess_StageType] FOREIGN KEY ([StageTypeId]) REFERENCES [dbo].[TransactionStageType] ([StageTypeId])
)
GO

-- User Stock Take Access Table - Controls access to stock taking operations
CREATE TABLE [dbo].[UserStockTakeAccess](
    [StockTakeAccessId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [CostCenterId] [int] NULL, -- NULL means all cost centers
    [CanView] [bit] NOT NULL DEFAULT 1,
    [CanCreate] [bit] NOT NULL DEFAULT 0,
    [CanEdit] [bit] NOT NULL DEFAULT 0,
    [CanDelete] [bit] NOT NULL DEFAULT 0,
    [CanCount] [bit] NOT NULL DEFAULT 0, -- Permission to perform physical counts
    [CanFinalize] [bit] NOT NULL DEFAULT 0, -- Permission to finalize stock takes
    [CanReopen] [bit] NOT NULL DEFAULT 0, -- Permission to reopen finalized stock takes
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_UserStockTakeAccess] PRIMARY KEY CLUSTERED ([StockTakeAccessId] ASC),
    CONSTRAINT [UQ_UserStockTakeAccess] UNIQUE ([UserId], [CostCenterId]),
    CONSTRAINT [FK_UserStockTakeAccess_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_UserStockTakeAccess_CostCenter] FOREIGN KEY ([CostCenterId]) REFERENCES [dbo].[CostCenter] ([CostCenterId])
)
GO

-- User Report Access Table - Controls access to various reports
CREATE TABLE [dbo].[UserReportAccess](
    [ReportAccessId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [ReportCode] [nvarchar](50) NOT NULL, -- Unique code for each report type
    [CanView] [bit] NOT NULL DEFAULT 0,
    [CanExport] [bit] NOT NULL DEFAULT 0,
    [CanSchedule] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_UserReportAccess] PRIMARY KEY CLUSTERED ([ReportAccessId] ASC),
    CONSTRAINT [UQ_UserReportAccess] UNIQUE ([UserId], [ReportCode]),
    CONSTRAINT [FK_UserReportAccess_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId])
)
GO

-- User Product Access Table - Controls access to products
CREATE TABLE [dbo].[UserProductAccess](
    [ProductAccessId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [ProductId] [int] NULL, -- NULL means all products
    [DepartmentId] [int] NULL, -- NULL means all departments
    [GroupId] [int] NULL, -- NULL means all product groups
    [SubGroupId] [int] NULL, -- NULL means all product subgroups
    [CanView] [bit] NOT NULL DEFAULT 1,
    [CanCreate] [bit] NOT NULL DEFAULT 0,
    [CanEdit] [bit] NOT NULL DEFAULT 0,
    [CanDelete] [bit] NOT NULL DEFAULT 0,
    [CanManageRecipes] [bit] NOT NULL DEFAULT 0, -- Permission to manage product recipes
    [CanAdjustPrices] [bit] NOT NULL DEFAULT 0, -- Permission to adjust product prices
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_UserProductAccess] PRIMARY KEY CLUSTERED ([ProductAccessId] ASC),
    CONSTRAINT [FK_UserProductAccess_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_UserProductAccess_Product] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Product] ([ProductId]),
    CONSTRAINT [FK_UserProductAccess_Department] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Department] ([DepartmentId]),
    CONSTRAINT [FK_UserProductAccess_ProductGroup] FOREIGN KEY ([GroupId]) REFERENCES [dbo].[ProductGroup] ([GroupId]),
    CONSTRAINT [FK_UserProductAccess_ProductSubGroup] FOREIGN KEY ([SubGroupId]) REFERENCES [dbo].[ProductSubGroup] ([SubGroupId])
)
GO

-- User Transaction Type Access Table - Controls access to specific transaction types
CREATE TABLE [dbo].[UserTransactionTypeAccess](
    [TransactionTypeAccessId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [TransactionTypeId] [int] NOT NULL,
    [CanView] [bit] NOT NULL DEFAULT 1,
    [CanCreate] [bit] NOT NULL DEFAULT 0,
    [CanEdit] [bit] NOT NULL DEFAULT 0,
    [CanDelete] [bit] NOT NULL DEFAULT 0,
    [CanApprove] [bit] NOT NULL DEFAULT 0,
    [CanReject] [bit] NOT NULL DEFAULT 0,
    [MaxApprovalAmount] [decimal](18, 4) NULL, -- NULL means no limit
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_UserTransactionTypeAccess] PRIMARY KEY CLUSTERED ([TransactionTypeAccessId] ASC),
    CONSTRAINT [UQ_UserTransactionTypeAccess] UNIQUE ([UserId], [TransactionTypeId]),
    CONSTRAINT [FK_UserTransactionTypeAccess_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_UserTransactionTypeAccess_TransactionType] FOREIGN KEY ([TransactionTypeId]) REFERENCES [dbo].[TransactionType] ([TransactionTypeId])
)
GO

-- Application Form Table - Represents forms/screens in the application
CREATE TABLE [dbo].[ApplicationForm](
    [FormId] [int] IDENTITY(1,1) NOT NULL,
    [FormName] [nvarchar](100) NOT NULL,
    [FormDescription] [nvarchar](255) NULL,
    [FormCategory] [nvarchar](50) NULL, -- e.g., 'Inventory', 'Purchasing', 'Configuration'
    [FormPath] [nvarchar](255) NULL, -- Angular route path
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_ApplicationForm] PRIMARY KEY CLUSTERED ([FormId] ASC),
    CONSTRAINT [UQ_ApplicationForm_FormName] UNIQUE ([FormName])
)
GO

-- Form Action Table - Represents actions that can be performed on forms
CREATE TABLE [dbo].[FormAction](
    [ActionId] [int] IDENTITY(1,1) NOT NULL,
    [FormId] [int] NOT NULL,
    [ActionName] [nvarchar](100) NOT NULL, -- e.g., 'Save', 'Submit', 'Delete', 'Approve'
    [ActionDescription] [nvarchar](255) NULL,
    [ActionCode] [nvarchar](50) NOT NULL, -- Unique code for programmatic reference
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_FormAction] PRIMARY KEY CLUSTERED ([ActionId] ASC),
    CONSTRAINT [UQ_FormAction_FormId_ActionName] UNIQUE ([FormId], [ActionName]),
    CONSTRAINT [UQ_FormAction_ActionCode] UNIQUE ([ActionCode]),
    CONSTRAINT [FK_FormAction_ApplicationForm] FOREIGN KEY ([FormId]) REFERENCES [dbo].[ApplicationForm] ([FormId])
)
GO

-- Role Form Action Access Table - Controls role access to specific form actions
CREATE TABLE [dbo].[RoleFormActionAccess](
    [AccessId] [int] IDENTITY(1,1) NOT NULL,
    [RoleId] [int] NOT NULL,
    [ActionId] [int] NOT NULL,
    [HasAccess] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_RoleFormActionAccess] PRIMARY KEY CLUSTERED ([AccessId] ASC),
    CONSTRAINT [UQ_RoleFormActionAccess] UNIQUE ([RoleId], [ActionId]),
    CONSTRAINT [FK_RoleFormActionAccess_Role] FOREIGN KEY ([RoleId]) REFERENCES [dbo].[Role] ([RoleId]),
    CONSTRAINT [FK_RoleFormActionAccess_FormAction] FOREIGN KEY ([ActionId]) REFERENCES [dbo].[FormAction] ([ActionId])
)
GO

-- User Form Action Access Table - Overrides role access for specific users
CREATE TABLE [dbo].[UserFormActionAccess](
    [AccessId] [int] IDENTITY(1,1) NOT NULL,
    [UserId] [int] NOT NULL,
    [ActionId] [int] NOT NULL,
    [HasAccess] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_UserFormActionAccess] PRIMARY KEY CLUSTERED ([AccessId] ASC),
    CONSTRAINT [UQ_UserFormActionAccess] UNIQUE ([UserId], [ActionId]),
    CONSTRAINT [FK_UserFormActionAccess_User] FOREIGN KEY ([UserId]) REFERENCES [dbo].[User] ([UserId]),
    CONSTRAINT [FK_UserFormActionAccess_FormAction] FOREIGN KEY ([ActionId]) REFERENCES [dbo].[FormAction] ([ActionId])
)
GO

-- =============================================
-- PAYMENT AND SALES TABLES
-- =============================================

-- Payment Method Table
CREATE TABLE [dbo].[PaymentMethod](
    [PaymentMethodId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [Description] [nvarchar](255) NULL,
    [AccountNumber] [nvarchar](50) NULL,
    [IsPointsSystem] [bit] NOT NULL DEFAULT 0,
    [PointsConversionRate] [decimal](18, 4) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_PaymentMethod] PRIMARY KEY CLUSTERED ([PaymentMethodId] ASC),
    CONSTRAINT [UQ_PaymentMethod_Name] UNIQUE ([Name])
)
GO

-- Payment Table
CREATE TABLE [dbo].[Payment](
    [PaymentId] [int] IDENTITY(1,1) NOT NULL,
    [TransactionId] [int] NOT NULL,
    [PaymentMethodId] [int] NOT NULL,
    [Amount] [decimal](18, 4) NOT NULL,
    [ChangeAmount] [decimal](18, 4) NULL DEFAULT 0,
    [PointsUsed] [decimal](18, 4) NULL DEFAULT 0,
    [PointsRate] [decimal](18, 4) NULL,
    [Notes] [nvarchar](max) NULL,
    [PaymentDate] [datetime2] NOT NULL DEFAULT GETDATE(),
    [CreatedById] [int] NOT NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Payment] PRIMARY KEY CLUSTERED ([PaymentId] ASC),
    CONSTRAINT [FK_Payment_TransactionHeader] FOREIGN KEY ([TransactionId]) REFERENCES [dbo].[TransactionHeader] ([TransactionId]),
    CONSTRAINT [FK_Payment_PaymentMethod] FOREIGN KEY ([PaymentMethodId]) REFERENCES [dbo].[PaymentMethod] ([PaymentMethodId])
)
GO

-- Currency Table
CREATE TABLE [dbo].[Currency](
    [CurrencyId] [int] IDENTITY(1,1) NOT NULL,
    [Code] [nvarchar](3) NOT NULL,
    [Name] [nvarchar](50) NOT NULL,
    [Symbol] [nvarchar](5) NULL,
    [ExchangeRate] [decimal](18, 6) NOT NULL DEFAULT 1,
    [IsBaseCurrency] [bit] NOT NULL DEFAULT 0,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Currency] PRIMARY KEY CLUSTERED ([CurrencyId] ASC),
    CONSTRAINT [UQ_Currency_Code] UNIQUE ([Code])
)
GO

-- Shift Table
CREATE TABLE [dbo].[Shift](
    [ShiftId] [int] IDENTITY(1,1) NOT NULL,
    [Name] [nvarchar](150) NOT NULL,
    [StartTime] [time] NULL,
    [EndTime] [time] NULL,
    [Description] [nvarchar](255) NULL,
    [CreatedAt] [datetime2] NOT NULL DEFAULT GETDATE(),
    [UpdatedAt] [datetime2] NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    CONSTRAINT [PK_Shift] PRIMARY KEY CLUSTERED ([ShiftId] ASC),
    CONSTRAINT [UQ_Shift_Name] UNIQUE ([Name])
)
GO

-- =============================================
-- NOTE: Initial data is now handled in the migration script
-- =============================================
-- Default data has been moved to migration_script.sql to avoid conflicts
-- during the migration process. The migration script will check for existing
-- data before inserting defaults.
-- =============================================

-- =============================================
-- VIEWS AND STORED PROCEDURES
-- =============================================

-- We'll use a view instead of a separate Stock Ledger table to avoid data duplication
-- This approach maintains a single source of truth while still providing the reporting capabilities

-- Create a view to show stock take details with variance information
CREATE VIEW [dbo].[StockTakeVarianceView] AS
SELECT
    sth.[StockTakeId],
    sth.[StockTakeNumber],
    sth.[CostCenterId],
    cc.[Name] AS [CostCenterName],
    sth.[FiscalYearId],
    fy.[Year] AS [FiscalYear],
    sth.[PeriodId],
    p.[PeriodNumber],
    sth.[StockTakeDate],
    sth.[Status],
    sth.[IsFinalized],
    sth.[FinalizedDate],
    sth.[IsReopened],
    std.[StockTakeDetailId],
    std.[ProductId],
    prod.[Code] AS [ProductCode],
    prod.[Name] AS [ProductName],
    std.[SystemQuantity],
    std.[ActualQuantity],
    std.[VarianceQuantity],
    std.[OpeningQuantity],
    std.[ClosingQuantity],
    std.[CostPrice],
    std.[VarianceValue],
    u.[Name] AS [UnitName],
    stt.[Name] AS [StockTakeTypeName],
    dept.[Name] AS [DepartmentName],
    pg.[Name] AS [ProductGroupName],
    sg.[Name] AS [SubGroupName],
    usr.[Username] AS [CountedByUsername],
    std.[LastCountDate]
FROM
    [dbo].[StockTakeHeader] sth
    INNER JOIN [dbo].[StockTakeDetail] std ON sth.[StockTakeId] = std.[StockTakeId]
    INNER JOIN [dbo].[CostCenter] cc ON sth.[CostCenterId] = cc.[CostCenterId]
    INNER JOIN [dbo].[FiscalYear] fy ON sth.[FiscalYearId] = fy.[FiscalYearId]
    INNER JOIN [dbo].[Product] prod ON std.[ProductId] = prod.[ProductId]
    INNER JOIN [dbo].[Unit] u ON std.[UnitId] = u.[UnitId]
    LEFT JOIN [dbo].[Period] p ON sth.[PeriodId] = p.[PeriodId]
    LEFT JOIN [dbo].[StockTakeType] stt ON std.[StockTakeTypeId] = stt.[StockTakeTypeId]
    LEFT JOIN [dbo].[Department] dept ON prod.[DepartmentId] = dept.[DepartmentId]
    LEFT JOIN [dbo].[ProductGroup] pg ON prod.[GroupId] = pg.[GroupId]
    LEFT JOIN [dbo].[ProductSubGroup] sg ON prod.[SubGroupId] = sg.[SubGroupId]
    LEFT JOIN [dbo].[User] usr ON std.[CountedById] = usr.[UserId]
WHERE
    std.[IsActive] = 1 AND sth.[IsActive] = 1;
GO

-- Create a view for Stock Ledger reporting
CREATE VIEW [dbo].[StockLedgerView] AS
SELECT
    sl.[LedgerId],
    sl.[ProductId],
    p.[Code] AS [ProductCode],
    p.[Name] AS [ProductName],
    sl.[TransactionDate],
    sl.[TransactionType],
    tt.[Name] AS [TransactionTypeName],
    sl.[DocumentNumber],
    sl.[CostCenterId],
    cc.[Name] AS [CostCenterName],
    sl.[SupplierId],
    s.[Name] AS [SupplierName],
    sl.[Quantity],
    sl.[UnitId],
    u.[Name] AS [UnitName],
    sl.[UnitCost],
    sl.[TotalCost],
    sl.[RunningQuantity],
    sl.[AverageCost],
    sl.[TransactionId],
    sl.[StockTakeId],
    sl.[BatchId],
    b.[BatchNumber],
    sl.[Remarks],
    usr.[Username] AS [CreatedByUsername],
    sl.[CreatedAt]
FROM
    [dbo].[StockLedger] sl
    INNER JOIN [dbo].[Product] p ON sl.[ProductId] = p.[ProductId]
    LEFT JOIN [dbo].[TransactionType] tt ON sl.[TransactionTypeId] = tt.[TransactionTypeId]
    LEFT JOIN [dbo].[CostCenter] cc ON sl.[CostCenterId] = cc.[CostCenterId]
    LEFT JOIN [dbo].[Supplier] s ON sl.[SupplierId] = s.[SupplierId]
    LEFT JOIN [dbo].[Unit] u ON sl.[UnitId] = u.[UnitId]
    LEFT JOIN [dbo].[Batch] b ON sl.[BatchId] = b.[BatchId]
    LEFT JOIN [dbo].[User] usr ON sl.[CreatedById] = usr.[UserId]
WHERE
    sl.[IsActive] = 1;
GO

-- Note: We've removed the UserPermissionsView and CheckUserPermission stored procedure
-- Permission checking will be handled in the application layer using Entity Framework Core
-- This approach is more appropriate for a .NET and Angular web application

-- Create a stored procedure to update the Stock Ledger
CREATE PROCEDURE [dbo].[UpdateStockLedger]
    @ProductId INT,
    @TransactionDate DATETIME2,
    @TransactionTypeId INT,
    @TransactionType NVARCHAR(50),
    @DocumentNumber NVARCHAR(50),
    @CostCenterId INT,
    @SupplierId INT = NULL,
    @Quantity DECIMAL(18, 4),
    @UnitId INT,
    @UnitCost DECIMAL(18, 4) = NULL,
    @TransactionId INT = NULL,
    @StockTakeId INT = NULL,
    @BatchId INT = NULL,
    @Remarks NVARCHAR(255) = NULL,
    @CreatedById INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @TotalCost DECIMAL(18, 4);
    DECLARE @RunningQuantity DECIMAL(18, 4);
    DECLARE @AverageCost DECIMAL(18, 4);
    DECLARE @CostCenterSpecificRunningQty DECIMAL(18, 4);

    -- Calculate total cost if unit cost is provided
    IF @UnitCost IS NOT NULL
        SET @TotalCost = @Quantity * @UnitCost;

    -- Get the current running quantity and average cost for this product across all cost centers
    SELECT TOP 1
        @RunningQuantity = [RunningQuantity],
        @AverageCost = [AverageCost]
    FROM
        [dbo].[StockLedger]
    WHERE
        [ProductId] = @ProductId
    ORDER BY
        [LedgerId] DESC;

    -- If no previous records, initialize values
    IF @RunningQuantity IS NULL
    BEGIN
        SET @RunningQuantity = 0;
        SET @AverageCost = @UnitCost;
    END

    -- Get the current cost center specific quantity
    SELECT @CostCenterSpecificRunningQty = [Quantity]
    FROM [dbo].[StockOnHand]
    WHERE [ProductId] = @ProductId AND [CostCenterId] = @CostCenterId;

    -- If no record exists for this cost center, initialize
    IF @CostCenterSpecificRunningQty IS NULL
        SET @CostCenterSpecificRunningQty = 0;

    -- Update running quantities
    SET @RunningQuantity = @RunningQuantity + @Quantity;
    SET @CostCenterSpecificRunningQty = @CostCenterSpecificRunningQty + @Quantity;

    -- Calculate new average cost if this is a positive transaction with a cost
    IF @Quantity > 0 AND @UnitCost IS NOT NULL AND @RunningQuantity > 0
    BEGIN
        -- Calculate weighted average cost
        DECLARE @PreviousTotalValue DECIMAL(18, 4);
        DECLARE @NewTotalValue DECIMAL(18, 4);

        SET @PreviousTotalValue = (@RunningQuantity - @Quantity) * ISNULL(@AverageCost, 0);
        SET @NewTotalValue = @PreviousTotalValue + @TotalCost;

        SET @AverageCost = CASE WHEN @RunningQuantity > 0 THEN @NewTotalValue / @RunningQuantity ELSE @UnitCost END;
    END

    -- Insert the new ledger entry
    INSERT INTO [dbo].[StockLedger]
    (
        [ProductId],
        [TransactionDate],
        [TransactionTypeId],
        [TransactionType],
        [DocumentNumber],
        [CostCenterId],
        [SupplierId],
        [Quantity],
        [UnitId],
        [UnitCost],
        [TotalCost],
        [RunningQuantity],
        [AverageCost],
        [TransactionId],
        [StockTakeId],
        [BatchId],
        [Remarks],
        [CreatedById],
        [CreatedAt],
        [IsActive]
    )
    VALUES
    (
        @ProductId,
        @TransactionDate,
        @TransactionTypeId,
        @TransactionType,
        @DocumentNumber,
        @CostCenterId,
        @SupplierId,
        @Quantity,
        @UnitId,
        @UnitCost,
        @TotalCost,
        @RunningQuantity,
        @AverageCost,
        @TransactionId,
        @StockTakeId,
        @BatchId,
        @Remarks,
        @CreatedById,
        GETDATE(),
        1
    );

    -- Update the StockOnHand table for this specific cost center
    UPDATE [dbo].[StockOnHand]
    SET
        [Quantity] = @CostCenterSpecificRunningQty,
        [AverageCost] = @AverageCost,
        [CostPrice] = @AverageCost,
        [LastUpdated] = GETDATE()
    WHERE
        [ProductId] = @ProductId AND
        [CostCenterId] = @CostCenterId;

    -- If no record exists in StockOnHand for this cost center, create one
    IF @@ROWCOUNT = 0
    BEGIN
        INSERT INTO [dbo].[StockOnHand]
        (
            [ProductId],
            [CostCenterId],
            [Quantity],
            [BaseQuantity],
            [UnitId],
            [AverageCost],
            [CostPrice],
            [LastUpdated]
        )
        VALUES
        (
            @ProductId,
            @CostCenterId,
            @CostCenterSpecificRunningQty,
            @CostCenterSpecificRunningQty, -- Assuming base quantity is same as quantity for now
            @UnitId,
            @AverageCost,
            @AverageCost, -- Setting cost price same as average cost
            GETDATE()
        );
    END

    -- Return the updated running quantity and average cost
    SELECT
        @RunningQuantity AS TotalRunningQuantity,
        @CostCenterSpecificRunningQty AS CostCenterRunningQuantity,
        @AverageCost AS AverageCost;
END
GO

-- Create a stored procedure to generate a new stock take
CREATE PROCEDURE [dbo].[CreateStockTake]
    @CostCenterId INT,
    @FiscalYearId INT,
    @StockTakeDate DATETIME2,
    @Notes NVARCHAR(MAX) = NULL,
    @CreatedById INT,
    @PeriodId INT = NULL,
    @StockTakeTypeId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @StockTakeNumber NVARCHAR(50);
    DECLARE @StockTakeId INT;

    -- Generate a unique stock take number
    SET @StockTakeNumber = 'ST-' + CAST(@CostCenterId AS NVARCHAR(10)) + '-' +
                           CONVERT(NVARCHAR(8), @StockTakeDate, 112) + '-' +
                           CAST(NEXT VALUE FOR [dbo].[StockTakeNumberSequence] AS NVARCHAR(10));

    -- Create the stock take header
    INSERT INTO [dbo].[StockTakeHeader]
    (
        [StockTakeNumber],
        [CostCenterId],
        [FiscalYearId],
        [PeriodId],
        [StockTakeDate],
        [Notes],
        [Status],
        [CreatedById],
        [CreatedAt],
        [IsActive],
        [StockTakeTypeId]
    )
    VALUES
    (
        @StockTakeNumber,
        @CostCenterId,
        @FiscalYearId,
        @PeriodId,
        @StockTakeDate,
        @Notes,
        'Draft',
        @CreatedById,
        GETDATE(),
        1,
        @StockTakeTypeId
    );

    SET @StockTakeId = SCOPE_IDENTITY();

    -- Create stock take details for all products in the cost center
    INSERT INTO [dbo].[StockTakeDetail]
    (
        [StockTakeId],
        [ProductId],
        [BatchId],
        [SystemQuantity],
        [UnitId],
        [CostPrice],
        [CreatedAt],
        [IsActive],
        [OpeningQuantity]
    )
    SELECT
        @StockTakeId,
        soh.[ProductId],
        NULL, -- No batch specified initially
        soh.[Quantity],
        soh.[UnitId],
        soh.[AverageCost],
        GETDATE(),
        1,
        soh.[Quantity]
    FROM
        [dbo].[StockOnHand] soh
    WHERE
        soh.[CostCenterId] = @CostCenterId;

    -- Return the created stock take ID
    SELECT @StockTakeId AS StockTakeId, @StockTakeNumber AS StockTakeNumber;
END
GO

-- Create a stored procedure to finalize a stock take
CREATE PROCEDURE [dbo].[FinalizeStockTake]
    @StockTakeId INT,
    @FinalizedById INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @CostCenterId INT;
    DECLARE @FiscalYearId INT;
    DECLARE @PeriodId INT;
    DECLARE @StockTakeDate DATETIME2;
    DECLARE @StockTakeNumber NVARCHAR(50);

    -- Get stock take information
    SELECT
        @CostCenterId = [CostCenterId],
        @FiscalYearId = [FiscalYearId],
        @PeriodId = [PeriodId],
        @StockTakeDate = [StockTakeDate],
        @StockTakeNumber = [StockTakeNumber]
    FROM
        [dbo].[StockTakeHeader]
    WHERE
        [StockTakeId] = @StockTakeId;

    -- Update stock take header
    UPDATE [dbo].[StockTakeHeader]
    SET
        [Status] = 'Completed',
        [IsFinalized] = 1,
        [FinalizedById] = @FinalizedById,
        [FinalizedDate] = GETDATE(),
        [UpdatedAt] = GETDATE()
    WHERE
        [StockTakeId] = @StockTakeId;

    -- Update stock take details with variance calculations
    UPDATE [dbo].[StockTakeDetail]
    SET
        [VarianceQuantity] = [ActualQuantity] - [SystemQuantity],
        [VarianceValue] = ([ActualQuantity] - [SystemQuantity]) * [CostPrice],
        [ClosingQuantity] = [ActualQuantity],
        [UpdatedAt] = GETDATE()
    WHERE
        [StockTakeId] = @StockTakeId AND
        [ActualQuantity] IS NOT NULL;

    -- Create Stock Ledger entries for each variance
    INSERT INTO [dbo].[StockLedger]
    (
        [ProductId],
        [TransactionDate],
        [TransactionTypeId],
        [TransactionType],
        [DocumentNumber],
        [CostCenterId],
        [Quantity],
        [UnitId],
        [UnitCost],
        [TotalCost],
        [RunningQuantity],
        [AverageCost],
        [StockTakeId],
        [Remarks],
        [CreatedById],
        [CreatedAt],
        [IsActive]
    )
    SELECT
        std.[ProductId],
        @StockTakeDate,
        NULL, -- No specific transaction type for stock take
        'Stock Take Adjustment',
        @StockTakeNumber,
        @CostCenterId,
        std.[VarianceQuantity],
        std.[UnitId],
        std.[CostPrice],
        std.[VarianceValue],
        std.[ActualQuantity], -- This is the new running quantity
        std.[CostPrice], -- Using cost price as average cost
        @StockTakeId,
        'Stock Take Adjustment',
        @FinalizedById,
        GETDATE(),
        1
    FROM
        [dbo].[StockTakeDetail] std
    WHERE
        std.[StockTakeId] = @StockTakeId AND
        std.[ActualQuantity] IS NOT NULL AND
        std.[VarianceQuantity] != 0; -- Only create entries for items with variance

    -- Update stock on hand with actual quantities
    UPDATE soh
    SET
        soh.[Quantity] = std.[ActualQuantity],
        soh.[LastUpdated] = GETDATE()
    FROM
        [dbo].[StockOnHand] soh
        INNER JOIN [dbo].[StockTakeDetail] std ON soh.[ProductId] = std.[ProductId]
    WHERE
        std.[StockTakeId] = @StockTakeId AND
        soh.[CostCenterId] = @CostCenterId AND
        std.[ActualQuantity] IS NOT NULL;

    -- Create period close records if period is specified
    IF @PeriodId IS NOT NULL
    BEGIN
        INSERT INTO [dbo].[PeriodClose]
        (
            [PeriodId],
            [FiscalYearId],
            [ProductId],
            [CostCenterId],
            [OpeningQuantity],
            [ClosingQuantity],
            [OpeningValue],
            [ClosingValue],
            [AverageCost],
            [CostPrice],
            [StockTakeId],
            [ClosedDate],
            [ClosedById],
            [CreatedAt],
            [IsActive]
        )
        SELECT
            @PeriodId,
            @FiscalYearId,
            std.[ProductId],
            @CostCenterId,
            std.[OpeningQuantity],
            std.[ActualQuantity],
            std.[OpeningQuantity] * std.[CostPrice],
            std.[ActualQuantity] * std.[CostPrice],
            std.[CostPrice],
            std.[CostPrice],
            @StockTakeId,
            GETDATE(),
            @FinalizedById,
            GETDATE(),
            1
        FROM
            [dbo].[StockTakeDetail] std
        WHERE
            std.[StockTakeId] = @StockTakeId AND
            std.[ActualQuantity] IS NOT NULL;
    END

    -- Calculate and update total variance in header
    UPDATE [dbo].[StockTakeHeader]
    SET
        [TotalVariance] = (
            SELECT SUM([VarianceValue])
            FROM [dbo].[StockTakeDetail]
            WHERE [StockTakeId] = @StockTakeId
        ),
        [UpdatedAt] = GETDATE()
    WHERE
        [StockTakeId] = @StockTakeId;
END
GO

-- =============================================
-- ENABLE FOREIGN KEY CONSTRAINTS
-- =============================================

EXEC sp_MSforeachtable 'ALTER TABLE ? CHECK CONSTRAINT ALL'
GO

-- =============================================
-- SCHEMA NOTES
-- =============================================

/*
IMPORTANT SCHEMA IMPROVEMENTS:

1. Primary Key Changes:
   - All tables now use ID-based primary keys instead of name-based primary keys
   - Names are still enforced as unique through UNIQUE constraints
   - This improves performance and referential integrity

2. Recipe Support:
   - Enhanced Recipe and RecipeIngredient tables with proper relationships
   - Added IsSubRecipe flag to support sub-recipes
   - Recipes can now be nested to any level

3. Modern Web App Features:
   - Added CreatedAt/UpdatedAt timestamps for all tables
   - Added soft delete functionality with IsActive flag
   - Proper foreign key constraints for referential integrity
   - Consistent naming conventions throughout the schema

4. Authentication & Authorization:
   - Modern user authentication system
   - Role-based authorization with default permissions
   - Form-based permission system matching the original application's granular controls
   - Granular permissions at multiple levels:
     * Cost center access control
     * Transaction access by type and stage
     * Stock take access control
     * Product access control by department/group/subgroup
     * Report access control
     * Form and action-specific permissions
   - Permission inheritance from role defaults
   - Application-layer permission checking for web application
   - Support for both role-level and user-level permission overrides

5. Stocktaking Enhancements:
   - Support for stocktaking at the cost center level with fiscal year association
   - Three stocktaking types: Counted, System, and Zero Count
   - Period tracking with year, period number, and opening/closing quantities
   - Proper variance calculation for both quantity and value
   - Finalization process with proper audit trails
   - Historical data tracking with period close records
   - Comprehensive reporting through dedicated views

6. Additional Improvements:
   - Proper decimal precision for monetary values
   - Batch/lot tracking for expiry date management
   - Multi-currency support
   - Payment processing with multiple payment methods
   - Shift management for sales tracking

This schema is designed to work with .NET Core and Angular web applications,
providing a solid foundation for a modern inventory management system.
*/
GO
