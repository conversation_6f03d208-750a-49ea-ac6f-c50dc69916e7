using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace InventoryAPI.Models
{
    [Table("Shifts")]
    public class Shift
    {
        [Key]
        [Column("ShiftId")]
        public int ShiftId { get; set; }

        [Column("CostCenterId")]
        public int CostCenterId { get; set; }

        [Column("UserId")]
        public int UserId { get; set; }

        [Column("StartTime")]
        public DateTime StartTime { get; set; }

        [Column("EndTime")]
        public DateTime? EndTime { get; set; }

        [Column("OpeningBalance")]
        [Precision(18, 2)]
        public decimal OpeningBalance { get; set; }

        [Column("ClosingBalance")]
        [Precision(18, 2)]
        public decimal? ClosingBalance { get; set; }

        [Column("Status")]
        [StringLength(20)]
        public string Status { get; set; } // Open, Closed, Reconciled

        [Column("Notes")]
        [StringLength(500)]
        public string? Notes { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        [ForeignKey("CostCenterId")]
        public virtual CostCenter CostCenter { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        // Related collections
        public virtual ICollection<Payment> Payments { get; set; }
        public virtual ICollection<TransactionHeader> TransactionHeaders { get; set; }
    }
}
