using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("UserProductAccess")]
    public class UserProductAccess
    {
        [Key]
        [Column("ProductAccessId")]
        public int ProductAccessId { get; set; }

        [Column("UserId")]
        public int UserId { get; set; }

        [Column("ProductId")]
        public int? ProductId { get; set; }

        [Column("DepartmentId")]
        public int? DepartmentId { get; set; }

        [Column("GroupId")]
        public int? GroupId { get; set; }

        [Column("SubGroupId")]
        public int? SubGroupId { get; set; }

        [Column("CanView")]
        public bool CanView { get; set; }

        [Column("CanCreate")]
        public bool CanCreate { get; set; }

        [Column("CanEdit")]
        public bool CanEdit { get; set; }

        [Column("CanDelete")]
        public bool CanDelete { get; set; }

        [Column("CanManageRecipes")]
        public bool CanManageRecipes { get; set; }

        [Column("CanAdjustPrices")]
        public bool CanAdjustPrices { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; }

        [ForeignKey("GroupId")]
        public virtual ProductGroup ProductGroup { get; set; }

        [ForeignKey("SubGroupId")]
        public virtual ProductSubGroup ProductSubGroup { get; set; }
    }
}
