using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("ApplicationForm")]
    public class ApplicationForm
    {
        [Key]
        [Column("FormId")]
        public int FormId { get; set; }

        [Required]
        [StringLength(100)]
        [Column("FormName")]
        public string FormName { get; set; }

        [StringLength(255)]
        [Column("FormDescription")]
        public string FormDescription { get; set; }

        [StringLength(50)]
        [Column("FormCategory")]
        public string FormCategory { get; set; }

        [StringLength(255)]
        [Column("FormPath")]
        public string FormPath { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        public virtual ICollection<FormAction> FormActions { get; set; }
    }
}
