using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("TransactionType")]
    public class TransactionType
    {
        [Key]
        [Column("TransactionTypeId")]
        public int TransactionTypeId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Name")]
        public string Name { get; set; }

        [StringLength(255)]
        [Column("Description")]
        public string Description { get; set; }

        [Column("AffectsInventory")]
        public bool AffectsInventory { get; set; }

        [Column("IsSale")]
        public bool IsSale { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Related collections
        public virtual ICollection<TransactionHeader> TransactionHeaders { get; set; }
        public virtual ICollection<UserTransactionAccess> UserTransactionAccesses { get; set; }
        public virtual ICollection<UserTransactionTypeAccess> UserTransactionTypeAccesses { get; set; }
    }
}
