import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlaceholderComponent } from '../../../shared/components/placeholder/placeholder.component';

@Component({
  selector: 'app-receiving-list',
  standalone: true,
  imports: [CommonModule, PlaceholderComponent],
  template: `
    <app-placeholder 
      title="Receiving Management" 
      message="The receiving management feature is coming soon. This will allow you to manage goods receiving from suppliers."
      icon="bi-truck"
      backLink="/dashboard"
      backLabel="Back to Dashboard">
    </app-placeholder>
  `
})
export class ReceivingListComponent {}
