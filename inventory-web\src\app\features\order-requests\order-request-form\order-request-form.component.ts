import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { OrderRequestDTO, OrderRequestItemDTO } from '../../../core/models/transaction.model';
import { OrderRequestService } from '../../../core/services/order-request.service';
import { CostCenterService } from '../../../core/services/cost-center.service';
import { ProductService } from '../../../core/services/product.service';
import { CostCenter } from '../../../core/models/cost-center.model';
import { Product } from '../../../core/models/product.model';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-order-request-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule, LoadingSpinnerComponent],
  templateUrl: './order-request-form.component.html',
  styleUrls: ['./order-request-form.component.scss']
})
export class OrderRequestFormComponent implements OnInit {
  orderRequestForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;
  orderId?: number;
  
  costCenters: CostCenter[] = [];
  products: Product[] = [];
  
  constructor(
    private fb: FormBuilder,
    private orderRequestService: OrderRequestService,
    private costCenterService: CostCenterService,
    private productService: ProductService,
    private route: ActivatedRoute,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadCostCenters();
    this.loadProducts();
    
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.orderId = +params['id'];
        this.loadOrderRequest(this.orderId);
      }
    });
  }

  initForm(): void {
    this.orderRequestForm = this.fb.group({
      transactionDate: [new Date(), Validators.required],
      sourceCostCenterId: [null],
      destinationCostCenterId: [null, Validators.required],
      notes: [''],
      items: this.fb.array([])
    });
  }

  loadCostCenters(): void {
    this.costCenterService.getCostCenters().subscribe({
      next: (data) => {
        this.costCenters = data;
      },
      error: (error) => {
        console.error('Error loading cost centers:', error);
        this.errorMessage = 'Failed to load cost centers. Please try again later.';
      }
    });
  }

  loadProducts(): void {
    this.productService.getProducts().subscribe({
      next: (data) => {
        this.products = data;
      },
      error: (error) => {
        console.error('Error loading products:', error);
        this.errorMessage = 'Failed to load products. Please try again later.';
      }
    });
  }

  loadOrderRequest(id: number): void {
    this.isLoading = true;
    this.orderRequestService.getOrderRequest(id).subscribe({
      next: (data) => {
        this.patchFormValues(data);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading order request:', error);
        this.errorMessage = 'Failed to load order request. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  patchFormValues(data: OrderRequestDTO): void {
    this.orderRequestForm.patchValue({
      transactionDate: new Date(data.transactionDate),
      sourceCostCenterId: data.sourceCostCenterId,
      destinationCostCenterId: data.destinationCostCenterId,
      notes: data.notes
    });
    
    // Clear existing items
    this.itemsFormArray.clear();
    
    // Add items from data
    if (data.items && data.items.length > 0) {
      data.items.forEach(item => {
        this.addItem(item);
      });
    }
  }

  get itemsFormArray(): FormArray {
    return this.orderRequestForm.get('items') as FormArray;
  }

  addItem(item?: OrderRequestItemDTO): void {
    const itemForm = this.fb.group({
      productId: [item?.productId || null, Validators.required],
      quantity: [item?.quantity || 1, [Validators.required, Validators.min(0.01)]],
      unitId: [item?.unitId || null, Validators.required],
      unitPrice: [item?.unitPrice || 0, [Validators.required, Validators.min(0)]],
      lineTotal: [{ value: item?.lineTotal || 0, disabled: true }]
    });
    
    // Add listeners for quantity and price changes
    itemForm.get('quantity')?.valueChanges.subscribe(() => this.updateLineTotal(itemForm));
    itemForm.get('unitPrice')?.valueChanges.subscribe(() => this.updateLineTotal(itemForm));
    
    // If product is selected, update unit and price
    itemForm.get('productId')?.valueChanges.subscribe(productId => {
      if (productId) {
        const product = this.products.find(p => p.productId === productId);
        if (product) {
          itemForm.patchValue({
            unitId: product.unitId,
            unitPrice: product.costPrice || 0
          });
        }
      }
    });
    
    this.itemsFormArray.push(itemForm);
  }

  removeItem(index: number): void {
    this.itemsFormArray.removeAt(index);
  }

  updateLineTotal(itemForm: FormGroup): void {
    const quantity = itemForm.get('quantity')?.value || 0;
    const unitPrice = itemForm.get('unitPrice')?.value || 0;
    const lineTotal = quantity * unitPrice;
    
    itemForm.get('lineTotal')?.setValue(lineTotal);
  }

  getProductName(productId: number): string {
    const product = this.products.find(p => p.productId === productId);
    return product ? product.name : '';
  }

  getUnitName(unitId: number): string {
    const product = this.products.find(p => p.unitId === unitId);
    return product ? product.unitName || '' : '';
  }

  calculateTotal(): number {
    let total = 0;
    for (const itemForm of this.itemsFormArray.controls) {
      const quantity = itemForm.get('quantity')?.value || 0;
      const unitPrice = itemForm.get('unitPrice')?.value || 0;
      total += quantity * unitPrice;
    }
    return total;
  }

  onSubmit(): void {
    if (this.orderRequestForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      this.orderRequestForm.markAllAsTouched();
      return;
    }
    
    this.isSubmitting = true;
    
    const formValue = this.orderRequestForm.value;
    const orderRequest: OrderRequestDTO = {
      transactionDate: formValue.transactionDate,
      sourceCostCenterId: formValue.sourceCostCenterId,
      destinationCostCenterId: formValue.destinationCostCenterId,
      notes: formValue.notes,
      items: formValue.items.map((item: any) => ({
        productId: item.productId,
        quantity: item.quantity,
        unitId: item.unitId,
        unitPrice: item.unitPrice
      }))
    };
    
    if (this.isEditMode && this.orderId) {
      orderRequest.transactionId = this.orderId;
      this.orderRequestService.updateOrderRequest(this.orderId, orderRequest).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.router.navigate(['/order-requests']);
        },
        error: (error) => {
          console.error('Error updating order request:', error);
          this.errorMessage = 'Failed to update order request. Please try again later.';
          this.isSubmitting = false;
        }
      });
    } else {
      this.orderRequestService.createOrderRequest(orderRequest).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.router.navigate(['/order-requests']);
        },
        error: (error) => {
          console.error('Error creating order request:', error);
          this.errorMessage = 'Failed to create order request. Please try again later.';
          this.isSubmitting = false;
        }
      });
    }
  }
}
