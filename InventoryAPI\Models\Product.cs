using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("Product")]
    public class Product
    {
        [Key]
        [Column("ProductId")]
        public int ProductId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Code")]
        public string Code { get; set; }

        [Required]
        [StringLength(150)]
        [Column("Name")]
        public string Name { get; set; }

        [Column("BrandId")]
        public int? BrandId { get; set; }

        [Column("UnitId")]
        public int? UnitId { get; set; }

        [Column("UnitGroupId")]
        public int? UnitGroupId { get; set; }

        [Column("DepartmentId")]
        public int? DepartmentId { get; set; }

        [Column("GroupId")]
        public int? GroupId { get; set; }

        [Column("SubGroupId")]
        public int? SubGroupId { get; set; }

        [Column("CostPrice")]
        public decimal? CostPrice { get; set; }

        [Column("AverageCost")]
        public decimal? AverageCost { get; set; }

        [Column("SalesPrice")]
        public decimal? SalesPrice { get; set; }

        [Column("MinStock")]
        public decimal? MinStock { get; set; }

        [Column("MaxStock")]
        public decimal? MaxStock { get; set; }

        [Column("ReorderPoint")]
        public decimal? ReorderPoint { get; set; }

        [Column("Notes")]
        public string Notes { get; set; }

        [Column("IsStockItem")]
        public bool IsStockItem { get; set; }

        [Column("IsRecipe")]
        public bool IsRecipe { get; set; }

        [Column("HasExpiry")]
        public bool HasExpiry { get; set; }

        [Column("IsProduction")]
        public bool IsProduction { get; set; }

        [Column("IsSaleable")]
        public bool IsSaleable { get; set; }

        [Column("TaxId")]
        public int? TaxId { get; set; }

        [Column("SalesUnitId")]
        public int? SalesUnitId { get; set; }

        [Column("SalesUnitConversionFactor")]
        public decimal? SalesUnitConversionFactor { get; set; }

        [Column("AllowDiscount")]
        public bool AllowDiscount { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("BrandId")]
        public virtual Brand Brand { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; }

        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; }

        [ForeignKey("GroupId")]
        public virtual ProductGroup ProductGroup { get; set; }

        [ForeignKey("SubGroupId")]
        public virtual ProductSubGroup ProductSubGroup { get; set; }

        [ForeignKey("TaxId")]
        public virtual Tax Tax { get; set; }

        [ForeignKey("SalesUnitId")]
        public virtual Unit SalesUnit { get; set; }

        // Related collections
        public virtual ICollection<Recipe> Recipes { get; set; }
        public virtual ICollection<RecipeIngredient> RecipeIngredients { get; set; }
        public virtual ICollection<StockOnHand> StockOnHands { get; set; }
        public virtual ICollection<ProductCostCenterLink> ProductCostCenterLinks { get; set; }
        public virtual ICollection<Batch> Batches { get; set; }
        public virtual ICollection<TransactionDetail> TransactionDetails { get; set; }
        public virtual ICollection<StockTakeDetail> StockTakeDetails { get; set; }
        public virtual ICollection<PeriodClose> PeriodCloses { get; set; }
        public virtual ICollection<UserProductAccess> UserProductAccesses { get; set; }
    }
}
