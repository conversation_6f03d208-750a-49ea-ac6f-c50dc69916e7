import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CostCenter } from '../models/cost-center.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class CostCenterService {
  private apiUrl = `${environment.apiUrl}/costcenters`;

  constructor(private http: HttpClient) { }

  getCostCenters(): Observable<CostCenter[]> {
    return this.http.get<CostCenter[]>(this.apiUrl);
  }

  getCostCenter(id: number): Observable<CostCenter> {
    return this.http.get<CostCenter>(`${this.apiUrl}/${id}`);
  }

  getCostCentersByStore(storeId: number): Observable<CostCenter[]> {
    return this.http.get<CostCenter[]>(`${this.apiUrl}/bystore/${storeId}`);
  }

  createCostCenter(costCenter: CostCenter): Observable<CostCenter> {
    return this.http.post<CostCenter>(this.apiUrl, costCenter);
  }

  updateCostCenter(id: number, costCenter: CostCenter): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, costCenter);
  }

  deleteCostCenter(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
