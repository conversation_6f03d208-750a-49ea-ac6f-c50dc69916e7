Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "InventoryAPI", "InventoryAPI\InventoryAPI.csproj", "{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EDA3CF81-82B7-7EA6-DB26-EC631E2CAE39}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {54390AA9-4DD3-412C-A6F6-098A24EDDFE7}
	EndGlobalSection
EndGlobal
