using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("Batch")]
    public class Batch
    {
        [Key]
        [Column("BatchId")]
        public int BatchId { get; set; }

        [Required]
        [StringLength(150)]
        [Column("BatchNumber")]
        public string BatchNumber { get; set; }

        [Column("ProductId")]
        public int ProductId { get; set; }

        [Column("CostCenterId")]
        public int CostCenterId { get; set; }

        [Column("ManufactureDate")]
        public DateTime? ManufactureDate { get; set; }

        [Column("ExpiryDate")]
        public DateTime? ExpiryDate { get; set; }

        [Column("InitialQuantity")]
        public decimal InitialQuantity { get; set; }

        [Column("CurrentQuantity")]
        public decimal CurrentQuantity { get; set; }

        [Column("UnitId")]
        public int? UnitId { get; set; }

        [Column("CostPrice")]
        public decimal? CostPrice { get; set; }

        [Column("IsOpen")]
        public bool IsOpen { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("CostCenterId")]
        public virtual CostCenter CostCenter { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; }

        // Related collections
        public virtual ICollection<TransactionDetail> TransactionDetails { get; set; }
        public virtual ICollection<StockTakeDetail> StockTakeDetails { get; set; }
    }
}
