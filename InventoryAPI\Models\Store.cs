using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("Store")]
    public class Store
    {
        [Key]
        [Column("StoreId")]
        public int StoreId { get; set; }

        [Required]
        [StringLength(150)]
        [Column("Name")]
        public string Name { get; set; }

        [Column("LocationId")]
        public int? LocationId { get; set; }

        [Column("IsSalesPoint")]
        public bool IsSalesPoint { get; set; }

        [StringLength(255)]
        [Column("LogoPath")]
        public string LogoPath { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("LocationId")]
        public virtual Location Location { get; set; }

        // Related collections
        public virtual ICollection<CostCenter> CostCenters { get; set; }
    }
}
