.dashboard-container {
  padding: 1rem;
}

.page-header {
  margin-bottom: 2rem;
  
  h1 {
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
}

.dashboard-card {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  
  .card-title {
    color: #6c757d;
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .card-value {
    font-weight: 600;
    font-size: 2rem;
    margin-bottom: 0;
  }
  
  .card-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    i {
      font-size: 1.5rem;
      color: white;
    }
  }
}

.table {
  th {
    font-weight: 600;
    white-space: nowrap;
  }
  
  td {
    vertical-align: middle;
  }
}

.badge {
  padding: 0.5em 0.75em;
  font-weight: 500;
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  padding: 1rem;
  
  h5 {
    font-weight: 600;
    margin: 0;
  }
}

.card-footer {
  background-color: transparent;
  border-top: 1px solid rgba(0, 0, 0, 0.125);
  padding: 1rem;
}
