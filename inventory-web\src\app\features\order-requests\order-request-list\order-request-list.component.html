<div class="container-fluid">
  <div class="page-header d-flex justify-content-between align-items-center">
    <div>
      <h1>Order Requests</h1>
      <p class="text-muted">Manage your order requests</p>
    </div>
    <div>
      <a routerLink="/order-requests/new" class="btn btn-primary">
        <i class="bi bi-plus-circle me-2"></i>New Order Request
      </a>
    </div>
  </div>

  <div class="card">
    <div class="card-body">
      <div *ngIf="isLoading">
        <app-loading-spinner></app-loading-spinner>
      </div>

      <div *ngIf="errorMessage" class="alert alert-danger">
        {{ errorMessage }}
      </div>

      <div *ngIf="!isLoading && !errorMessage">
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Number</th>
                <th>Date</th>
                <th>Source</th>
                <th>Destination</th>
                <th>Status</th>
                <th class="text-end">Total</th>
                <th class="text-center">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngIf="orderRequests.length === 0">
                <td colspan="7" class="text-center py-4">
                  <p class="mb-0">No order requests found</p>
                  <a routerLink="/order-requests/new" class="btn btn-sm btn-primary mt-2">
                    Create your first order request
                  </a>
                </td>
              </tr>
              <tr *ngFor="let orderRequest of orderRequests" [routerLink]="['/order-requests', orderRequest.transactionId]" class="cursor-pointer">
                <td>{{ orderRequest.transactionNumber }}</td>
                <td>{{ orderRequest.transactionDate | date:'MMM d, y' }}</td>
                <td>{{ orderRequest.sourceCostCenterName || 'N/A' }}</td>
                <td>{{ orderRequest.destinationCostCenterName || 'N/A' }}</td>
                <td><span [ngClass]="getStatusClass(orderRequest.status)">{{ orderRequest.status }}</span></td>
                <td class="text-end">{{ orderRequest.totalAmount | currency }}</td>
                <td class="text-center">
                  <div class="btn-group">
                    <a [routerLink]="['/order-requests', orderRequest.transactionId]" class="btn btn-sm btn-outline-primary">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a [routerLink]="['/order-requests', orderRequest.transactionId, 'edit']" class="btn btn-sm btn-outline-secondary">
                      <i class="bi bi-pencil"></i>
                    </a>
                    <button (click)="deleteOrderRequest(orderRequest.transactionId, $event)" class="btn btn-sm btn-outline-danger">
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
