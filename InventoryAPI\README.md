# Inventory Management API

This is a .NET 8 Web API for the Inventory Management System. It provides a RESTful API for managing inventory, products, transactions, and users, designed to replace the desktop C# application with a modern web-based solution.

## Prerequisites

- .NET 8 SDK
- SQL Server (local or remote)
- Visual Studio 2022 or Visual Studio Code

## Getting Started

1. Clone the repository
2. Update the connection string in `appsettings.json` to point to your SQL Server instance
3. Run the following commands to create the database and apply migrations:

```bash
dotnet ef migrations add InitialCreate
dotnet ef database update
```

4. Run the application:

```bash
dotnet run
```

5. Access the Swagger UI at `http://localhost:5000/swagger` to explore the API

## API Endpoints

The API provides the following endpoints:

### Authentication

- `POST /api/Auth/Login` - Authenticate a user and get a JWT token

### Products

- `GET /api/Products` - Get all products
- `GET /api/Products/{id}` - Get a product by ID
- `POST /api/Products` - Create a new product
- `PUT /api/Products/{id}` - Update a product
- `DELETE /api/Products/{id}` - Delete a product

### Departments

- `GET /api/Departments` - Get all departments
- `GET /api/Departments/{id}` - Get a department by ID
- `POST /api/Departments` - Create a new department
- `PUT /api/Departments/{id}` - Update a department
- `DELETE /api/Departments/{id}` - Delete a department

### Order Requests

- `GET /api/OrderRequest` - Get all order requests
- `GET /api/OrderRequest/{id}` - Get an order request by ID
- `POST /api/OrderRequest` - Create a new order request
- `PUT /api/OrderRequest/{id}` - Update an order request
- `DELETE /api/OrderRequest/{id}` - Delete an order request
- `POST /api/OrderRequest/{id}/Submit` - Submit an order request

### Receiving

- `GET /api/Receiving` - Get all receiving transactions
- `GET /api/Receiving/{id}` - Get a receiving transaction by ID
- `POST /api/Receiving` - Create a new receiving transaction
- `PUT /api/Receiving/{id}` - Update a receiving transaction
- `DELETE /api/Receiving/{id}` - Delete a receiving transaction

### Transfers

- `GET /api/Transfers` - Get all transfer transactions
- `GET /api/Transfers/{id}` - Get a transfer transaction by ID
- `POST /api/Transfers` - Create a new transfer transaction
- `PUT /api/Transfers/{id}` - Update a transfer transaction
- `DELETE /api/Transfers/{id}` - Delete a transfer transaction

### Cost Centers

- `GET /api/CostCenters` - Get all cost centers
- `GET /api/CostCenters/{id}` - Get a cost center by ID
- `GET /api/CostCenters/ByStore/{storeId}` - Get cost centers by store
- `POST /api/CostCenters` - Create a new cost center
- `PUT /api/CostCenters/{id}` - Update a cost center
- `DELETE /api/CostCenters/{id}` - Delete a cost center

### Suppliers

- `GET /api/Suppliers` - Get all suppliers
- `GET /api/Suppliers/{id}` - Get a supplier by ID
- `POST /api/Suppliers` - Create a new supplier
- `PUT /api/Suppliers/{id}` - Update a supplier
- `DELETE /api/Suppliers/{id}` - Delete a supplier

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. To access protected endpoints, you need to:

1. Call the `/api/Auth/Login` endpoint with valid credentials
2. Get the JWT token from the response
3. Include the token in the Authorization header of subsequent requests:

```
Authorization: Bearer {token}
```

## Database Schema

The database schema is based on the `improved_schema.sql` file and includes tables for:

- Products, Departments, Groups, and SubGroups
- Inventory management (Stock, Batches, etc.)
- Transactions and Transaction Details
- Users, Roles, and Permissions

## Development

To add more controllers and features:

1. Create entity models in the `Models` folder
2. Add DbSet properties to the `InventoryDbContext` class
3. Create controllers in the `Controllers` folder
4. Implement business logic in services (optional)

## Security

- The API uses JWT for authentication
- Passwords should be hashed before storing in the database (not implemented in this example)
- Role-based authorization is supported through the User and Role entities

## Features Implemented

- **Order Requests**: Create and manage order requests similar to the desktop application
- **Receiving**: Process goods receiving from suppliers
- **Transfers**: Transfer inventory between cost centers
- **Stock Management**: Automatic stock updates when receiving or transferring items
- **JWT Authentication**: Secure API access with JSON Web Tokens
- **Soft Delete**: Records are marked as inactive rather than being physically deleted

## Future Enhancements

- Implement proper password hashing
- Add controllers for remaining entities (Recipes, Stock Takes, etc.)
- Implement business logic in services
- Add validation and error handling
- Implement caching for better performance
- Add unit and integration tests
- Implement real-time notifications for inventory changes
- Add reporting functionality
- Implement batch tracking and expiry date management
