USE [TibaRosee]
GO
/****** Object:  Table [dbo].[ApplicationTypeTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ApplicationTypeTBL](
	[AppID] [int] NULL,
	[AppName] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[AppUpdateTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[AppUpdateTBL](
	[ISDone] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[AutholizeTransaction]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[AutholizeTransaction](
	[Transaction_Id] [int] NULL,
	[Transaction_Name] [nvarchar](100) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[AuthorAdditionTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[AuthorAdditionTBL](
	[Author_Id] [int] IDENTITY(1,1) NOT NULL,
	[Author_Name] [nvarchar](150) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Authorizations]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Authorizations](
	[IdLevel] [int] IDENTITY(1,1) NOT NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Name] [nvarchar](100) NULL,
	[AutolizationLevel] [int] NULL,
	[User_Id] [int] NULL,
	[User_Name] [nvarchar](150) NULL,
	[MaxLevel] [int] NULL,
	[Store_id] [int] NULL,
	[Store_Name] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[BarcodeTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BarcodeTBL](
	[ID] [int] NULL,
	[H] [float] NULL,
	[W] [float] NULL,
	[PrinterName] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[BrandsTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BrandsTbl](
	[Product_BrandId] [int] IDENTITY(1,1) NOT NULL,
	[Product_BrandName] [nvarchar](150) NOT NULL,
 CONSTRAINT [PK_BrandsTbl] PRIMARY KEY CLUSTERED 
(
	[Product_BrandName] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CompanyTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CompanyTbl](
	[Comp_Id] [int] IDENTITY(1,1) NOT NULL,
	[Comp_Name] [nvarchar](150) NOT NULL,
 CONSTRAINT [PK_CompanyTbl_1] PRIMARY KEY CLUSTERED 
(
	[Comp_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CostcenterAnalysis]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CostcenterAnalysis](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Name] [nvarchar](150) NULL,
	[TransactionCount] [int] NULL,
	[Total] [float] NULL,
	[Tax] [float] NULL,
	[NetTotal] [float] NULL,
 CONSTRAINT [PK_CostcenterAnalysis] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CostCenterLinkPOS]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CostCenterLinkPOS](
	[Ser] [int] NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CostCenterTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CostCenterTbl](
	[CostCenter_Id] [int] IDENTITY(1,1) NOT NULL,
	[CostCenter_Name] [nvarchar](150) NOT NULL,
	[Store_id] [int] NULL,
	[Store_Name] [nvarchar](150) NULL,
	[Type_id] [int] NULL,
	[CostCenter_Type] [nvarchar](100) NULL,
	[Auto_Transfer] [bit] NULL,
	[IsSales] [bit] NULL,
	[Abbreviation] [nvarchar](50) NULL,
 CONSTRAINT [PK_CostCenterTbl] PRIMARY KEY CLUSTERED 
(
	[CostCenter_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CostCenterType]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CostCenterType](
	[Type_id] [int] NOT NULL,
	[CostCenter_Type] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Currency_TBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Currency_TBL](
	[Currency_ID] [int] NULL,
	[Currency_Name] [nvarchar](70) NULL,
	[Currency_SMPLE] [nvarchar](5) NULL,
	[IsBase] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CustomerSOHPointsTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CustomerSOHPointsTBL](
	[Suppliers_Id] [int] NULL,
	[Transaction_Code] [bigint] NULL,
	[Transaction_Id] [int] NULL,
	[OpeningPoints] [float] NULL,
	[ClosePoints] [float] NULL,
	[TransactionPoints] [float] NULL,
	[TransactionPointsID] [int] NULL,
	[TransactionDate] [datetime] NULL,
	[IsExpired] [bit] NULL,
	[ExpiredDate] [datetime] NULL,
	[DeductionPoints] [float] NULL,
	[NetPoints] [float] NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[CutElect]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CutElect](
	[Id] [int] NULL,
	[OffOrOn] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DepartmentTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DepartmentTbl](
	[Depart_Id] [int] IDENTITY(1,1) NOT NULL,
	[Depart_Name] [nvarchar](150) NOT NULL,
 CONSTRAINT [PK_DepartmentTbl] PRIMARY KEY CLUSTERED 
(
	[Depart_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Details_Localization]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Details_Localization](
	[Id_Details] [int] IDENTITY(1,1) NOT NULL,
	[Form_Id] [int] NULL,
	[Form_Name] [nvarchar](150) NULL,
	[Lang_Id] [int] NULL,
	[LangName] [nvarchar](150) NULL,
	[Control_Name] [nvarchar](150) NULL,
	[Control_Text] [nvarchar](150) NULL,
	[IsTab] [bit] NULL,
	[IsGroup] [bit] NULL,
	[IsButton] [bit] NULL,
	[IsLabel] [bit] NULL,
	[Id_Master] [int] NULL,
 CONSTRAINT [PK_Details_Localization] PRIMARY KEY CLUSTERED 
(
	[Id_Details] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DetailsCostPermission]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DetailsCostPermission](
	[SerDet] [int] IDENTITY(1,1) NOT NULL,
	[Store_id] [int] NULL,
	[Store_Name] [nvarchar](150) NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[IsBransh] [bit] NULL,
	[IsCostCenter] [bit] NULL,
	[OwnerAct] [bit] NULL,
	[TransTo] [bit] NULL,
	[SerMaster] [int] NULL,
	[GroupSer] [int] NULL,
	[GroupName] [nvarchar](150) NULL,
	[DesplayOnTree] [bit] NULL,
 CONSTRAINT [PK_DetailsCostPermission] PRIMARY KEY CLUSTERED 
(
	[SerDet] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DetailsFormAuth]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DetailsFormAuth](
	[IdSer] [int] IDENTITY(1,1) NOT NULL,
	[Master_Ser] [int] NULL,
	[FormSer] [int] NULL,
	[FormName] [nvarchar](150) NULL,
	[ActionName] [nvarchar](150) NULL,
	[ControlName] [nvarchar](150) NULL,
	[GroupSer] [int] NULL,
	[GroupName] [nvarchar](150) NULL,
	[Active] [bit] NULL,
	[IsFormm] [bit] NULL,
 CONSTRAINT [PK_DetailsFormAuth] PRIMARY KEY CLUSTERED 
(
	[IdSer] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DetailsGlAcc]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DetailsGlAcc](
	[Store_id] [int] NULL,
	[Store_Name] [nvarchar](150) NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[Depart_Id] [int] NULL,
	[Depart_Name] [nvarchar](150) NULL,
	[Group_Id] [int] NULL,
	[Group_Name] [nvarchar](150) NULL,
	[SubGroup_Id] [int] NULL,
	[SubGroup_Name] [nvarchar](150) NULL,
	[GLAcc] [nvarchar](150) NULL,
	[DeffGlAcc] [nvarchar](150) NULL,
	[IsStore] [bit] NULL,
	[IsCostCenter] [bit] NULL,
	[IsDepartment] [bit] NULL,
	[IsGroup] [bit] NULL,
	[IsSubGroup] [bit] NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DiscountPresTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DiscountPresTBL](
	[DiscountPres_Id] [int] IDENTITY(1,1) NOT NULL,
	[DiscountPres_Name] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ExChangeRate_TBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ExChangeRate_TBL](
	[Currency_ID] [int] NULL,
	[Currency_Name] [nvarchar](70) NULL,
	[ExchangeRate] [float] NULL,
	[StartDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[Currency_ID_Base] [int] NULL,
	[Currency_Name_Base] [nvarchar](70) NULL,
	[SerMaster] [int] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ExChangeRateHistory_TBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ExChangeRateHistory_TBL](
	[Currency_ID] [int] NULL,
	[Currency_Name] [nvarchar](70) NULL,
	[ExchangeRate] [float] NULL,
	[StartDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[Currency_ID_Base] [int] NULL,
	[Currency_Name_Base] [nvarchar](70) NULL,
	[SerMaster] [int] NOT NULL,
	[SerDetails] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FormsTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FormsTbl](
	[Form_Id] [int] IDENTITY(1,1) NOT NULL,
	[Form_Name] [nvarchar](150) NULL,
 CONSTRAINT [PK_FormsTbl] PRIMARY KEY CLUSTERED 
(
	[Form_Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[GiftVocherPriceTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[GiftVocherPriceTBL](
	[GiftVocherId] [int] NULL,
	[GiftVocherPrice] [float] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[GroupsTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[GroupsTbl](
	[Group_Id] [int] IDENTITY(1,1) NOT NULL,
	[Group_Name] [nvarchar](150) NOT NULL,
	[Depart_Id] [int] NULL,
	[Depart_Name] [nvarchar](150) NULL,
 CONSTRAINT [PK_GroupsTbl] PRIMARY KEY CLUSTERED 
(
	[Group_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[HowUser]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[HowUser](
	[Id] [int] NULL,
	[Counted] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IFCTax]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IFCTax](
	[TaxId] [int] NULL,
	[Taxes] [nvarchar](50) NULL,
	[TrueFalse] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ImprintAdditionsTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ImprintAdditionsTBL](
	[ImprintID] [int] IDENTITY(1,1) NOT NULL,
	[ImprintName] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ItmCostCenterLink]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ItmCostCenterLink](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[MaxiMum] [float] NULL,
	[MinMum] [float] NULL,
	[ReOrder] [float] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Language]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Language](
	[Lang_Id] [int] IDENTITY(1,1) NOT NULL,
	[LangName] [nvarchar](150) NULL,
	[RtL_Align] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LanguageAdditionTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LanguageAdditionTBL](
	[Language_ID] [int] IDENTITY(1,1) NOT NULL,
	[Language_Name] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LanguageDetails]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LanguageDetails](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[FrmName] [nvarchar](max) NULL,
	[ObjName] [nvarchar](max) NULL,
	[En] [nvarchar](max) NULL,
	[Ar] [nvarchar](max) NULL,
	[Date_Time] [datetime] NULL,
	[FrmText] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LincesSCM]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LincesSCM](
	[Register] [nvarchar](150) NULL,
	[KeyValue] [nvarchar](150) NULL,
	[DLinces] [nvarchar](150) NULL,
	[MLinces] [nvarchar](150) NULL,
	[YLinces] [nvarchar](150) NULL,
	[LincesId] [int] IDENTITY(1,1) NOT NULL,
 CONSTRAINT [PK_LincesSCM] PRIMARY KEY CLUSTERED 
(
	[LincesId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LinkSuppliersItems]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LinkSuppliersItems](
	[SerId] [int] IDENTITY(1,1) NOT NULL,
	[Suppliers_Id] [int] NULL,
	[Suppliers_Name] [nvarchar](150) NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[AvCost] [float] NULL,
	[Cost_Product] [float] NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
 CONSTRAINT [PK_LinkSuppliersItems] PRIMARY KEY CLUSTERED 
(
	[SerId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LocationTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LocationTbl](
	[Location_Id] [int] IDENTITY(1,1) NOT NULL,
	[Location_Name] [nvarchar](150) NOT NULL,
	[Comp_Id] [int] NULL,
	[Comp_Name] [nvarchar](150) NULL,
 CONSTRAINT [PK_LocationTbl] PRIMARY KEY CLUSTERED 
(
	[Location_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Master_Localization]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Master_Localization](
	[Id_Master] [int] IDENTITY(1,1) NOT NULL,
	[Form_Id] [int] NULL,
	[Form_Name] [nvarchar](150) NULL,
	[Lang_Id] [int] NULL,
	[LangName] [nvarchar](150) NULL,
	[Control_Name] [nvarchar](150) NULL,
	[Control_Text] [nvarchar](150) NULL,
	[IsTab] [bit] NULL,
	[IsGroup] [bit] NULL,
	[IsButton] [bit] NULL,
	[IsLabel] [bit] NULL,
 CONSTRAINT [PK_Master_Localization] PRIMARY KEY CLUSTERED 
(
	[Id_Master] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Master_Msg]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Master_Msg](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Msg_Txt] [nvarchar](max) NULL,
	[Msg_Title] [nvarchar](150) NULL,
	[Valid_Id] [nvarchar](50) NULL,
 CONSTRAINT [PK_Master_Msg] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MasterCostPermission]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MasterCostPermission](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Store_id] [int] NULL,
	[Store_Name] [nvarchar](150) NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[IsBransh] [bit] NULL,
	[IsCostCenter] [bit] NULL,
	[OwnerAct] [bit] NULL,
	[TransTo] [bit] NULL,
	[DesplayOnTree] [bit] NULL,
 CONSTRAINT [PK_MasterCostPermission] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MasterFormAuth]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MasterFormAuth](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[FormSer] [int] NULL,
	[FormName] [nvarchar](150) NULL,
	[ActionName] [nvarchar](150) NULL,
	[ControlName] [nvarchar](150) NULL,
	[Act] [bit] NULL,
	[IsFormm] [bit] NULL,
 CONSTRAINT [PK_MasterFormAuth] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MasterGlAcc]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MasterGlAcc](
	[Store_id] [int] NULL,
	[Store_Name] [nvarchar](150) NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[Depart_Id] [int] NULL,
	[Depart_Name] [nvarchar](150) NULL,
	[Group_Id] [int] NULL,
	[Group_Name] [nvarchar](150) NULL,
	[SubGroup_Id] [int] NULL,
	[SubGroup_Name] [nvarchar](150) NULL,
	[GLAcc] [nvarchar](50) NULL,
	[DeffGlAcc] [nvarchar](50) NULL,
	[IsStore] [bit] NULL,
	[IsCostCenter] [bit] NULL,
	[IsDepartment] [bit] NULL,
	[IsGroup] [bit] NULL,
	[IsSubGroup] [bit] NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MasterMain]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MasterMain](
	[StockOnHand] [bit] NULL,
	[StockOnHandSpecific] [bit] NULL,
	[OnOrders] [bit] NULL,
	[ThisOrders] [bit] NULL,
	[Expected] [bit] NULL,
	[LastPrice] [bit] NULL,
	[Consumption] [bit] NULL,
	[DaysStocked] [bit] NULL,
	[DaysExpected] [bit] NULL,
	[AverageCost] [bit] NULL,
	[MinLevel] [bit] NULL,
	[OrderLevel] [bit] NULL,
	[MaxLevel] [bit] NULL,
	[UserGroup_Id] [int] NULL,
	[BackOrder] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MethodOfPaymentTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MethodOfPaymentTBL](
	[MethodOfpayment_ID] [int] NULL,
	[MethodOfpayment_Name] [nvarchar](150) NULL,
	[AccountNo] [nvarchar](50) NULL,
	[IsPoints] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Patches]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Patches](
	[Patch_Ser] [int] IDENTITY(1,1) NOT NULL,
	[Patch_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [real] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [real] NULL,
	[NetQ_Qsetup_CurrentQ] [real] NULL,
	[Prud_Date] [datetime] NULL,
	[Exp_Date] [datetime] NULL,
	[CloseOpen] [bit] NULL,
	[usd] [bit] NULL,
	[Product_Id] [int] NULL,
 CONSTRAINT [PK_Patches] PRIMARY KEY CLUSTERED 
(
	[Patch_Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Patches_EXP]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Patches_EXP](
	[Patch_Ser] [int] NOT NULL,
	[Patch_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [real] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [real] NULL,
	[NetQ_Qsetup_CurrentQ] [real] NULL,
	[Prud_Date] [datetime] NULL,
	[Exp_Date] [datetime] NULL,
	[CloseOpen] [bit] NULL,
	[usd] [bit] NULL,
	[Product_Id] [int] NULL,
	[NoShow] [bit] NULL,
 CONSTRAINT [PK_Patches_EXP] PRIMARY KEY CLUSTERED 
(
	[Patch_Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PaymentTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PaymentTBL](
	[PaymetID] [int] IDENTITY(1,1) NOT NULL,
	[Transaction_Code] [int] NULL,
	[Transaction_Id] [int] NULL,
	[MethodOfpayment_ID] [int] NULL,
	[Amount] [float] NULL,
	[Comment] [nvarchar](max) NULL,
	[ChangeAmount] [float] NULL,
	[PointsRate] [float] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PCUser]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PCUser](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[User_Id] [int] NULL,
	[User_Name] [nvarchar](150) NULL,
	[ComputerName] [nvarchar](100) NULL,
	[IP] [nvarchar](100) NULL,
	[Logine] [bit] NULL,
 CONSTRAINT [PK_PCUser] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Period_Close]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Period_Close](
	[ser] [int] IDENTITY(1,1) NOT NULL,
	[per_no] [int] NULL,
	[per_frm] [datetime] NULL,
	[per_to] [datetime] NULL,
	[Product_Code] [varchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[itm_opn] [float] NULL,
	[itm_cls] [float] NULL,
	[clos_dat] [datetime] NULL,
	[clos_tim] [time](7) NULL,
	[per_ser] [int] NULL,
	[CostCenter_Id] [int] NULL,
	[cls_opn] [bit] NULL,
	[nam] [nvarchar](250) NULL,
	[Product_Id] [int] NULL,
	[PeriodClose_Ser] [int] NULL,
	[AvCost] [float] NULL,
	[Cost_Product] [float] NULL,
	[Year_Num] [int] NULL,
	[StockTack_Master_id] [bigint] NULL,
	[SumAvrage] [decimal](18, 3) NULL,
	[SumCost] [decimal](18, 3) NULL,
	[SumAvrageOpen] [decimal](18, 3) NULL,
	[SumCostOpen] [decimal](18, 3) NULL,
 CONSTRAINT [PK_Period_Close] PRIMARY KEY CLUSTERED 
(
	[ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Period_Store]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Period_Store](
	[ser] [int] IDENTITY(1,1) NOT NULL,
	[Store_id] [int] NULL,
	[Store_Name] [nvarchar](150) NULL,
	[Month_No] [int] NULL,
	[From_Period] [datetime] NULL,
	[To_Priod] [datetime] NULL,
	[Year_Num] [int] NULL,
	[Period_Numm] [int] NULL,
	[prod] [bit] NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[HasHalf] [bit] NULL,
 CONSTRAINT [PK_Period_Store] PRIMARY KEY CLUSTERED 
(
	[ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PindintTransferTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PindintTransferTBL](
	[Pinding_ID] [int] IDENTITY(1,1) NOT NULL,
	[CostCenter_Id_Frm] [int] NULL,
	[CostCenter_Name_Frm] [nvarchar](150) NULL,
	[CostCenter_Id_To] [int] NULL,
	[CostCenter_Name_To] [nvarchar](150) NULL,
	[Transaction_Code] [int] NULL,
	[Reciving_Q] [float] NULL,
	[Cost_Product] [float] NULL,
	[CostTotalLine] [float] NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](150) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [float] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](150) NULL,
	[Current_Unt_Q] [float] NULL,
	[NetQ_Qsetup_CurrentQ] [float] NULL,
	[Product_Id] [int] NULL,
	[Product_Code] [nvarchar](150) NULL,
	[Product_Name] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PormotionSalesTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PormotionSalesTBL](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[CostCenter_Id] [int] NULL,
	[SalesPriceEXP] [float] NULL,
	[DiscountValue] [float] NULL,
	[DiscountPRSNT] [float] NULL,
	[DateFrom] [datetime] NULL,
	[DateTo] [datetime] NULL,
	[SalesPrice] [float] NULL,
	[ActDate] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PormotionSalesTBLHeader]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PormotionSalesTBLHeader](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[CostCenter_Id] [int] NULL,
	[SalesPriceEXP] [float] NULL,
	[DiscountValue] [float] NULL,
	[DiscountPRSNT] [float] NULL,
	[DateFrom] [datetime] NULL,
	[DateTo] [datetime] NULL,
	[SalesPrice] [float] NULL,
	[ActDate] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[POSSalesRevenu]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[POSSalesRevenu](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Unt_Name] [nvarchar](150) NULL,
	[Depart_Id] [int] NULL,
	[Group_Id] [int] NULL,
	[SubGroup_Id] [int] NULL,
	[Cost_Product] [float] NULL,
	[AvCost] [float] NULL,
	[SalesPrice] [float] NULL,
	[NetQ_Qsetup_CurrentQ] [float] NULL,
	[Tax_Amount] [float] NULL,
	[Tax_Name] [nvarchar](50) NULL,
	[TotalCost_Product] [float] NULL,
	[TotalAvCost] [float] NULL,
	[TotalSalesPrice] [float] NULL,
	[NetCost_Product] [float] NULL,
	[NetAvCost] [float] NULL,
	[Transaction_Code] [int] NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Patch] [uniqueidentifier] NULL,
	[TransDate] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[POSSetting]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[POSSetting](
	[Ser] [int] NULL,
	[Company_IdSCM] [int] NULL,
	[Company_NameSCM] [nvarchar](150) NULL,
	[Branch_IdSCM] [int] NULL,
	[Branch_NameSCM] [nvarchar](150) NULL,
	[Store_IdSCM] [int] NULL,
	[Store_NameSCM] [nvarchar](150) NULL,
	[Company_IdPOS] [int] NULL,
	[Company_NamePOS] [nvarchar](150) NULL,
	[Brand_IdPOS] [int] NULL,
	[Brand_NamePOS] [nvarchar](150) NULL,
	[CostCenter_IdPOS] [int] NULL,
	[CostCenter_NamePOS] [nvarchar](150) NULL,
	[IsDelete] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[POSTrans]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[POSTrans](
	[ItemCode] [nvarchar](50) NULL,
	[ItemName] [nvarchar](150) NULL,
	[Quantity] [float] NULL,
	[SalesPrice] [float] NULL,
	[Total] [float] NULL,
	[CostCenter_Id] [int] NULL,
	[Trans_Id] [int] NULL,
	[DateTrans] [datetime] NULL,
	[Patch_Ser] [int] NULL,
	[Patch_Name] [nvarchar](200) NULL,
	[Tax_Amount] [float] NULL,
	[Tax_Name] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ProductAnalysis]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProductAnalysis](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Openening] [float] NULL,
	[Receiveing] [float] NULL,
	[Returning] [float] NULL,
	[TransIn] [float] NULL,
	[TransOut] [float] NULL,
	[ProductionIn] [float] NULL,
	[ProductionOut] [float] NULL,
	[YieldIn] [float] NULL,
	[YieldOut] [float] NULL,
	[Adjustment] [float] NULL,
	[Sales] [float] NULL,
	[Closeing] [float] NULL,
 CONSTRAINT [PK_ProductAnalysis] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ProductsTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProductsTbl](
	[Product_Id] [int] IDENTITY(1,1) NOT NULL,
	[Product_Name] [nvarchar](150) NOT NULL,
	[Product_Code] [nvarchar](50) NOT NULL,
	[Product_BrandId] [int] NULL,
	[Product_BrandName] [nvarchar](150) NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](150) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [int] NULL,
	[Depart_Id] [int] NULL,
	[Group_Id] [int] NULL,
	[SubGroup_Id] [int] NULL,
	[Cost_Product] [float] NULL,
	[AvCost] [float] NULL,
	[Consumption] [real] NULL,
	[SalesPrice] [float] NULL,
	[MinStock] [real] NULL,
	[MaxStock] [real] NULL,
	[ReOrder] [real] NULL,
	[Notes] [nvarchar](max) NULL,
	[IsStock] [bit] NULL,
	[IsRecipe] [bit] NULL,
	[IsExpire] [bit] NULL,
	[IsProduction] [bit] NULL,
	[IsSales] [bit] NULL,
	[Auth] [int] NULL,
	[Tax_Id] [int] NULL,
	[Unt_IdSales] [int] NULL,
	[Unt_NameSales] [nvarchar](150) NULL,
	[Unt_QSales] [int] NULL,
	[IsDescount] [bit] NULL,
	[Language] [int] NULL,
	[Title] [nvarchar](200) NULL,
	[Author] [int] NULL,
	[Format] [nvarchar](200) NULL,
	[Edition] [nvarchar](200) NULL,
	[YearOfPublication] [nvarchar](50) NULL,
	[Publisher] [int] NULL,
	[Supplier] [int] NULL,
	[Imprint] [int] NULL,
	[Category] [nvarchar](200) NULL,
	[Translator] [nvarchar](250) NULL,
	[CreatedDate] [datetime] NULL,
 CONSTRAINT [PK_ProductsTbl_1] PRIMARY KEY CLUSTERED 
(
	[Product_Code] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PRSNTPoints]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PRSNTPoints](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[PRSNT] [float] NULL,
	[MinimumPoints] [float] NULL,
	[RatePoints] [float] NULL,
	[MinimumPointsCalc] [float] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PublisherAdditionsTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PublisherAdditionsTBL](
	[PublisherID] [int] IDENTITY(1,1) NOT NULL,
	[PublisherName] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Recipe_ProductsTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Recipe_ProductsTbl](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Recipe_Product_Id] [int] NULL,
	[Recipe_Product_Name] [nvarchar](150) NULL,
	[Recipe_Product_Code] [nvarchar](50) NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [int] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [int] NULL,
	[Cost_Product] [real] NULL,
	[UsedQuantity] [real] NULL,
	[Recipe_Quantity] [real] NULL,
	[Recipe_Price] [real] NULL,
	[Recipe_Persentage_Loss] [real] NULL,
	[Recipe_Price_Loss] [real] NULL,
	[Recipe_Lost_cost] [real] NULL,
	[NetQ_Qsetup_CurrentQ] [real] NULL,
	[Del] [bit] NULL,
	[Cost_ProductPerUnit] [float] NULL,
	[NetQ_Qsetup_CurrentQBase] [float] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[RecipyPriceTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[RecipyPriceTBL](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[Profit] [float] NULL,
	[Tax_Id] [int] NULL,
	[Product_Id] [int] NULL,
	[SalesPriceRecipe] [float] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Sales_POS]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Sales_POS](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[CostCenterPOS_Id] [int] NULL,
	[CostCenterPOS_Name] [nvarchar](150) NULL,
	[CostCenter_Id_To] [int] NULL,
	[CostCenter_Name_To] [nvarchar](150) NULL,
	[Transaction_Code] [int] NULL,
	[Product_Id] [int] NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Reciving_Q] [float] NULL,
	[Cost_Product] [float] NULL,
	[CostTotalLine] [float] NULL,
	[Patch_Ser] [int] NULL,
	[Patch_Name] [nvarchar](150) NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [float] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [float] NULL,
	[NetQ_Qsetup_CurrentQ] [float] NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Date_Create] [datetime] NULL,
	[Transaction_Submit] [bit] NULL,
	[TransactionDetails_Ser] [int] NULL,
	[Authulized] [bit] NULL,
	[SOH] [float] NULL,
	[Open_Q] [float] NULL,
	[Close_Q] [float] NULL,
	[Transaction_Patch] [uniqueidentifier] NULL,
	[Check_No] [int] NULL,
	[TotalAvg] [float] NULL,
	[Is_Recipy] [bit] NULL,
	[Is_Production] [bit] NULL,
	[CompanyPOS_Id] [int] NULL,
	[CompanyPOS_Name] [nvarchar](150) NULL,
	[OutLetPOS_Id] [int] NULL,
	[OutLetPOS_Name] [nvarchar](150) NULL,
	[MethodOfPayment_Id] [int] NULL,
	[MethodOfPayment_Name] [nvarchar](150) NULL,
	[Sales_Price] [float] NULL,
	[IsExpire] [bit] NULL,
	[ProductionCode] [int] NULL,
	[ChDID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SalesDateTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SalesDateTBL](
	[SalesDateID] [int] IDENTITY(1,1) NOT NULL,
	[SalesDate] [datetime] NULL,
	[OpenSalesDate] [datetime] NULL,
	[UserIdOpen] [int] NULL,
	[CloseSalesDate] [datetime] NULL,
	[UserIdClose] [int] NULL,
	[SalesDateAcc] [datetime] NULL,
	[SalesDateIsClose] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ShiftTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ShiftTBL](
	[ShiftID] [int] NOT NULL,
	[ShiftName] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ShiftUserTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ShiftUserTBL](
	[ShiftID] [int] NULL,
	[DateAcc] [datetime] NULL,
	[SalesDate] [datetime] NULL,
	[User_Id] [int] NULL,
	[IsClose] [bit] NULL,
	[UserIdOpen] [datetime] NULL,
	[CloseSalesDate] [datetime] NULL,
	[ShiftUserId] [int] IDENTITY(1,1) NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ShowAllCostStockTakeTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ShowAllCostStockTakeTBL](
	[AllCostShow] [bit] NULL,
	[AllCost_ID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StockOnHandTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StockOnHandTbl](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[CostCenter_Id] [int] NULL,
	[Quntity] [decimal](24, 4) NULL,
	[QuntityBase] [decimal](24, 4) NULL,
	[Item_Unit] [real] NULL,
	[AvCost] [float] NULL,
	[Cost_Product] [float] NULL,
	[ReturnVariance] [float] NULL,
	[NetCost] [float] NULL,
	[IsDescount] [bit] NULL,
	[TheAvCost] [float] NULL,
	[TheQuantaty] [float] NULL,
	[SalesPrice] [float] NULL,
 CONSTRAINT [PK_StockOnHandTbl] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StockOnHandTbl_POS]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StockOnHandTbl_POS](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Product_Code] [nvarchar](50) NULL,
	[CostCenter_Id] [int] NULL,
	[Quntity] [decimal](24, 4) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StockOnHandUpdateTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StockOnHandUpdateTbl](
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Product_Id] [int] NULL,
	[CostCenter_Id] [int] NULL,
	[Quntity] [float] NULL,
	[User_Id] [int] NULL,
	[TransactionDate] [datetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StockTackDetails]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StockTackDetails](
	[StockTack_DetailsId] [int] IDENTITY(1,1) NOT NULL,
	[StockTack_Master_id] [int] NULL,
	[CostCenter_Id_Frm] [int] NULL,
	[CostCenter_Name_Frm] [nvarchar](150) NULL,
	[Product_Id] [int] NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Order_Q] [float] NULL,
	[Reciving_Q] [float] NULL,
	[Invoice_Q] [float] NULL,
	[Return_Q] [float] NULL,
	[Cost_Product] [float] NULL,
	[CostTotalLine] [float] NULL,
	[Count_System] [float] NULL,
	[Count_Actual] [float] NULL,
	[Count_Defrent] [float] NULL,
	[Patch_Ser] [int] NULL,
	[Patch_Name] [nvarchar](150) NULL,
	[IsExpire] [bit] NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [float] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [float] NULL,
	[NetQ_Qsetup_CurrentQ] [float] NULL,
	[AvCost] [float] NULL,
	[Total_AvCost] [float] NULL,
	[ReOpen] [bit] NULL,
	[Cost_ProductPerUnit] [float] NULL,
	[NetQ_Qsetup_CurrentQBase] [float] NULL,
	[ReturnVariance] [float] NULL,
	[NetCost] [float] NULL,
 CONSTRAINT [PK_StockTackDetails] PRIMARY KEY CLUSTERED 
(
	[StockTack_DetailsId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StockTackMaster]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StockTackMaster](
	[StockTack_Master_id] [int] NULL,
	[StockTack_Submit] [bit] NULL,
	[StockTack_Merge] [bit] NULL,
	[Total_Variance] [float] NULL,
	[Overal_Total] [float] NULL,
	[Line_Total] [float] NULL,
	[StockTack_Date] [datetime] NULL,
	[StockTack_Time] [time](7) NULL,
	[Remarks] [nvarchar](150) NULL,
	[CostCenter_Id] [int] NULL,
	[CostCenter_Name] [nvarchar](150) NULL,
	[StockTack_Save] [bit] NULL,
	[Utholization_Id] [int] NULL,
	[User_Id] [int] NULL,
	[StockTack_BginDate] [datetime] NULL,
	[prod] [int] NULL,
	[ser] [int] NULL,
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[Transaction_Code] [bigint] NULL,
	[StockTack_Finalize] [bit] NULL,
	[Type_id] [int] NULL,
	[CostCenter_Type] [nvarchar](50) NULL,
	[ReOpen] [bit] NULL,
	[Del] [bit] NULL,
 CONSTRAINT [PK_StockTackMaster] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StoresTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StoresTbl](
	[Store_id] [int] IDENTITY(1,1) NOT NULL,
	[Store_Name] [nvarchar](150) NOT NULL,
	[Location_Id] [int] NULL,
	[Location_Name] [nvarchar](150) NULL,
	[issales] [bit] NULL,
	[Logo_Company] [nvarchar](max) NULL,
 CONSTRAINT [PK_StoresTbl] PRIMARY KEY CLUSTERED 
(
	[Store_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubGroup]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubGroup](
	[SubGroup_Id] [int] IDENTITY(1,1) NOT NULL,
	[SubGroup_Name] [nvarchar](150) NOT NULL,
	[Group_Id] [int] NULL,
	[Group_Name] [nvarchar](150) NULL,
 CONSTRAINT [PK_SubGroup] PRIMARY KEY CLUSTERED 
(
	[SubGroup_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SuplierGroupTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SuplierGroupTbl](
	[SuplierGroup_Id] [int] IDENTITY(1,1) NOT NULL,
	[SuplierGroup_Name] [nvarchar](150) NOT NULL,
 CONSTRAINT [PK_SuplierGroupTbl] PRIMARY KEY CLUSTERED 
(
	[SuplierGroup_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SupplierAdditionsTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SupplierAdditionsTBL](
	[SupplierAdditions_ID] [int] IDENTITY(1,1) NOT NULL,
	[SupplierAdditions_Name] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SuppliersTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SuppliersTbl](
	[Suppliers_Id] [int] IDENTITY(1,1) NOT NULL,
	[Suppliers_Name] [nvarchar](150) NOT NULL,
	[Typ_Id] [int] NULL,
	[Typ_Name] [nvarchar](150) NULL,
	[Suppliers_CreditLimit] [float] NULL,
	[Suppliers_Contact] [nvarchar](150) NULL,
	[SuplierGroup_Id] [int] NULL,
	[SuplierGroup_Name] [nvarchar](150) NULL,
	[Phone] [nvarchar](50) NULL,
	[Mobile] [nvarchar](50) NULL,
	[Fax] [nvarchar](50) NULL,
	[Addres] [nvarchar](200) NULL,
	[Email] [nvarchar](250) NULL,
	[WebSite] [nvarchar](150) NULL,
	[Load_Time] [nvarchar](50) NULL,
	[Suppliers_Code] [nvarchar](50) NOT NULL,
	[Notes] [nvarchar](max) NULL,
	[Suppliers_Amount] [real] NULL,
	[Suppliers_Active] [bit] NULL,
	[TheImage] [image] NULL,
	[TaxEffect] [bit] NULL,
	[Ledger_No] [nvarchar](150) NULL,
	[InvoiceTotalLedgerCode] [nvarchar](150) NULL,
	[Tax_Regs_No] [nvarchar](150) NULL,
	[IsCustomer] [bit] NULL,
	[Currency_ID] [int] NULL,
	[PointsBalance] [float] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SupplierTypTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SupplierTypTbl](
	[Typ_Id] [int] IDENTITY(1,1) NOT NULL,
	[Typ_Name] [nvarchar](150) NOT NULL,
 CONSTRAINT [PK_SupplierTypTbl] PRIMARY KEY CLUSTERED 
(
	[Typ_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Taxs]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Taxs](
	[Tax_Id] [int] IDENTITY(1,1) NOT NULL,
	[Tax_Name] [nvarchar](50) NOT NULL,
	[Tax_Persentage] [float] NULL,
 CONSTRAINT [PK_Taxs] PRIMARY KEY CLUSTERED 
(
	[Tax_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[text]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[text](
	[stri] [nvarchar](50) NULL,
	[dt] [datetime] NULL,
	[instock] [bit] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Transaction_Details_HistoryTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transaction_Details_HistoryTbl](
	[Transaction_Code] [int] NULL,
	[Product_Id] [int] NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Order_Q] [float] NULL,
	[Reciving_Q] [float] NULL,
	[Invoice_Q] [float] NULL,
	[Return_Q] [float] NULL,
	[Cost_Product] [float] NULL,
	[CostTotalLine] [float] NULL,
	[CostCenter_Id_Frm] [int] NULL,
	[CostCenter_Name_Frm] [nvarchar](150) NULL,
	[CostCenter_Id_To] [int] NULL,
	[CostCenter_Name_To] [nvarchar](150) NULL,
	[Suppliers_Id_Frm] [int] NULL,
	[Suppliers_Name_Frm] [nvarchar](150) NULL,
	[Suppliers_Id_To] [int] NULL,
	[Suppliers_Name_To] [nvarchar](150) NULL,
	[Supplier_Frm] [bit] NULL,
	[Supplier_To] [bit] NULL,
	[Patch_Ser] [int] NULL,
	[Patch_Name] [nvarchar](150) NULL,
	[IsExpire] [bit] NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [float] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [float] NULL,
	[NetQ_Qsetup_CurrentQ] [float] NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Date_Create] [datetime] NULL,
	[Transaction_Save] [bit] NULL,
	[Transaction_Submit] [bit] NULL,
	[Transaction_Cancel] [bit] NULL,
	[Del] [bit] NULL,
	[Transaction_Patch] [nvarchar](150) NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Authulized] [bit] NULL,
	[Period_Store_Id] [int] NULL,
	[Period_Close_Id] [int] NULL,
	[Inventory] [bit] NULL,
	[SOH] [float] NULL,
	[GardDate] [datetime] NULL,
	[Open_Q] [float] NULL,
	[Close_Q] [float] NULL,
	[AvCost] [float] NULL,
	[Invoice_NoReciving] [nvarchar](70) NULL,
	[IsRecipe] [bit] NULL,
	[StockTakeMaster] [int] NULL,
	[InStock] [bit] NULL,
	[ReOpen] [bit] NULL,
	[Tax_Id] [int] NULL,
	[Tax_Name] [nvarchar](100) NULL,
	[Tax_Persentage] [float] NULL,
	[Tax_Value] [float] NULL,
	[TaxEffect] [bit] NULL,
	[Cost_ProductPerUnit] [float] NULL,
	[NetQ_Qsetup_CurrentQBase] [float] NULL,
	[NetTotal] [float] NULL,
	[ReturnVariance] [float] NULL,
	[Total_Av] [float] NULL,
	[IsDescount] [bit] NULL,
	[Product_Id_PRO] [int] NULL,
	[Auto_Transfer] [bit] NULL,
	[SalesPrice] [float] NULL,
	[TotalSales] [float] NULL,
	[Currency_ID] [int] NULL,
	[Currency_Rate] [float] NULL,
	[IsGift] [bit] NULL,
	[ShiftID] [int] NULL,
 CONSTRAINT [PK_Transaction_Details_HistoryTbl] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Transaction_DetailsTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transaction_DetailsTbl](
	[Transaction_Code] [int] NULL,
	[Product_Id] [int] NULL,
	[Product_Code] [nvarchar](50) NULL,
	[Product_Name] [nvarchar](150) NULL,
	[Order_Q] [float] NULL,
	[Reciving_Q] [float] NULL,
	[Invoice_Q] [float] NULL,
	[Return_Q] [float] NULL,
	[Cost_Product] [float] NULL,
	[CostTotalLine] [float] NULL,
	[CostCenter_Id_Frm] [int] NULL,
	[CostCenter_Name_Frm] [nvarchar](150) NULL,
	[CostCenter_Id_To] [int] NULL,
	[CostCenter_Name_To] [nvarchar](150) NULL,
	[Suppliers_Id_Frm] [int] NULL,
	[Suppliers_Name_Frm] [nvarchar](150) NULL,
	[Suppliers_Id_To] [int] NULL,
	[Suppliers_Name_To] [nvarchar](150) NULL,
	[Supplier_Frm] [bit] NULL,
	[Supplier_To] [bit] NULL,
	[Patch_Ser] [int] NULL,
	[Patch_Name] [nvarchar](150) NULL,
	[IsExpire] [bit] NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](50) NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [float] NULL,
	[Current_Unt_Id] [int] NULL,
	[Current_Unt_Name] [nvarchar](50) NULL,
	[Current_Unt_Q] [float] NULL,
	[NetQ_Qsetup_CurrentQ] [float] NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Date_Create] [datetime] NULL,
	[Transaction_Save] [bit] NULL,
	[Transaction_Submit] [bit] NULL,
	[Transaction_Cancel] [bit] NULL,
	[Del] [bit] NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Authulized] [bit] NULL,
	[Period_Store_Id] [int] NULL,
	[Period_Close_Id] [int] NULL,
	[Inventory] [bit] NULL,
	[SOH] [float] NULL,
	[GardDate] [datetime] NULL,
	[Open_Q] [float] NULL,
	[Close_Q] [float] NULL,
	[AvCost] [float] NULL,
	[Invoice_NoReciving] [nvarchar](70) NULL,
	[IsRecipe] [bit] NULL,
	[StockTakeMaster] [int] NULL,
	[InStock] [bit] NULL,
	[Transaction_Patch] [uniqueidentifier] NULL,
	[ReOpen] [bit] NULL,
	[Tax_Id] [int] NULL,
	[Tax_Name] [nvarchar](100) NULL,
	[Tax_Persentage] [float] NULL,
	[Tax_Value] [float] NULL,
	[TaxEffect] [bit] NULL,
	[Cost_ProductPerUnit] [float] NULL,
	[NetQ_Qsetup_CurrentQBase] [float] NULL,
	[NetTotal] [float] NULL,
	[ReturnVariance] [float] NULL,
	[Total_Av] [float] NULL,
	[IsDescount] [bit] NULL,
	[Accept_Reciving] [bit] NULL,
	[Product_Id_PRO] [int] NULL,
	[Auto_Transfer] [bit] NULL,
	[SalesPrice] [float] NULL,
	[TotalSales] [float] NULL,
	[Currency_ID] [int] NULL,
	[Currency_Rate] [float] NULL,
	[TahmelCost_Persent] [float] NULL,
	[TahmelCost_Tota] [float] NULL,
	[TahmelCost_PerUnit] [float] NULL,
	[TahmelCost] [float] NULL,
	[Discount_Amount] [float] NULL,
	[Discount_Pers] [float] NULL,
	[IsGift] [bit] NULL,
	[ShiftID] [int] NULL,
	[FreeSales] [bit] NULL,
	[HasDisc] [bit] NULL,
 CONSTRAINT [PK_Transaction_DetailsTbl] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Transaction_Head_HistoryTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transaction_Head_HistoryTbl](
	[Transaction_Code] [bigint] NOT NULL,
	[CostCenter_Id] [int] NULL,
	[Suppliers_Id] [int] NULL,
	[CostCenter_Supplier] [nvarchar](150) NULL,
	[Remarks] [nvarchar](150) NULL,
	[Invoice_No] [nvarchar](50) NULL,
	[Amount_Bill] [float] NULL,
	[Tax_Bill] [float] NULL,
	[Total_Amount] [float] NULL,
	[Transaction_Save] [bit] NULL,
	[Transaction_Submit] [bit] NULL,
	[User_Id] [int] NULL,
	[Utholization_Id] [int] NULL,
	[Transaction_PaidAmount] [float] NULL,
	[Transaction_NetAmount] [float] NULL,
	[Authulized] [bit] NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Date] [datetime] NULL,
	[Transaction_Date_Submit] [datetime] NULL,
	[Transaction_Patch] [uniqueidentifier] NULL,
	[Accept_Reciving] [bit] NULL,
	[User_Id_Submit] [bit] NULL,
	[User_IdAccept] [int] NULL,
	[Invoice_NoReciving] [nvarchar](70) NULL,
	[per_no] [int] NULL,
	[Year_Num] [int] NULL,
	[ReOpen] [bit] NULL,
	[AutolizationLevel] [int] NULL,
	[AcceptedLevel] [bit] NULL,
	[DiscardLevel] [bit] NULL,
	[Currency_ID] [int] NULL,
	[Currency_Rate] [float] NULL,
	[ShiftID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Transaction_HeadTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Transaction_HeadTbl](
	[Transaction_Code] [bigint] NOT NULL,
	[CostCenter_Id] [int] NULL,
	[Suppliers_Id] [int] NULL,
	[CostCenter_Supplier] [nvarchar](150) NULL,
	[Remarks] [nvarchar](150) NULL,
	[Invoice_No] [nvarchar](50) NULL,
	[Amount_Bill] [float] NULL,
	[Tax_Bill] [float] NULL,
	[Total_Amount] [float] NULL,
	[Transaction_Save] [bit] NULL,
	[Transaction_Submit] [bit] NULL,
	[User_Id] [int] NULL,
	[Utholization_Id] [int] NULL,
	[Transaction_PaidAmount] [float] NULL,
	[Transaction_NetAmount] [float] NULL,
	[Authulized] [bit] NULL,
	[Ser] [int] IDENTITY(1,1) NOT NULL,
	[Transaction_Id] [int] NULL,
	[Transaction_Date] [datetime] NULL,
	[Transaction_Date_Submit] [datetime] NULL,
	[Transaction_Patch] [uniqueidentifier] ROWGUIDCOL  NULL,
	[Accept_Reciving] [bit] NULL,
	[User_Id_Submit] [bit] NULL,
	[User_IdAccept] [int] NULL,
	[Invoice_NoReciving] [nvarchar](70) NULL,
	[per_no] [int] NULL,
	[Year_Num] [int] NULL,
	[ReOpen] [bit] NULL,
	[AutolizationLevel] [int] NULL,
	[AcceptedLevel] [bit] NULL,
	[DiscardLevel] [bit] NULL,
	[Auto_Transfer] [bit] NULL,
	[User_Id_Discard] [int] NULL,
	[Currency_ID] [int] NULL,
	[Currency_Rate] [float] NULL,
	[TahmelAmount] [float] NULL,
	[Discount_Amount] [float] NULL,
	[Discount_Pers] [float] NULL,
	[ShiftID] [int] NULL,
 CONSTRAINT [PK_Transaction_Head] PRIMARY KEY CLUSTERED 
(
	[Ser] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TransactionPointsTBL]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TransactionPointsTBL](
	[TransactionPointsID] [int] NULL,
	[TransactionPointsName] [nvarchar](50) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[TransactionsTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TransactionsTbl](
	[Transaction_Id] [int] NOT NULL,
	[Transaction_Name] [varchar](50) NOT NULL,
	[Effecting] [bit] NULL,
	[Pos] [bit] NULL,
 CONSTRAINT [PK_TransactionsTbl] PRIMARY KEY CLUSTERED 
(
	[Transaction_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Unit_Base]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Unit_Base](
	[Base_Id] [int] NOT NULL,
	[Base_Name] [nvarchar](100) NULL,
	[Base_Unt_Q] [real] NULL,
 CONSTRAINT [PK_Unit_Base] PRIMARY KEY CLUSTERED 
(
	[Base_Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Unit_Group]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Unit_Group](
	[Unt_GroupId] [int] NOT NULL,
	[Unt_Group_name] [nvarchar](150) NULL,
 CONSTRAINT [PK_Unit_Group] PRIMARY KEY CLUSTERED 
(
	[Unt_GroupId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Unit_Map]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Unit_Map](
	[Unit_Map_Id] [int] IDENTITY(1,1) NOT NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Id] [int] NULL,
	[Unt_Name] [nvarchar](150) NULL,
	[Unt_Q] [real] NULL,
	[Unt_IdParnt] [int] NULL,
	[Unt_NameParnt] [nvarchar](150) NULL,
	[Unt_QParnt] [real] NULL,
	[Base_Id] [int] NULL,
	[Base_Name] [nvarchar](150) NULL,
	[Base_Unt_Q] [real] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UnitesTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UnitesTbl](
	[Unt_Id] [int] NOT NULL,
	[Unt_Name] [nvarchar](150) NOT NULL,
	[Unt_GroupId] [int] NULL,
	[Unt_Q] [int] NULL,
	[IsBase] [bit] NULL,
 CONSTRAINT [PK_Unites] PRIMARY KEY CLUSTERED 
(
	[Unt_Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UserGroupTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UserGroupTbl](
	[UserGroup_Id] [int] IDENTITY(1,1) NOT NULL,
	[UserGroup_Name] [nvarchar](150) NOT NULL,
	[UserGroup_Active] [bit] NULL,
	[Utholization_Id] [int] NULL,
 CONSTRAINT [PK_UserGroupTbl] PRIMARY KEY CLUSTERED 
(
	[UserGroup_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UserLog]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UserLog](
	[LogId] [int] IDENTITY(1,1) NOT NULL,
	[LogName] [nvarchar](150) NULL,
	[ScreenName] [nvarchar](150) NULL,
	[User_Id] [int] NULL,
	[User_Name] [nvarchar](150) NULL,
	[Cost_Suppliers] [nvarchar](150) NULL,
	[TransAction_Name] [nvarchar](150) NULL,
	[CostCenter_SupplierTo] [nvarchar](150) NULL,
	[DocNo] [nvarchar](150) NULL,
	[LogDate] [datetime] NULL,
	[Descr] [nvarchar](150) NULL,
 CONSTRAINT [PK_UserLog] PRIMARY KEY CLUSTERED 
(
	[LogId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UsersTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UsersTbl](
	[User_Id] [int] IDENTITY(1,1) NOT NULL,
	[User_Name] [nvarchar](150) NOT NULL,
	[User_LognName] [nvarchar](150) NULL,
	[UserGroup_Id] [int] NULL,
	[User_Password] [nvarchar](50) NULL,
	[User_Active] [bit] NULL,
	[User_Email] [nvarchar](150) NULL,
	[Utholization_Id] [int] NULL,
	[User_Delete] [bit] NULL,
	[Exp_Count] [int] NULL,
	[ShowExp] [bit] NULL,
	[Lang] [nvarchar](50) NULL,
 CONSTRAINT [PK_UsersTbl] PRIMARY KEY CLUSTERED 
(
	[User_Name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UtholizationTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UtholizationTbl](
	[Utholization_Id] [int] IDENTITY(1,1) NOT NULL,
	[Utholization_Name] [nvarchar](5) NULL,
	[Utholization_Dep] [nvarchar](150) NULL,
 CONSTRAINT [PK_UtholizationTbl] PRIMARY KEY CLUSTERED 
(
	[Utholization_Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Vat14sum]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Vat14sum](
	[Doc_Type] [int] NULL,
	[Description] [nvarchar](150) NULL,
	[Doc_Date] [datetime] NULL,
	[Vendor] [nvarchar](100) NULL,
	[Doc_Number] [nvarchar](100) NULL,
	[InvoiceNo] [nvarchar](100) NULL,
	[Purchase_Amount] [float] NULL,
	[Check_Amount] [float] NULL,
	[Account] [nvarchar](70) NULL,
	[Dist_Type] [int] NULL,
	[Debit] [float] NULL,
	[Credit] [float] NULL,
	[TAX_AMT] [float] NULL,
	[TAX_SCD] [nvarchar](70) NULL,
	[TransNum] [nvarchar](150) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Vat14SumHeader]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Vat14SumHeader](
	[Doc_Type] [int] NULL,
	[Description] [nvarchar](150) NULL,
	[Vendor] [nvarchar](150) NULL,
	[Doc_Date] [datetime] NULL,
	[Doc_Number] [nvarchar](100) NULL,
	[InvoiceNo] [nvarchar](100) NULL,
	[PurchaseAmount] [float] NULL,
	[TAX_AMT] [float] NULL,
	[TAX_SCD] [nvarchar](50) NULL,
	[TransNum] [nvarchar](100) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Year_NumTbl]    Script Date: 13/04/2025 10:52 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Year_NumTbl](
	[YearId] [int] IDENTITY(1,1) NOT NULL,
	[Year_Num] [int] NULL,
 CONSTRAINT [PK_Year_NumTbl] PRIMARY KEY CLUSTERED 
(
	[YearId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[CostCenterTbl] ADD  CONSTRAINT [DF_CostCenterTbl_Auto_Transfer]  DEFAULT ((0)) FOR [Auto_Transfer]
GO
ALTER TABLE [dbo].[CostCenterTbl] ADD  CONSTRAINT [DF_CostCenterTbl_IsSales]  DEFAULT ((0)) FOR [IsSales]
GO
ALTER TABLE [dbo].[Currency_TBL] ADD  CONSTRAINT [DF_Currency_TBL_IsBase]  DEFAULT ((0)) FOR [IsBase]
GO
ALTER TABLE [dbo].[CustomerSOHPointsTBL] ADD  CONSTRAINT [DF_CustomerSOHPointsTBL_IsExpired]  DEFAULT ((0)) FOR [IsExpired]
GO
ALTER TABLE [dbo].[CustomerSOHPointsTBL] ADD  CONSTRAINT [DF_CustomerSOHPointsTBL_DeductionPoints]  DEFAULT ((0)) FOR [DeductionPoints]
GO
ALTER TABLE [dbo].[ItmCostCenterLink] ADD  CONSTRAINT [DF_ItmCostCenterLink_MaxiMum]  DEFAULT ((0)) FOR [MaxiMum]
GO
ALTER TABLE [dbo].[ItmCostCenterLink] ADD  CONSTRAINT [DF_ItmCostCenterLink_MinMum]  DEFAULT ((0)) FOR [MinMum]
GO
ALTER TABLE [dbo].[ItmCostCenterLink] ADD  CONSTRAINT [DF_ItmCostCenterLink_ReOrder]  DEFAULT ((0)) FOR [ReOrder]
GO
ALTER TABLE [dbo].[MasterGlAcc] ADD  CONSTRAINT [DF_MasterGlAcc_IsStore]  DEFAULT ((0)) FOR [IsStore]
GO
ALTER TABLE [dbo].[MasterGlAcc] ADD  CONSTRAINT [DF_MasterGlAcc_IsCostCenter]  DEFAULT ((0)) FOR [IsCostCenter]
GO
ALTER TABLE [dbo].[MasterGlAcc] ADD  CONSTRAINT [DF_MasterGlAcc_IsDepartment]  DEFAULT ((0)) FOR [IsDepartment]
GO
ALTER TABLE [dbo].[MasterGlAcc] ADD  CONSTRAINT [DF_MasterGlAcc_IsGroup]  DEFAULT ((0)) FOR [IsGroup]
GO
ALTER TABLE [dbo].[MasterGlAcc] ADD  CONSTRAINT [DF_MasterGlAcc_IsSubGroup]  DEFAULT ((0)) FOR [IsSubGroup]
GO
ALTER TABLE [dbo].[MasterMain] ADD  CONSTRAINT [DF_MasterMain_BackOrder]  DEFAULT ((0)) FOR [BackOrder]
GO
ALTER TABLE [dbo].[MethodOfPaymentTBL] ADD  CONSTRAINT [DF_MethodOfPaymentTBL_IsPoints]  DEFAULT ((0)) FOR [IsPoints]
GO
ALTER TABLE [dbo].[Patches_EXP] ADD  CONSTRAINT [DF_Patches_EXP_NoShow]  DEFAULT ((0)) FOR [NoShow]
GO
ALTER TABLE [dbo].[PaymentTBL] ADD  CONSTRAINT [DF_PaymentTBL_ChangeAmount]  DEFAULT ((0)) FOR [ChangeAmount]
GO
ALTER TABLE [dbo].[PaymentTBL] ADD  CONSTRAINT [DF_PaymentTBL_PointsRate]  DEFAULT ((0)) FOR [PointsRate]
GO
ALTER TABLE [dbo].[POSSetting] ADD  CONSTRAINT [DF_POSSetting_IsDelete]  DEFAULT ((0)) FOR [IsDelete]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Product_BrandId]  DEFAULT ((1)) FOR [Product_BrandId]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_IsStock]  DEFAULT ((1)) FOR [IsStock]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Tax_Id]  DEFAULT ((1)) FOR [Tax_Id]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_IsDescount]  DEFAULT ((0)) FOR [IsDescount]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Language]  DEFAULT ((0)) FOR [Language]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Title]  DEFAULT ('') FOR [Title]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Author]  DEFAULT ((0)) FOR [Author]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Edition]  DEFAULT ('') FOR [Edition]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_YearOfPublication]  DEFAULT ((0)) FOR [YearOfPublication]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Publisher]  DEFAULT ((0)) FOR [Publisher]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Supplier]  DEFAULT ((0)) FOR [Supplier]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Imprint]  DEFAULT ((0)) FOR [Imprint]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_Category]  DEFAULT ('') FOR [Category]
GO
ALTER TABLE [dbo].[ProductsTbl] ADD  CONSTRAINT [DF_ProductsTbl_CreatedDate]  DEFAULT (getdate()) FOR [CreatedDate]
GO
ALTER TABLE [dbo].[PRSNTPoints] ADD  CONSTRAINT [DF_PRSNTPoints_MinimumPoints]  DEFAULT ((0)) FOR [MinimumPoints]
GO
ALTER TABLE [dbo].[PRSNTPoints] ADD  CONSTRAINT [DF_PRSNTPoints_RatePoints]  DEFAULT ((0)) FOR [RatePoints]
GO
ALTER TABLE [dbo].[PRSNTPoints] ADD  CONSTRAINT [DF_PRSNTPoints_MinimumPointsCalc]  DEFAULT ((0)) FOR [MinimumPointsCalc]
GO
ALTER TABLE [dbo].[Sales_POS] ADD  CONSTRAINT [DF_Sales_POS_ChDID]  DEFAULT ((0)) FOR [ChDID]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_Quntity]  DEFAULT ((0)) FOR [Quntity]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_QuntityBase]  DEFAULT ((0)) FOR [QuntityBase]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_Item_Unit]  DEFAULT ((0)) FOR [Item_Unit]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_ReturnVariance]  DEFAULT ((0)) FOR [ReturnVariance]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_NetCost]  DEFAULT ((0)) FOR [NetCost]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_IsDescount]  DEFAULT ((0)) FOR [IsDescount]
GO
ALTER TABLE [dbo].[StockOnHandTbl] ADD  CONSTRAINT [DF_StockOnHandTbl_SalesPrice]  DEFAULT ((0)) FOR [SalesPrice]
GO
ALTER TABLE [dbo].[StockTackDetails] ADD  CONSTRAINT [DF_StockTackDetails_ReOpen]  DEFAULT ((0)) FOR [ReOpen]
GO
ALTER TABLE [dbo].[StockTackDetails] ADD  CONSTRAINT [DF_StockTackDetails_ReturnVariance]  DEFAULT ((0)) FOR [ReturnVariance]
GO
ALTER TABLE [dbo].[StockTackDetails] ADD  CONSTRAINT [DF_StockTackDetails_NetCost]  DEFAULT ((0)) FOR [NetCost]
GO
ALTER TABLE [dbo].[StockTackMaster] ADD  CONSTRAINT [DF_StockTackMaster_ReOpen]  DEFAULT ((0)) FOR [ReOpen]
GO
ALTER TABLE [dbo].[StockTackMaster] ADD  CONSTRAINT [DF_StockTackMaster_Del]  DEFAULT ((0)) FOR [Del]
GO
ALTER TABLE [dbo].[SuppliersTbl] ADD  CONSTRAINT [DF_SuppliersTbl_IsCustomer]  DEFAULT ((0)) FOR [IsCustomer]
GO
ALTER TABLE [dbo].[SuppliersTbl] ADD  CONSTRAINT [DF_SuppliersTbl_Currency_ID]  DEFAULT ((0)) FOR [Currency_ID]
GO
ALTER TABLE [dbo].[SuppliersTbl] ADD  CONSTRAINT [DF_SuppliersTbl_PointsBalance]  DEFAULT ((0)) FOR [PointsBalance]
GO
ALTER TABLE [dbo].[text] ADD  CONSTRAINT [DF_text_instock]  DEFAULT ((1)) FOR [instock]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_InStock]  DEFAULT ((1)) FOR [InStock]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_ReOpen]  DEFAULT ((0)) FOR [ReOpen]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_ReturnVariance]  DEFAULT ((0)) FOR [ReturnVariance]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_Total_Av]  DEFAULT ((0)) FOR [Total_Av]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_IsDescount]  DEFAULT ((0)) FOR [IsDescount]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_Auto_Transfer]  DEFAULT ((1)) FOR [Auto_Transfer]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_SalesPrice]  DEFAULT ((0)) FOR [SalesPrice]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_TotalSales]  DEFAULT ((0)) FOR [TotalSales]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_Currency_ID]  DEFAULT ((1)) FOR [Currency_ID]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_Currency_Rate]  DEFAULT ((1)) FOR [Currency_Rate]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_IsGift]  DEFAULT ((0)) FOR [IsGift]
GO
ALTER TABLE [dbo].[Transaction_Details_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Details_HistoryTbl_ShiftID]  DEFAULT ((0)) FOR [ShiftID]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_InStock]  DEFAULT ((1)) FOR [InStock]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_ReOpen]  DEFAULT ((0)) FOR [ReOpen]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Tax_Id]  DEFAULT ((1)) FOR [Tax_Id]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Tax_Name]  DEFAULT (N'0 % Tax') FOR [Tax_Name]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Tax_Persentage]  DEFAULT ((0)) FOR [Tax_Persentage]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Tax_Value]  DEFAULT ((0)) FOR [Tax_Value]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TaxEffect]  DEFAULT ((0)) FOR [TaxEffect]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_ReturnVariance]  DEFAULT ((0)) FOR [ReturnVariance]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Total_Av]  DEFAULT ((0)) FOR [Total_Av]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_IsDescount]  DEFAULT ((0)) FOR [IsDescount]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Accept_Reciving]  DEFAULT ((0)) FOR [Accept_Reciving]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Auto_Transfer]  DEFAULT ((1)) FOR [Auto_Transfer]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_SalesPrice]  DEFAULT ((0)) FOR [SalesPrice]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TotalSales]  DEFAULT ((0)) FOR [TotalSales]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Currency_ID]  DEFAULT ((1)) FOR [Currency_ID]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Currency_Rate]  DEFAULT ((1)) FOR [Currency_Rate]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TahmelCost_Persent]  DEFAULT ((0)) FOR [TahmelCost_Persent]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TahmelCost_Tota]  DEFAULT ((0)) FOR [TahmelCost_Tota]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TahmelCost_PerUnit]  DEFAULT ((0)) FOR [TahmelCost_PerUnit]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_TahmelCost]  DEFAULT ((0)) FOR [TahmelCost]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Discount_Amount]  DEFAULT ((0)) FOR [Discount_Amount]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_Discount_Pers]  DEFAULT ((0)) FOR [Discount_Pers]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_IsGift]  DEFAULT ((0)) FOR [IsGift]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl]  DEFAULT ((0)) FOR [ShiftID]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_FreeSales]  DEFAULT ((0)) FOR [FreeSales]
GO
ALTER TABLE [dbo].[Transaction_DetailsTbl] ADD  CONSTRAINT [DF_Transaction_DetailsTbl_HasDisc]  DEFAULT ((0)) FOR [HasDisc]
GO
ALTER TABLE [dbo].[Transaction_Head_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Head_HistoryTbl_Transaction_Patch_1]  DEFAULT (newid()) FOR [Transaction_Patch]
GO
ALTER TABLE [dbo].[Transaction_Head_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Head_HistoryTbl_ReOpen]  DEFAULT ((0)) FOR [ReOpen]
GO
ALTER TABLE [dbo].[Transaction_Head_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Head_HistoryTbl_AutolizationLevel]  DEFAULT ((0)) FOR [AutolizationLevel]
GO
ALTER TABLE [dbo].[Transaction_Head_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Head_HistoryTbl_AcceptedLevel]  DEFAULT ((0)) FOR [AcceptedLevel]
GO
ALTER TABLE [dbo].[Transaction_Head_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Head_HistoryTbl_DiscardLevel]  DEFAULT ((0)) FOR [DiscardLevel]
GO
ALTER TABLE [dbo].[Transaction_Head_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Head_HistoryTbl_Currency_ID]  DEFAULT ((1)) FOR [Currency_ID]
GO
ALTER TABLE [dbo].[Transaction_Head_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Head_HistoryTbl_Currency_Rate]  DEFAULT ((1)) FOR [Currency_Rate]
GO
ALTER TABLE [dbo].[Transaction_Head_HistoryTbl] ADD  CONSTRAINT [DF_Transaction_Head_HistoryTbl_ShiftID]  DEFAULT ((0)) FOR [ShiftID]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_Head_Transaction_Patch]  DEFAULT (newid()) FOR [Transaction_Patch]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_ReOpen]  DEFAULT ((0)) FOR [ReOpen]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_AutolizationLevel]  DEFAULT ((0)) FOR [AutolizationLevel]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_AcceptedLevel]  DEFAULT ((0)) FOR [AcceptedLevel]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_DiscardLevel]  DEFAULT ((0)) FOR [DiscardLevel]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Auto_Transfer]  DEFAULT ((1)) FOR [Auto_Transfer]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_User_Id_Discard]  DEFAULT ((0)) FOR [User_Id_Discard]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Currency_ID]  DEFAULT ((1)) FOR [Currency_ID]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Currency_Rate]  DEFAULT ((1)) FOR [Currency_Rate]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_TahmelAmount]  DEFAULT ((0)) FOR [TahmelAmount]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Discount_Amount]  DEFAULT ((0)) FOR [Discount_Amount]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_Discount_Pers]  DEFAULT ((0)) FOR [Discount_Pers]
GO
ALTER TABLE [dbo].[Transaction_HeadTbl] ADD  CONSTRAINT [DF_Transaction_HeadTbl_ShiftID]  DEFAULT ((0)) FOR [ShiftID]
GO
ALTER TABLE [dbo].[UnitesTbl] ADD  CONSTRAINT [DF_UnitesTbl_IsBase]  DEFAULT ((0)) FOR [IsBase]
GO
ALTER TABLE [dbo].[UsersTbl] ADD  CONSTRAINT [DF_UsersTbl_Exp_Count]  DEFAULT ((0)) FOR [Exp_Count]
GO
