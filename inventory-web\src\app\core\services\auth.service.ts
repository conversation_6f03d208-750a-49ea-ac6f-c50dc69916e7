import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { JwtHelperService } from '@auth0/angular-jwt';
import { LoginRequest, LoginResponse, User } from '../models/user.model';
import { environment } from '../../../environments/environment';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = `${environment.apiUrl}/auth`;
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private jwtHelper = new JwtHelperService();
  private isBrowser: boolean;

  constructor(
    private http: HttpClient,
    @Inject(PLATFORM_ID) platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(platformId);
    if (this.isBrowser) {
      this.loadUserFromStorage();
    }
  }

  // Safe localStorage methods
  private getLocalStorage(key: string): string | null {
    return this.isBrowser ? localStorage.getItem(key) : null;
  }

  private setLocalStorage(key: string, value: string): void {
    if (this.isBrowser) {
      localStorage.setItem(key, value);
    }
  }

  private removeLocalStorage(key: string): void {
    if (this.isBrowser) {
      localStorage.removeItem(key);
    }
  }

  login(loginRequest: LoginRequest): Observable<LoginResponse> {
    console.log('Login request:', loginRequest);
    console.log('API URL:', `${this.apiUrl}/login`);

    return this.http.post<LoginResponse>(`${this.apiUrl}/login`, loginRequest, {
      headers: { 'Content-Type': 'application/json' }
    })
      .pipe(
        tap(response => {
          console.log('Login response:', response);
          if (response && response.token) {
            this.setLocalStorage('token', response.token);
            const user: User = {
              userId: response.userId,
              username: response.username,
              email: '',
              firstName: response.fullName.split(' ')[0] || '',
              lastName: response.fullName.split(' ').slice(1).join(' ') || '',
              phoneNumber: '',
              roleId: 0,
              roleName: response.role,
              defaultLanguage: 'en',
              lastLoginDate: new Date(),
              isActive: true
            };
            this.currentUserSubject.next(user);
            this.setLocalStorage('user', JSON.stringify(user));
          } else {
            console.error('Invalid response format:', response);
            throw new Error('Invalid response format');
          }
        }),
        catchError(error => {
          console.error('Login error:', error);
          return throwError(() => new Error('Invalid username or password'));
        })
      );
  }

  logout(): void {
    this.removeLocalStorage('token');
    this.removeLocalStorage('user');
    this.currentUserSubject.next(null);
  }

  isLoggedIn(): boolean {
    const token = this.getLocalStorage('token');
    return !!token && !this.jwtHelper.isTokenExpired(token);
  }

  getToken(): string | null {
    return this.getLocalStorage('token');
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  private loadUserFromStorage(): void {
    const userJson = this.getLocalStorage('user');
    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        this.currentUserSubject.next(user);
      } catch (e) {
        console.error('Error parsing user from localStorage', e);
        this.logout();
      }
    }
  }
}
