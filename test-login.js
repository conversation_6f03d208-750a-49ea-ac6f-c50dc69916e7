const fetch = require('node-fetch');

async function testLogin() {
  try {
    const response = await fetch('http://localhost:5201/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin'
      })
    });

    const status = response.status;
    const statusText = response.statusText;
    console.log(`Status: ${status} ${statusText}`);

    const text = await response.text();
    console.log('Raw response:', text);

    try {
      const json = JSON.parse(text);
      console.log('Parsed JSON:', json);
    } catch (e) {
      console.error('Error parsing JSON:', e.message);
    }
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testLogin();
