<div class="container-fluid">
  <div class="page-header d-flex justify-content-between align-items-center">
    <div>
      <h1>{{ isEditMode ? 'Edit' : 'New' }} Order Request</h1>
      <p class="text-muted">{{ isEditMode ? 'Update existing' : 'Create a new' }} order request</p>
    </div>
    <div>
      <a routerLink="/order-requests" class="btn btn-outline-secondary me-2">
        <i class="bi bi-arrow-left me-2"></i>Back to List
      </a>
      <button type="button" class="btn btn-primary" (click)="onSubmit()" [disabled]="isSubmitting">
        <i class="bi" [ngClass]="isEditMode ? 'bi-save' : 'bi-plus-circle'"></i>
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </div>
  </div>

  <div *ngIf="isLoading">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div *ngIf="errorMessage" class="alert alert-danger">
    {{ errorMessage }}
  </div>

  <div *ngIf="!isLoading">
    <form [formGroup]="orderRequestForm" (ngSubmit)="onSubmit()">
      <div class="row">
        <div class="col-md-8">
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">Order Request Details</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="transactionDate" class="form-label">Date</label>
                  <input 
                    type="date" 
                    class="form-control" 
                    id="transactionDate" 
                    formControlName="transactionDate"
                    [ngClass]="{'is-invalid': orderRequestForm.get('transactionDate')?.invalid && orderRequestForm.get('transactionDate')?.touched}"
                  >
                  <div *ngIf="orderRequestForm.get('transactionDate')?.invalid && orderRequestForm.get('transactionDate')?.touched" class="invalid-feedback">
                    Date is required
                  </div>
                </div>
                
                <div class="col-md-6 mb-3">
                  <label for="sourceCostCenterId" class="form-label">Source Cost Center</label>
                  <select 
                    class="form-select" 
                    id="sourceCostCenterId" 
                    formControlName="sourceCostCenterId"
                  >
                    <option value="">-- Select Source --</option>
                    <option *ngFor="let costCenter of costCenters" [value]="costCenter.costCenterId">
                      {{ costCenter.name }}
                    </option>
                  </select>
                </div>
                
                <div class="col-md-6 mb-3">
                  <label for="destinationCostCenterId" class="form-label">Destination Cost Center</label>
                  <select 
                    class="form-select" 
                    id="destinationCostCenterId" 
                    formControlName="destinationCostCenterId"
                    [ngClass]="{'is-invalid': orderRequestForm.get('destinationCostCenterId')?.invalid && orderRequestForm.get('destinationCostCenterId')?.touched}"
                  >
                    <option value="">-- Select Destination --</option>
                    <option *ngFor="let costCenter of costCenters" [value]="costCenter.costCenterId">
                      {{ costCenter.name }}
                    </option>
                  </select>
                  <div *ngIf="orderRequestForm.get('destinationCostCenterId')?.invalid && orderRequestForm.get('destinationCostCenterId')?.touched" class="invalid-feedback">
                    Destination is required
                  </div>
                </div>
                
                <div class="col-md-12 mb-3">
                  <label for="notes" class="form-label">Notes</label>
                  <textarea 
                    class="form-control" 
                    id="notes" 
                    formControlName="notes"
                    rows="3"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
          
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">Items</h5>
              <button type="button" class="btn btn-sm btn-primary" (click)="addItem()">
                <i class="bi bi-plus-circle me-1"></i>Add Item
              </button>
            </div>
            <div class="card-body p-0">
              <div class="table-responsive">
                <table class="table mb-0">
                  <thead>
                    <tr>
                      <th style="width: 40%">Product</th>
                      <th style="width: 15%">Quantity</th>
                      <th style="width: 15%">Unit</th>
                      <th style="width: 15%">Price</th>
                      <th style="width: 15%">Total</th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngIf="itemsFormArray.length === 0">
                      <td colspan="6" class="text-center py-3">
                        No items added. Click "Add Item" to add products to this order request.
                      </td>
                    </tr>
                    <ng-container formArrayName="items">
                      <tr *ngFor="let itemForm of itemsFormArray.controls; let i = index" [formGroupName]="i">
                        <td>
                          <select 
                            class="form-select" 
                            formControlName="productId"
                            [ngClass]="{'is-invalid': itemForm.get('productId')?.invalid && itemForm.get('productId')?.touched}"
                          >
                            <option value="">-- Select Product --</option>
                            <option *ngFor="let product of products" [value]="product.productId">
                              {{ product.name }}
                            </option>
                          </select>
                        </td>
                        <td>
                          <input 
                            type="number" 
                            class="form-control" 
                            formControlName="quantity"
                            min="0.01"
                            step="0.01"
                            [ngClass]="{'is-invalid': itemForm.get('quantity')?.invalid && itemForm.get('quantity')?.touched}"
                          >
                        </td>
                        <td>
                          <select 
                            class="form-select" 
                            formControlName="unitId"
                            [ngClass]="{'is-invalid': itemForm.get('unitId')?.invalid && itemForm.get('unitId')?.touched}"
                          >
                            <option value="">-- Select Unit --</option>
                            <option *ngFor="let product of products" [value]="product.unitId">
                              {{ product.unitName }}
                            </option>
                          </select>
                        </td>
                        <td>
                          <input 
                            type="number" 
                            class="form-control" 
                            formControlName="unitPrice"
                            min="0"
                            step="0.01"
                            [ngClass]="{'is-invalid': itemForm.get('unitPrice')?.invalid && itemForm.get('unitPrice')?.touched}"
                          >
                        </td>
                        <td>
                          <input 
                            type="text" 
                            class="form-control" 
                            formControlName="lineTotal"
                            readonly
                          >
                        </td>
                        <td class="text-center">
                          <button type="button" class="btn btn-sm btn-outline-danger" (click)="removeItem(i)">
                            <i class="bi bi-trash"></i>
                          </button>
                        </td>
                      </tr>
                    </ng-container>
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colspan="4" class="text-end fw-bold">Total:</td>
                      <td>{{ calculateTotal() | currency }}</td>
                      <td></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">Actions</h5>
            </div>
            <div class="card-body">
              <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary" [disabled]="isSubmitting">
                  <i class="bi" [ngClass]="isEditMode ? 'bi-save' : 'bi-plus-circle'"></i>
                  {{ isEditMode ? 'Update Order Request' : 'Create Order Request' }}
                </button>
                <a routerLink="/order-requests" class="btn btn-outline-secondary">
                  <i class="bi bi-x-circle me-2"></i>Cancel
                </a>
              </div>
            </div>
          </div>
          
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">Help</h5>
            </div>
            <div class="card-body">
              <p class="mb-2">
                <i class="bi bi-info-circle me-2 text-primary"></i>
                <strong>Order Request</strong> is used to request items from another cost center or supplier.
              </p>
              <p class="mb-2">
                <i class="bi bi-info-circle me-2 text-primary"></i>
                <strong>Source</strong> is the cost center that will fulfill the request (optional).
              </p>
              <p class="mb-2">
                <i class="bi bi-info-circle me-2 text-primary"></i>
                <strong>Destination</strong> is the cost center that is requesting the items.
              </p>
              <p class="mb-0">
                <i class="bi bi-info-circle me-2 text-primary"></i>
                Add items by clicking the <strong>Add Item</strong> button.
              </p>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
