using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("StockTakeHeader")]
    public class StockTakeHeader
    {
        [Key]
        [Column("StockTakeId")]
        public int StockTakeId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("StockTakeNumber")]
        public string StockTakeNumber { get; set; }

        [Column("CostCenterId")]
        public int CostCenterId { get; set; }

        [Column("FiscalYearId")]
        public int FiscalYearId { get; set; }

        [Column("PeriodId")]
        public int? PeriodId { get; set; }

        [Column("StockTakeDate")]
        public DateTime StockTakeDate { get; set; }

        [Column("Notes")]
        public string Notes { get; set; }

        [Required]
        [StringLength(20)]
        [Column("Status")]
        public string Status { get; set; }

        [Column("TotalVariance")]
        public decimal? TotalVariance { get; set; }

        [Column("StockTakeTypeId")]
        public int? StockTakeTypeId { get; set; }

        [Column("CreatedById")]
        public int CreatedById { get; set; }

        [Column("CompletedById")]
        public int? CompletedById { get; set; }

        [Column("CompletedDate")]
        public DateTime? CompletedDate { get; set; }

        [Column("IsFinalized")]
        public bool IsFinalized { get; set; }

        [Column("FinalizedById")]
        public int? FinalizedById { get; set; }

        [Column("FinalizedDate")]
        public DateTime? FinalizedDate { get; set; }

        [Column("IsReopened")]
        public bool IsReopened { get; set; }

        [Column("ReopenedById")]
        public int? ReopenedById { get; set; }

        [Column("ReopenedDate")]
        public DateTime? ReopenedDate { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("CostCenterId")]
        public virtual CostCenter CostCenter { get; set; }

        [ForeignKey("FiscalYearId")]
        public virtual FiscalYear FiscalYear { get; set; }

        [ForeignKey("PeriodId")]
        public virtual Period Period { get; set; }

        [ForeignKey("StockTakeTypeId")]
        public virtual StockTakeType StockTakeType { get; set; }

        [ForeignKey("CreatedById")]
        public virtual User CreatedBy { get; set; }

        [ForeignKey("CompletedById")]
        public virtual User CompletedBy { get; set; }

        [ForeignKey("FinalizedById")]
        public virtual User FinalizedBy { get; set; }

        [ForeignKey("ReopenedById")]
        public virtual User ReopenedBy { get; set; }

        // Related collections
        public virtual ICollection<StockTakeDetail> StockTakeDetails { get; set; }
        public virtual ICollection<PeriodClose> PeriodCloses { get; set; }
    }
}
