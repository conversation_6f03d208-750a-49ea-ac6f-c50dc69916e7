$body = @{
    username = "admin"
    password = "12345"
} | ConvertTo-<PERSON>son

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5201/api/auth/login" -Method POST -Body $body -ContentType "application/json"
    Write-Host "Login successful!"
    Write-Host "Token: $($response.token)"
    Write-Host "User: $($response.username)"
    Write-Host "Role: $($response.role)"
} catch {
    Write-Host "Login failed: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
}
