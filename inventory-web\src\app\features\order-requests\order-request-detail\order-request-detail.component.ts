import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { OrderRequestDTO } from '../../../core/models/transaction.model';
import { OrderRequestService } from '../../../core/services/order-request.service';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-order-request-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, LoadingSpinnerComponent],
  templateUrl: './order-request-detail.component.html',
  styleUrls: ['./order-request-detail.component.scss']
})
export class OrderRequestDetailComponent implements OnInit {
  orderRequest?: OrderRequestDTO;
  isLoading = false;
  errorMessage = '';
  isSubmitting = false;
  
  constructor(
    private orderRequestService: OrderRequestService,
    private route: ActivatedRoute,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.loadOrderRequest(+params['id']);
      }
    });
  }

  loadOrderRequest(id: number): void {
    this.isLoading = true;
    this.orderRequestService.getOrderRequest(id).subscribe({
      next: (data) => {
        this.orderRequest = data;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading order request:', error);
        this.errorMessage = 'Failed to load order request. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'badge bg-secondary';
      case 'submitted':
        return 'badge bg-primary';
      case 'approved':
        return 'badge bg-success';
      case 'rejected':
        return 'badge bg-danger';
      case 'completed':
        return 'badge bg-info';
      default:
        return 'badge bg-secondary';
    }
  }

  submitOrderRequest(): void {
    if (!this.orderRequest?.transactionId) {
      return;
    }
    
    if (confirm('Are you sure you want to submit this order request?')) {
      this.isSubmitting = true;
      this.orderRequestService.submitOrderRequest(this.orderRequest.transactionId).subscribe({
        next: () => {
          this.isSubmitting = false;
          // Reload the order request to get the updated status
          this.loadOrderRequest(this.orderRequest!.transactionId!);
        },
        error: (error) => {
          console.error('Error submitting order request:', error);
          this.errorMessage = 'Failed to submit order request. Please try again later.';
          this.isSubmitting = false;
        }
      });
    }
  }

  deleteOrderRequest(): void {
    if (!this.orderRequest?.transactionId) {
      return;
    }
    
    if (confirm('Are you sure you want to delete this order request?')) {
      this.isSubmitting = true;
      this.orderRequestService.deleteOrderRequest(this.orderRequest.transactionId).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.router.navigate(['/order-requests']);
        },
        error: (error) => {
          console.error('Error deleting order request:', error);
          this.errorMessage = 'Failed to delete order request. Please try again later.';
          this.isSubmitting = false;
        }
      });
    }
  }

  calculateTotal(): number {
    if (!this.orderRequest?.items) {
      return 0;
    }
    
    return this.orderRequest.items.reduce((total, item) => {
      return total + (item.quantity * item.unitPrice);
    }, 0);
  }
}
