using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("CostCenter")]
    public class CostCenter
    {
        [Key]
        [Column("CostCenterId")]
        public int CostCenterId { get; set; }

        [Required]
        [StringLength(150)]
        [Column("Name")]
        public string Name { get; set; }

        [Column("StoreId")]
        public int? StoreId { get; set; }

        [Column("TypeId")]
        public int? TypeId { get; set; }

        [StringLength(100)]
        [Column("TypeName")]
        public string TypeName { get; set; }

        [Column("AutoTransfer")]
        public bool AutoTransfer { get; set; }

        [Column("IsSalesPoint")]
        public bool IsSalesPoint { get; set; }

        [StringLength(50)]
        [Column("Abbreviation")]
        public string Abbreviation { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("StoreId")]
        public virtual Store Store { get; set; }

        // Related collections
        public virtual ICollection<CostCenterFiscalYear> CostCenterFiscalYears { get; set; }
        public virtual ICollection<StockOnHand> StockOnHands { get; set; }
        public virtual ICollection<ProductCostCenterLink> ProductCostCenterLinks { get; set; }
        public virtual ICollection<Batch> Batches { get; set; }
        public virtual ICollection<TransactionHeader> SourceTransactions { get; set; }
        public virtual ICollection<TransactionHeader> DestinationTransactions { get; set; }
        public virtual ICollection<StockTakeHeader> StockTakeHeaders { get; set; }
        public virtual ICollection<PeriodClose> PeriodCloses { get; set; }
        public virtual ICollection<UserCostCenterAccess> UserCostCenterAccesses { get; set; }
        public virtual ICollection<UserStockTakeAccess> UserStockTakeAccesses { get; set; }
    }
}
