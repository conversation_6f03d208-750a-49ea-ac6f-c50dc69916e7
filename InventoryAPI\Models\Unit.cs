using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("Unit")]
    public class Unit
    {
        [Key]
        [Column("UnitId")]
        public int UnitId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Name")]
        public string Name { get; set; }

        [StringLength(10)]
        [Column("Abbreviation")]
        public string Abbreviation { get; set; }

        [Column("UnitGroupId")]
        public int? UnitGroupId { get; set; }

        [Column("BaseConversionFactor")]
        public decimal BaseConversionFactor { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        public virtual ICollection<Product> Products { get; set; }
        public virtual ICollection<Product> ProductsWithSalesUnit { get; set; }
        public virtual ICollection<Recipe> Recipes { get; set; }
        public virtual ICollection<RecipeIngredient> RecipeIngredients { get; set; }
        public virtual ICollection<StockOnHand> StockOnHands { get; set; }
        public virtual ICollection<TransactionDetail> TransactionDetails { get; set; }
    }
}
