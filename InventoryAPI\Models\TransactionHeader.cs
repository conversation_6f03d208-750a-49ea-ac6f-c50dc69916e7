using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("TransactionHeader")]
    public class TransactionHeader
    {
        [Key]
        [Column("TransactionId")]
        public int TransactionId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("TransactionNumber")]
        public string TransactionNumber { get; set; }

        [Column("ProcessId")]
        public int? ProcessId { get; set; }

        [Column("StageTypeId")]
        public int? StageTypeId { get; set; }

        [Column("TransactionTypeId")]
        public int TransactionTypeId { get; set; }

        [Column("SourceCostCenterId")]
        public int? SourceCostCenterId { get; set; }

        [Column("DestinationCostCenterId")]
        public int? DestinationCostCenterId { get; set; }

        [Column("SupplierId")]
        public int? SupplierId { get; set; }

        [StringLength(50)]
        [Column("ReferenceNumber")]
        public string ReferenceNumber { get; set; }

        [Column("TransactionDate")]
        public DateTime TransactionDate { get; set; }

        [Column("Notes")]
        public string Notes { get; set; }

        [Column("SubTotal")]
        public decimal SubTotal { get; set; }

        [Column("TaxAmount")]
        public decimal TaxAmount { get; set; }

        [Column("TotalAmount")]
        public decimal TotalAmount { get; set; }

        [Column("DiscountAmount")]
        public decimal DiscountAmount { get; set; }

        [Column("DiscountPercentage")]
        public decimal DiscountPercentage { get; set; }

        [Required]
        [StringLength(20)]
        [Column("Status")]
        public string Status { get; set; }

        [Column("CreatedById")]
        public int CreatedById { get; set; }

        [Column("ApprovedById")]
        public int? ApprovedById { get; set; }

        [Column("ApprovedDate")]
        public DateTime? ApprovedDate { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("ProcessId")]
        public virtual TransactionProcess TransactionProcess { get; set; }

        [ForeignKey("StageTypeId")]
        public virtual TransactionStageType StageType { get; set; }

        [ForeignKey("TransactionTypeId")]
        public virtual TransactionType TransactionType { get; set; }

        [ForeignKey("SourceCostCenterId")]
        public virtual CostCenter SourceCostCenter { get; set; }

        [ForeignKey("DestinationCostCenterId")]
        public virtual CostCenter DestinationCostCenter { get; set; }

        [ForeignKey("SupplierId")]
        public virtual Supplier Supplier { get; set; }

        [ForeignKey("CreatedById")]
        public virtual User CreatedBy { get; set; }

        [ForeignKey("ApprovedById")]
        public virtual User ApprovedBy { get; set; }

        // Related collections
        public virtual ICollection<TransactionDetail> TransactionDetails { get; set; }
        public virtual ICollection<Payment> Payments { get; set; }
    }
}
