using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace InventoryAPI.Models
{
    [Table("PeriodClose")]
    public class PeriodClose
    {
        [Key]
        [Column("PeriodCloseId")]
        public int PeriodCloseId { get; set; }

        [Column("PeriodId")]
        public int PeriodId { get; set; }

        [Column("FiscalYearId")]
        public int FiscalYearId { get; set; }

        [Column("ProductId")]
        public int ProductId { get; set; }

        [Column("CostCenterId")]
        public int CostCenterId { get; set; }

        [Column("OpeningQuantity")]
        [Precision(18, 2)]
        public decimal OpeningQuantity { get; set; }

        [Column("ClosingQuantity")]
        [Precision(18, 2)]
        public decimal ClosingQuantity { get; set; }

        [Column("OpeningValue")]
        [Precision(18, 2)]
        public decimal OpeningValue { get; set; }

        [Column("ClosingValue")]
        [Precision(18, 2)]
        public decimal ClosingValue { get; set; }

        [Column("AverageCost")]
        [Precision(18, 2)]
        public decimal? AverageCost { get; set; }

        [Column("CostPrice")]
        [Precision(18, 2)]
        public decimal? CostPrice { get; set; }

        [Column("StockTakeId")]
        public int? StockTakeId { get; set; }

        [Column("ClosedDate")]
        public DateTime ClosedDate { get; set; }

        [Column("ClosedById")]
        public int ClosedById { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("PeriodId")]
        public virtual Period Period { get; set; }

        [ForeignKey("FiscalYearId")]
        public virtual FiscalYear FiscalYear { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("CostCenterId")]
        public virtual CostCenter CostCenter { get; set; }

        [ForeignKey("StockTakeId")]
        public virtual StockTakeHeader StockTake { get; set; }

        [ForeignKey("ClosedById")]
        public virtual User ClosedBy { get; set; }
    }
}
