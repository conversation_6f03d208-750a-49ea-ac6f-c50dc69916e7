using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("TransactionDetail")]
    public class TransactionDetail
    {
        [Key]
        [Column("TransactionDetailId")]
        public int TransactionDetailId { get; set; }

        [Column("TransactionId")]
        public int TransactionId { get; set; }

        [Column("ProductId")]
        public int ProductId { get; set; }

        [Column("BatchId")]
        public int? BatchId { get; set; }

        [Column("Quantity")]
        public decimal Quantity { get; set; }

        [Column("UnitId")]
        public int UnitId { get; set; }

        [Column("UnitPrice")]
        public decimal UnitPrice { get; set; }

        [Column("TaxId")]
        public int? TaxId { get; set; }

        [Column("TaxRate")]
        public decimal? TaxRate { get; set; }

        [Column("TaxAmount")]
        public decimal? TaxAmount { get; set; }

        [Column("DiscountAmount")]
        public decimal? DiscountAmount { get; set; }

        [Column("DiscountPercentage")]
        public decimal? DiscountPercentage { get; set; }

        [Column("LineTotal")]
        public decimal LineTotal { get; set; }

        [Column("Notes")]
        public string Notes { get; set; }

        [Column("IsRecipe")]
        public bool IsRecipe { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("TransactionId")]
        public virtual TransactionHeader TransactionHeader { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("BatchId")]
        public virtual Batch Batch { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; }

        [ForeignKey("TaxId")]
        public virtual Tax Tax { get; set; }
    }
}
