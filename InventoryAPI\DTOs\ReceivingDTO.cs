using System;
using System.Collections.Generic;

namespace InventoryAPI.DTOs
{
    public class ReceivingDTO
    {
        public int TransactionId { get; set; }
        public string TransactionNumber { get; set; }
        public DateTime TransactionDate { get; set; }
        public int? SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string ReferenceNumber { get; set; }
        public int? DestinationCostCenterId { get; set; }
        public string DestinationCostCenterName { get; set; }
        public string Notes { get; set; }
        public string Status { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal TotalAmount { get; set; }
        public List<ReceivingItemDTO> Items { get; set; } = new List<ReceivingItemDTO>();
    }

    public class ReceivingItemDTO
    {
        public int TransactionDetailId { get; set; }
        public int ProductId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public int? BatchId { get; set; }
        public string BatchNumber { get; set; }
        public decimal OrderQuantity { get; set; }
        public decimal ReceivedQuantity { get; set; }
        public int UnitId { get; set; }
        public string UnitName { get; set; }
        public decimal UnitPrice { get; set; }
        public int? TaxId { get; set; }
        public decimal? TaxRate { get; set; }
        public decimal? TaxAmount { get; set; }
        public decimal? DiscountAmount { get; set; }
        public decimal? DiscountPercentage { get; set; }
        public decimal LineTotal { get; set; }
    }
}
