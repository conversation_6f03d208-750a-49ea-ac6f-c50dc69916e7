using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("Payment")]
    public class Payment
    {
        [Key]
        [Column("PaymentId")]
        public int PaymentId { get; set; }

        [Column("TransactionId")]
        public int TransactionId { get; set; }

        [Column("PaymentMethodId")]
        public int PaymentMethodId { get; set; }

        [Column("Amount")]
        public decimal Amount { get; set; }

        [Column("ChangeAmount")]
        public decimal? ChangeAmount { get; set; }

        [Column("PointsUsed")]
        public decimal? PointsUsed { get; set; }

        [Column("PointsRate")]
        public decimal? PointsRate { get; set; }

        [Column("Notes")]
        public string Notes { get; set; }

        [Column("PaymentDate")]
        public DateTime PaymentDate { get; set; }

        [Column("CreatedById")]
        public int CreatedById { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        [Column("CurrencyId")]
        public int? CurrencyId { get; set; }

        [Column("ShiftId")]
        public int? ShiftId { get; set; }

        // Navigation properties
        [ForeignKey("TransactionId")]
        public virtual TransactionHeader Transaction { get; set; }

        [ForeignKey("PaymentMethodId")]
        public virtual PaymentMethod PaymentMethod { get; set; }

        [ForeignKey("CreatedById")]
        public virtual User CreatedBy { get; set; }

        [ForeignKey("CurrencyId")]
        public virtual Currency Currency { get; set; }

        [ForeignKey("ShiftId")]
        public virtual Shift Shift { get; set; }
    }
}
