import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlaceholderComponent } from '../../../shared/components/placeholder/placeholder.component';

@Component({
  selector: 'app-cost-center-list',
  standalone: true,
  imports: [CommonModule, PlaceholderComponent],
  template: `
    <app-placeholder 
      title="Cost Centers Management" 
      message="The cost centers management feature is coming soon. This will allow you to manage your cost centers."
      icon="bi-building"
      backLink="/dashboard"
      backLabel="Back to Dashboard">
    </app-placeholder>
  `
})
export class CostCenterListComponent {}
