using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("Supplier")]
    public class Supplier
    {
        [Key]
        [Column("SupplierId")]
        public int SupplierId { get; set; }

        [Required]
        [StringLength(150)]
        [Column("Name")]
        public string Name { get; set; }

        [StringLength(150)]
        [Column("ContactPerson")]
        public string ContactPerson { get; set; }

        [StringLength(150)]
        [Column("Email")]
        public string Email { get; set; }

        [StringLength(50)]
        [Column("Phone")]
        public string Phone { get; set; }

        [StringLength(255)]
        [Column("Address")]
        public string Address { get; set; }

        [StringLength(100)]
        [Column("City")]
        public string City { get; set; }

        [StringLength(100)]
        [Column("State")]
        public string State { get; set; }

        [StringLength(100)]
        [Column("Country")]
        public string Country { get; set; }

        [StringLength(20)]
        [Column("PostalCode")]
        public string PostalCode { get; set; }

        [StringLength(50)]
        [Column("TaxNumber")]
        public string TaxNumber { get; set; }

        [Column("Notes")]
        public string Notes { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Related collections
        public virtual ICollection<TransactionHeader> TransactionHeaders { get; set; }
    }
}
