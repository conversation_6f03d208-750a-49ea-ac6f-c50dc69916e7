[{"ContainingType": "InventoryAPI.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/Login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "login", "Type": "InventoryAPI.Controllers.AuthController+LoginModel", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Controllers.AuthController+LoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.CostCentersController", "Method": "GetCostCenters", "RelativePath": "api/CostCenters", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[InventoryAPI.Models.CostCenter, InventoryAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.CostCentersController", "Method": "CreateCostCenter", "RelativePath": "api/CostCenters", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "costCenter", "Type": "InventoryAPI.Models.CostCenter", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.CostCenter", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.CostCentersController", "Method": "GetCostCenter", "RelativePath": "api/CostCenters/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.CostCenter", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.CostCentersController", "Method": "UpdateCostCenter", "RelativePath": "api/CostCenters/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "costCenter", "Type": "InventoryAPI.Models.CostCenter", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.CostCentersController", "Method": "DeleteCostCenter", "RelativePath": "api/CostCenters/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.CostCentersController", "Method": "GetCostCentersByStore", "RelativePath": "api/CostCenters/ByStore/{storeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "storeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[InventoryAPI.Models.CostCenter, InventoryAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.DepartmentsController", "Method": "GetDepartments", "RelativePath": "api/Departments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[InventoryAPI.Models.Department, InventoryAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.DepartmentsController", "Method": "PostDepartment", "RelativePath": "api/Departments", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "department", "Type": "InventoryAPI.Models.Department", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.Department", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.DepartmentsController", "Method": "GetDepartment", "RelativePath": "api/Departments/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.Department", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.DepartmentsController", "Method": "PutDepartment", "RelativePath": "api/Departments/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "department", "Type": "InventoryAPI.Models.Department", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.DepartmentsController", "Method": "DeleteDepartment", "RelativePath": "api/Departments/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.OrderRequestController", "Method": "GetOrderRequests", "RelativePath": "api/OrderRequest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[InventoryAPI.Models.TransactionHeader, InventoryAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.OrderRequestController", "Method": "CreateOrderRequest", "RelativePath": "api/OrderRequest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orderRequestDTO", "Type": "InventoryAPI.DTOs.OrderRequestDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.TransactionHeader", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.OrderRequestController", "Method": "GetOrderRequest", "RelativePath": "api/OrderRequest/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.DTOs.OrderRequestDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.OrderRequestController", "Method": "UpdateOrderRequest", "RelativePath": "api/OrderRequest/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "orderRequestDTO", "Type": "InventoryAPI.DTOs.OrderRequestDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.OrderRequestController", "Method": "DeleteOrderRequest", "RelativePath": "api/OrderRequest/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.OrderRequestController", "Method": "SubmitOrderRequest", "RelativePath": "api/OrderRequest/{id}/Submit", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.ProductsController", "Method": "GetProducts", "RelativePath": "api/Products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[InventoryAPI.Models.Product, InventoryAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.ProductsController", "Method": "PostProduct", "RelativePath": "api/Products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "product", "Type": "InventoryAPI.Models.Product", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.Product", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.ProductsController", "Method": "GetProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.Product", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.ProductsController", "Method": "PutProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "product", "Type": "InventoryAPI.Models.Product", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.ProductsController", "Method": "DeleteProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.ReceivingController", "Method": "GetReceivings", "RelativePath": "api/Receiving", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[InventoryAPI.Models.TransactionHeader, InventoryAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.ReceivingController", "Method": "CreateReceiving", "RelativePath": "api/Receiving", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "receivingDTO", "Type": "InventoryAPI.DTOs.ReceivingDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.TransactionHeader", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.ReceivingController", "Method": "GetReceiving", "RelativePath": "api/Receiving/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.DTOs.ReceivingDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.ReceivingController", "Method": "UpdateReceiving", "RelativePath": "api/Receiving/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "receivingDTO", "Type": "InventoryAPI.DTOs.ReceivingDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.ReceivingController", "Method": "DeleteReceiving", "RelativePath": "api/Receiving/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.SuppliersController", "Method": "GetSuppliers", "RelativePath": "api/Suppliers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[InventoryAPI.Models.Supplier, InventoryAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.SuppliersController", "Method": "CreateSupplier", "RelativePath": "api/Suppliers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "supplier", "Type": "InventoryAPI.Models.Supplier", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.Supplier", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.SuppliersController", "Method": "GetSupplier", "RelativePath": "api/Suppliers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.Supplier", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.SuppliersController", "Method": "UpdateSupplier", "RelativePath": "api/Suppliers/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "supplier", "Type": "InventoryAPI.Models.Supplier", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.SuppliersController", "Method": "DeleteSupplier", "RelativePath": "api/Suppliers/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.TransfersController", "Method": "GetTransfers", "RelativePath": "api/Transfers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[InventoryAPI.Models.TransactionHeader, InventoryAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.TransfersController", "Method": "CreateTransfer", "RelativePath": "api/Transfers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "transferDTO", "Type": "InventoryAPI.DTOs.TransferDTO", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.Models.TransactionHeader", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.TransfersController", "Method": "GetTransfer", "RelativePath": "api/Transfers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "InventoryAPI.DTOs.TransferDTO", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "InventoryAPI.Controllers.TransfersController", "Method": "UpdateTransfer", "RelativePath": "api/Transfers/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "transferDTO", "Type": "InventoryAPI.DTOs.TransferDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "InventoryAPI.Controllers.TransfersController", "Method": "DeleteTransfer", "RelativePath": "api/Transfers/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}]