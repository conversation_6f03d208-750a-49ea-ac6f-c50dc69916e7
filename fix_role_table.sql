-- Sc<PERSON>t to add missing columns to the Role table to match the C# model

USE InventoryManagement;
GO

-- Add missing columns to Role table
ALTER TABLE [Role] ADD 
    [IsAdmin] bit NOT NULL DEFAULT 0,
    [IsSystem] bit NOT NULL DEFAULT 0,
    
    -- Cost Center Permissions
    [DefaultCanViewCostCenters] bit NOT NULL DEFAULT 0,
    [DefaultCanCreateCostCenters] bit NOT NULL DEFAULT 0,
    [DefaultCanEditCostCenters] bit NOT NULL DEFAULT 0,
    [DefaultCanDeleteCostCenters] bit NOT NULL DEFAULT 0,
    [DefaultCanApproveCostCenters] bit NOT NULL DEFAULT 0,
    
    -- Transaction Permissions
    [DefaultCanViewTransactions] bit NOT NULL DEFAULT 0,
    [DefaultCanCreateTransactions] bit NOT NULL DEFAULT 0,
    [DefaultCanEditTransactions] bit NOT NULL DEFAULT 0,
    [DefaultCanDeleteTransactions] bit NOT NULL DEFAULT 0,
    [DefaultCanApproveTransactions] bit NOT NULL DEFAULT 0,
    [DefaultCanRejectTransactions] bit NOT NULL DEFAULT 0,
    [DefaultCanProcessTransactions] bit NOT NULL DEFAULT 0,
    [DefaultMaxApprovalAmount] decimal(18,2) NULL,
    
    -- Stock Take Permissions
    [DefaultCanViewStockTakes] bit NOT NULL DEFAULT 0,
    [DefaultCanCreateStockTakes] bit NOT NULL DEFAULT 0,
    [DefaultCanEditStockTakes] bit NOT NULL DEFAULT 0,
    [DefaultCanDeleteStockTakes] bit NOT NULL DEFAULT 0,
    [DefaultCanCountStockTakes] bit NOT NULL DEFAULT 0,
    [DefaultCanFinalizeStockTakes] bit NOT NULL DEFAULT 0,
    [DefaultCanReopenStockTakes] bit NOT NULL DEFAULT 0,
    
    -- Report Permissions
    [DefaultCanViewReports] bit NOT NULL DEFAULT 0,
    [DefaultCanExportReports] bit NOT NULL DEFAULT 0,
    [DefaultCanScheduleReports] bit NOT NULL DEFAULT 0;
GO

-- Update existing roles with appropriate permissions
-- Update User role (RoleId = 1)
UPDATE [Role] 
SET 
    [IsAdmin] = 0,
    [IsSystem] = 0,
    [DefaultCanViewCostCenters] = 1,
    [DefaultCanViewTransactions] = 1,
    [DefaultCanViewStockTakes] = 1,
    [DefaultCanViewReports] = 1
WHERE [RoleId] = 1;

-- Update Admin role (RoleId = 2)
UPDATE [Role] 
SET 
    [IsAdmin] = 1,
    [IsSystem] = 0,
    [DefaultCanViewCostCenters] = 1,
    [DefaultCanCreateCostCenters] = 1,
    [DefaultCanEditCostCenters] = 1,
    [DefaultCanDeleteCostCenters] = 1,
    [DefaultCanApproveCostCenters] = 1,
    [DefaultCanViewTransactions] = 1,
    [DefaultCanCreateTransactions] = 1,
    [DefaultCanEditTransactions] = 1,
    [DefaultCanDeleteTransactions] = 1,
    [DefaultCanApproveTransactions] = 1,
    [DefaultCanRejectTransactions] = 1,
    [DefaultCanProcessTransactions] = 1,
    [DefaultCanViewStockTakes] = 1,
    [DefaultCanCreateStockTakes] = 1,
    [DefaultCanEditStockTakes] = 1,
    [DefaultCanDeleteStockTakes] = 1,
    [DefaultCanCountStockTakes] = 1,
    [DefaultCanFinalizeStockTakes] = 1,
    [DefaultCanReopenStockTakes] = 1,
    [DefaultCanViewReports] = 1,
    [DefaultCanExportReports] = 1,
    [DefaultCanScheduleReports] = 1
WHERE [RoleId] = 2;

-- Update InventoryManager role (RoleId = 1002)
UPDATE [Role] 
SET 
    [IsAdmin] = 0,
    [IsSystem] = 0,
    [DefaultCanViewCostCenters] = 1,
    [DefaultCanCreateCostCenters] = 1,
    [DefaultCanEditCostCenters] = 1,
    [DefaultCanViewTransactions] = 1,
    [DefaultCanCreateTransactions] = 1,
    [DefaultCanEditTransactions] = 1,
    [DefaultCanApproveTransactions] = 1,
    [DefaultCanProcessTransactions] = 1,
    [DefaultCanViewStockTakes] = 1,
    [DefaultCanCreateStockTakes] = 1,
    [DefaultCanEditStockTakes] = 1,
    [DefaultCanCountStockTakes] = 1,
    [DefaultCanFinalizeStockTakes] = 1,
    [DefaultCanViewReports] = 1,
    [DefaultCanExportReports] = 1
WHERE [RoleId] = 1002;

-- Verify the changes
SELECT 
    RoleId,
    Name,
    IsAdmin,
    IsSystem,
    DefaultCanViewCostCenters,
    DefaultCanCreateCostCenters,
    DefaultCanViewTransactions,
    DefaultCanCreateTransactions,
    DefaultCanViewStockTakes,
    DefaultCanViewReports
FROM [Role];

PRINT 'Role table has been updated successfully!';
GO
