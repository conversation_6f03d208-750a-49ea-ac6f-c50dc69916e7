using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace InventoryAPI.Models
{
    [Table("RecipeIngredients")]
    public class RecipeIngredient
    {
        [Key]
        [Column("RecipeIngredientId")]
        public int RecipeIngredientId { get; set; }

        [Column("RecipeId")]
        public int RecipeId { get; set; }

        [Column("ProductId")]
        public int ProductId { get; set; }

        [Column("Quantity")]
        [Precision(18, 4)]
        public decimal Quantity { get; set; }

        [Column("UnitId")]
        public int UnitId { get; set; }

        [Column("Cost")]
        [Precision(18, 4)]
        public decimal Cost { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        [ForeignKey("RecipeId")]
        public virtual Recipe Recipe { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; }
    }
}
