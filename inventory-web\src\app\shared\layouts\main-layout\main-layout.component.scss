.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-container {
  display: flex;
  flex: 1;
}

.sidebar-container {
  width: 250px;
  transition: width 0.3s;
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 1.5rem;
  background-color: #f8f9fa;
}

.sidebar-collapsed {
  .sidebar-container {
    width: 0;
    overflow: hidden;
  }
}

@media (max-width: 768px) {
  .sidebar-container {
    position: fixed;
    height: 100%;
    z-index: 1000;
  }
  
  .content-container {
    margin-left: 0;
  }
}
