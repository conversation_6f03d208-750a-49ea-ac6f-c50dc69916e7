<div class="dashboard-container">
  <div class="page-header">
    <h1>Dashboard</h1>
    <p class="text-muted">Welcome to the Inventory Management System</p>
  </div>
  
  <div class="row">
    <div class="col-md-3 mb-4" *ngFor="let card of cards">
      <div class="card dashboard-card" [routerLink]="card.route">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h5 class="card-title">{{ card.title }}</h5>
              <h2 class="card-value">{{ card.value }}</h2>
            </div>
            <div class="card-icon" [ngClass]="'bg-' + card.color">
              <i class="bi" [ngClass]="card.icon"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="row mt-4">
    <div class="col-md-8 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Recent Transactions</h5>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead>
                <tr>
                  <th>Number</th>
                  <th>Type</th>
                  <th>Date</th>
                  <th>Status</th>
                  <th class="text-end">Amount</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let transaction of recentTransactions" class="cursor-pointer">
                  <td>{{ transaction.number }}</td>
                  <td>{{ transaction.type }}</td>
                  <td>{{ transaction.date | date:'MMM d, y' }}</td>
                  <td><span [ngClass]="getStatusClass(transaction.status)">{{ transaction.status }}</span></td>
                  <td class="text-end">{{ transaction.amount | currency }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="card-footer text-center">
          <a href="#" class="text-decoration-none">View All Transactions</a>
        </div>
      </div>
    </div>
    
    <div class="col-md-4 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Quick Actions</h5>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a routerLink="/order-requests/new" class="btn btn-outline-primary">
              <i class="bi bi-cart-plus me-2"></i>New Order Request
            </a>
            <a routerLink="/receiving/new" class="btn btn-outline-success">
              <i class="bi bi-truck me-2"></i>New Receiving
            </a>
            <a routerLink="/transfers/new" class="btn btn-outline-info">
              <i class="bi bi-arrow-repeat me-2"></i>New Transfer
            </a>
            <a routerLink="/products/new" class="btn btn-outline-secondary">
              <i class="bi bi-plus-circle me-2"></i>New Product
            </a>
          </div>
        </div>
      </div>
      
      <div class="card mt-4">
        <div class="card-header">
          <h5 class="mb-0">System Info</h5>
        </div>
        <div class="card-body">
          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span>Version</span>
              <span class="badge bg-primary">1.0.0</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span>Last Sync</span>
              <span>Today, 10:30 AM</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span>Database</span>
              <span class="badge bg-success">Connected</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
