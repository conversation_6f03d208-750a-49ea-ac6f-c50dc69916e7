export interface CostCenter {
  costCenterId: number;
  name: string;
  storeId?: number;
  storeName?: string;
  typeId?: number;
  typeName?: string;
  autoTransfer: boolean;
  isSalesPoint: boolean;
  abbreviation?: string;
  createdAt: Date;
  updatedAt?: Date;
  isActive: boolean;
}

export interface Store {
  storeId: number;
  name: string;
  locationId?: number;
  locationName?: string;
  isSalesPoint: boolean;
  logoPath?: string;
  createdAt: Date;
  updatedAt?: Date;
  isActive: boolean;
}
