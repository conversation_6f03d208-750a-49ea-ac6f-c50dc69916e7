using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("UserStockTakeAccess")]
    public class UserStockTakeAccess
    {
        [Key]
        [Column("StockTakeAccessId")]
        public int StockTakeAccessId { get; set; }

        [Column("UserId")]
        public int UserId { get; set; }

        [Column("CostCenterId")]
        public int? CostCenterId { get; set; }

        [Column("CanView")]
        public bool CanView { get; set; }

        [Column("CanCreate")]
        public bool CanCreate { get; set; }

        [Column("CanEdit")]
        public bool CanEdit { get; set; }

        [Column("CanDelete")]
        public bool CanDelete { get; set; }

        [Column("CanCount")]
        public bool CanCount { get; set; }

        [Column("CanFinalize")]
        public bool CanFinalize { get; set; }

        [Column("CanReopen")]
        public bool CanReopen { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [ForeignKey("CostCenterId")]
        public virtual CostCenter CostCenter { get; set; }
    }
}
