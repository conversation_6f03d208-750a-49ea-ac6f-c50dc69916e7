using Microsoft.EntityFrameworkCore;
using InventoryAPI.Models;

namespace InventoryAPI.Data
{
    public class InventoryDbContext : DbContext
    {
        public InventoryDbContext(DbContextOptions<InventoryDbContext> options) : base(options)
        {
        }

        // Core tables
        public DbSet<Department> Departments { get; set; }
        public DbSet<ProductGroup> ProductGroups { get; set; }
        public DbSet<ProductSubGroup> ProductSubGroups { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<Brand> Brands { get; set; }
        public DbSet<Tax> Taxes { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Recipe> Recipes { get; set; }
        public DbSet<RecipeIngredient> RecipeIngredients { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<Store> Stores { get; set; }
        public DbSet<CostCenter> CostCenters { get; set; }

        // Inventory management tables
        public DbSet<FiscalYear> FiscalYears { get; set; }
        public DbSet<Period> Periods { get; set; }
        public DbSet<CostCenterFiscalYear> CostCenterFiscalYears { get; set; }
        public DbSet<StockTakeType> StockTakeTypes { get; set; }
        public DbSet<StockOnHand> StockOnHands { get; set; }
        public DbSet<ProductCostCenterLink> ProductCostCenterLinks { get; set; }
        public DbSet<Batch> Batches { get; set; }

        // Transaction tables
        public DbSet<TransactionType> TransactionTypes { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<TransactionProcess> TransactionProcesses { get; set; }
        public DbSet<TransactionStageType> TransactionStageTypes { get; set; }
        public DbSet<TransactionHeader> TransactionHeaders { get; set; }
        public DbSet<TransactionDetail> TransactionDetails { get; set; }
        public DbSet<StockTakeHeader> StockTakeHeaders { get; set; }
        public DbSet<StockTakeDetail> StockTakeDetails { get; set; }
        public DbSet<PeriodClose> PeriodCloses { get; set; }

        // User management tables
        public DbSet<Role> Roles { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<UserCostCenterAccess> UserCostCenterAccesses { get; set; }
        public DbSet<UserTransactionAccess> UserTransactionAccesses { get; set; }
        public DbSet<UserStockTakeAccess> UserStockTakeAccesses { get; set; }
        public DbSet<UserReportAccess> UserReportAccesses { get; set; }
        public DbSet<UserProductAccess> UserProductAccesses { get; set; }
        public DbSet<UserTransactionTypeAccess> UserTransactionTypeAccesses { get; set; }
        public DbSet<ApplicationForm> ApplicationForms { get; set; }
        public DbSet<FormAction> FormActions { get; set; }
        public DbSet<RoleFormActionAccess> RoleFormActionAccesses { get; set; }
        public DbSet<UserFormActionAccess> UserFormActionAccesses { get; set; }

        // Payment and sales tables
        public DbSet<PaymentMethod> PaymentMethods { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Currency> Currencies { get; set; }
        public DbSet<Shift> Shifts { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure entity relationships and constraints here

            // Configure CostCenter relationships
            modelBuilder.Entity<CostCenter>()
                .HasMany(c => c.SourceTransactions)
                .WithOne(t => t.SourceCostCenter)
                .HasForeignKey(t => t.SourceCostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<CostCenter>()
                .HasMany(c => c.DestinationTransactions)
                .WithOne(t => t.DestinationCostCenter)
                .HasForeignKey(t => t.DestinationCostCenterId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Product relationships with Unit
            modelBuilder.Entity<Product>()
                .HasOne(p => p.SalesUnit)
                .WithMany(u => u.ProductsWithSalesUnit)
                .HasForeignKey(p => p.SalesUnitId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure RecipeIngredient relationships to avoid cascade delete cycles
            modelBuilder.Entity<RecipeIngredient>()
                .HasOne(ri => ri.Recipe)
                .WithMany(r => r.RecipeIngredients)
                .HasForeignKey(ri => ri.RecipeId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<RecipeIngredient>()
                .HasOne(ri => ri.Product)
                .WithMany(p => p.RecipeIngredients)
                .HasForeignKey(ri => ri.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<RecipeIngredient>()
                .HasOne(ri => ri.Unit)
                .WithMany(u => u.RecipeIngredients)
                .HasForeignKey(ri => ri.UnitId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure Payment relationships to avoid cascade delete cycles
            modelBuilder.Entity<Payment>()
                .HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(p => p.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Payment>()
                .HasOne(p => p.Transaction)
                .WithMany()
                .HasForeignKey(p => p.TransactionId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure PeriodClose relationships to avoid cascade delete cycles
            modelBuilder.Entity<PeriodClose>()
                .HasOne(pc => pc.Period)
                .WithMany()
                .HasForeignKey(pc => pc.PeriodId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<PeriodClose>()
                .HasOne(pc => pc.FiscalYear)
                .WithMany()
                .HasForeignKey(pc => pc.FiscalYearId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<PeriodClose>()
                .HasOne(pc => pc.Product)
                .WithMany()
                .HasForeignKey(pc => pc.ProductId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<PeriodClose>()
                .HasOne(pc => pc.CostCenter)
                .WithMany()
                .HasForeignKey(pc => pc.CostCenterId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<PeriodClose>()
                .HasOne(pc => pc.ClosedBy)
                .WithMany()
                .HasForeignKey(pc => pc.ClosedById)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<PeriodClose>()
                .HasOne(pc => pc.StockTake)
                .WithMany()
                .HasForeignKey(pc => pc.StockTakeId)
                .OnDelete(DeleteBehavior.NoAction);
        }
    }
}
