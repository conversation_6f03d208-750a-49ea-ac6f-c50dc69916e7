using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using InventoryAPI.Data;
using InventoryAPI.Models;

namespace InventoryAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CostCentersController : ControllerBase
    {
        private readonly InventoryDbContext _context;

        public CostCentersController(InventoryDbContext context)
        {
            _context = context;
        }

        // GET: api/CostCenters
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CostCenter>>> GetCostCenters()
        {
            return await _context.CostCenters
                .Include(c => c.Store)
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        // GET: api/CostCenters/5
        [HttpGet("{id}")]
        public async Task<ActionResult<CostCenter>> GetCostCenter(int id)
        {
            var costCenter = await _context.CostCenters
                .Include(c => c.Store)
                .FirstOrDefaultAsync(c => c.CostCenterId == id && c.IsActive);

            if (costCenter == null)
            {
                return NotFound();
            }

            return costCenter;
        }

        // GET: api/CostCenters/ByStore/5
        [HttpGet("ByStore/{storeId}")]
        public async Task<ActionResult<IEnumerable<CostCenter>>> GetCostCentersByStore(int storeId)
        {
            return await _context.CostCenters
                .Include(c => c.Store)
                .Where(c => c.StoreId == storeId && c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        // POST: api/CostCenters
        [HttpPost]
        public async Task<ActionResult<CostCenter>> CreateCostCenter(CostCenter costCenter)
        {
            costCenter.CreatedAt = DateTime.UtcNow;
            costCenter.IsActive = true;
            
            _context.CostCenters.Add(costCenter);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetCostCenter), new { id = costCenter.CostCenterId }, costCenter);
        }

        // PUT: api/CostCenters/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCostCenter(int id, CostCenter costCenter)
        {
            if (id != costCenter.CostCenterId)
            {
                return BadRequest();
            }

            costCenter.UpdatedAt = DateTime.UtcNow;
            _context.Entry(costCenter).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!CostCenterExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/CostCenters/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCostCenter(int id)
        {
            var costCenter = await _context.CostCenters.FindAsync(id);
            if (costCenter == null)
            {
                return NotFound();
            }

            // Soft delete
            costCenter.IsActive = false;
            costCenter.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool CostCenterExists(int id)
        {
            return _context.CostCenters.Any(e => e.CostCenterId == id);
        }
    }
}
