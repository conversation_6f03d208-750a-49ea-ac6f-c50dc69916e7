using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("PaymentMethod")]
    public class PaymentMethod
    {
        [Key]
        [Column("PaymentMethodId")]
        public int PaymentMethodId { get; set; }

        [Required]
        [StringLength(150)]
        [Column("Name")]
        public string Name { get; set; }

        [StringLength(255)]
        [Column("Description")]
        public string Description { get; set; }

        [StringLength(50)]
        [Column("AccountNumber")]
        public string AccountNumber { get; set; }

        [Column("IsPointsSystem")]
        public bool IsPointsSystem { get; set; }

        [Column("PointsConversionRate")]
        public decimal? PointsConversionRate { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Related collections
        public virtual ICollection<Payment> Payments { get; set; }
    }
}
