using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("ProductSubGroup")]
    public class ProductSubGroup
    {
        [Key]
        [Column("SubGroupId")]
        public int SubGroupId { get; set; }

        [Required]
        [StringLength(150)]
        [Column("Name")]
        public string Name { get; set; }

        [Column("GroupId")]
        public int? GroupId { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Navigation properties
        [ForeignKey("GroupId")]
        public virtual ProductGroup ProductGroup { get; set; }
        
        public virtual ICollection<Product> Products { get; set; }
    }
}
