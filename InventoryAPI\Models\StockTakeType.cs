using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace InventoryAPI.Models
{
    [Table("StockTakeType")]
    public class StockTakeType
    {
        [Key]
        [Column("StockTakeTypeId")]
        public int StockTakeTypeId { get; set; }

        [Required]
        [StringLength(50)]
        [Column("Name")]
        public string Name { get; set; }

        [StringLength(255)]
        [Column("Description")]
        public string Description { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("UpdatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        // Related collections
        public virtual ICollection<StockTakeHeader> StockTakeHeaders { get; set; }
        public virtual ICollection<StockTakeDetail> StockTakeDetails { get; set; }
    }
}
